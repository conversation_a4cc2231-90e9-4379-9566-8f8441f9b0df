<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class BillingDocumentsByDailyCollectionExport implements
    FromCollection,
    WithHeadings,
    WithMapping,
    WithStyles,
    WithColumnWidths,
    WithTitle,
    WithEvents
{
    use Exportable, RegistersEventListeners;

    protected $data;
    public $total_amount;
    public $net_amount;
    protected $merge_cells = []; // Track cells to merge

    public function __construct($data, $total_amount = 0, $net_amount = 0)
    {
        $this->data = collect($data);
        $this->total_amount = $total_amount;
        $this->net_amount = $net_amount;
    }

    public function collection(): \Illuminate\Support\Collection
    {
        $export_data = collect();
        $invoice_number = 1;

        foreach ($this->data as $invoice) {
            $filtered_line_items = $invoice->lineItems->toArray();
            $line_items_count = count($filtered_line_items);
            $payments_count = count($invoice->payments);
            $row_count = max($line_items_count, $payments_count, 1);

            for ($i = 0; $i < $row_count; $i++) {
                $row = [];

                // First row of each invoice gets the main invoice data
                if ($i === 0) {
                    $row['no'] = $invoice_number;
                    $row['payment_date'] = \Carbon\Carbon::parse($invoice->paid_at)
                        ->tz(config('school.timezone'))
                        ->format('Y-m-d');
                    $row['invoice_date'] = $invoice->document_date;
                    $row['invoice_no'] = $invoice->reference_no;
                    $row['bill_to_name'] = $invoice->billTo->getTranslation('name', 'zh') ?? '-';
                    $row['bill_to_reference_no'] = $invoice->bill_to_reference_number;
                    $row['class'] = $invoice->class_name ?? '-';
                    $row['amount'] = number_format(
                        array_sum(array_column($filtered_line_items, 'amount_before_tax')),
                        2, '.', ''
                    );
                } else {
                    // Subsequent rows are empty for merged cells
                    $row['no'] = '';
                    $row['payment_date'] = '';
                    $row['invoice_date'] = '';
                    $row['invoice_no'] = '';
                    $row['bill_to_name'] = '';
                    $row['bill_to_reference_no'] = '';
                    $row['class'] = '';
                    $row['amount'] = '';
                }

                // Description (line items)
                $valid_line_items = array_values($filtered_line_items);
                $row['description'] = isset($valid_line_items[$i]) ? $valid_line_items[$i]['description'] : '';

                // Payment information
                if (isset($invoice->payments[$i])) {
                    $payment = $invoice->payments[$i];
                    $row['payment'] = $payment?->paymentMethod->code . ' (' . $payment->payment_reference_no . ')';

                    // Bank information
                    if (isset($payment->paymentSource?->bank)) {
                        $bank = $payment->paymentSource->bank;
                        $row['bank'] = $bank->name . ' (' . $bank->swift_code . ')';
                    } else {
                        $row['bank'] = '';
                    }
                } else {
                    $row['payment'] = '';
                    $row['bank'] = '';
                }

                $export_data->push($row);
            }

            $invoice_number++;
        }

        // Add total amount row
        $export_data->push([
            'no' => '',
            'payment_date' => '',
            'invoice_date' => '',
            'invoice_no' => '',
            'bill_to_name' => '',
            'bill_to_reference_no' => '',
            'class' => '',
            'description' => __('general.total_amount') . ' :',
            'amount' => number_format($this->total_amount, 2, '.', ''),
            'payment' => '',
            'bank' => ''
        ]);

        // Add net amount row if applicable
        if ($this->net_amount > 0) {
            $export_data->push([
                'no' => '',
                'payment_date' => '',
                'invoice_date' => '',
                'invoice_no' => '',
                'bill_to_name' => '',
                'bill_to_reference_no' => '',
                'class' => '',
                'description' => __('general.net_amount') . ' (After Payment Gateway Charges) :',
                'amount' => number_format($this->net_amount, 2, '.', ''),
                'payment' => '',
                'bank' => ''
            ]);
        }

        return $export_data;
    }

    public function headings(): array
    {
        return [
            __('general.no'),
            __('general.payment_date'),
            __('general.invoice_date'),
            __('general.invoice_no'),
            __('general.bill_to_name'),
            __('general.bill_to_reference_no'),
            __('general.class'),
            __('general.description'),
            __('general.amount') . ' (' . config('school.currency_code') . ')',
            __('general.payment'),
            __('general.bank'),
        ];
    }

    public function map($row): array
    {
        return [
            $row['no'],
            $row['payment_date'],
            $row['invoice_date'],
            $row['invoice_no'],
            $row['bill_to_name'],
            $row['bill_to_reference_no'],
            $row['class'],
            $row['description'],
            $row['amount'],
            $row['payment'],
            $row['bank'],
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Apply styles to header row
        $sheet->getStyle('A1:K1')->applyFromArray([
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ]);

        // Apply borders to all data
        $last_row = $sheet->getHighestRow();
        $sheet->getStyle("A1:K{$last_row}")->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'alignment' => [
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // Style total rows (last two rows if net amount exists, otherwise just last row)
        $total_row_start = $this->net_amount > 0 ? $last_row - 1 : $last_row;
        $sheet->getStyle("A{$total_row_start}:K{$last_row}")->applyFromArray([
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_RIGHT,
            ],
        ]);

        return [];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 8,   // No
            'B' => 15,  // Payment Date
            'C' => 15,  // Invoice Date
            'D' => 20,  // Invoice No
            'E' => 25,  // Bill To Name
            'F' => 20,  // Bill To Reference No
            'G' => 15,  // Class
            'H' => 30,  // Description
            'I' => 15,  // Amount
            'J' => 25,  // Payment
            'K' => 25,  // Bank
        ];
    }

    public function title(): string
    {
        return 'Daily Collection Report';
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $current_row = 2; // Start after header

                foreach ($this->data as $invoice) {
                    $filtered_line_items = $invoice->lineItems->toArray();
                    $line_items_count = count($filtered_line_items);
                    $payments_count = count($invoice->payments);
                    $row_count = max($line_items_count, $payments_count, 1);

                    if ($row_count > 1) {
                        $end_row = $current_row + $row_count - 1;

                        // Merge cells for columns that should span multiple rows
                        $columns_to_merge = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'I']; // No, Payment Date, Invoice Date, etc.

                        foreach ($columns_to_merge as $column) {
                            $event->sheet->mergeCells("{$column}{$current_row}:{$column}{$end_row}");
                        }
                    }

                    $current_row += $row_count;
                }
            },
        ];
    }
}
