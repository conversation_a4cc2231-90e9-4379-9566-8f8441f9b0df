<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Support\Collection;

class BillingDocumentsByDailyCollectionExport implements
    FromCollection,
    WithHeadings,
    WithMapping,
    WithStyles,
    WithColumnWidths,
    WithTitle,
    WithEvents
{
    protected $data;
    protected $totalAmount;
    protected $netAmount;
    protected $mergeCells = []; // Track cells to merge

    public function __construct($data, $totalAmount = 0, $netAmount = 0)
    {
        $this->data = collect($data);
        $this->totalAmount = $totalAmount;
        $this->netAmount = $netAmount;
    }

    public function collection()
    {
        $exportData = collect();
        $invoiceNumber = 1;

        foreach ($this->data as $invoice) {
            $filteredLineItems = $invoice->lineItems->toArray();
            $lineItemsCount = count($filteredLineItems);
            $paymentsCount = count($invoice->payments);
            $rowCount = max($lineItemsCount, $paymentsCount, 1);

            for ($i = 0; $i < $rowCount; $i++) {
                $row = [];

                // First row of each invoice gets the main invoice data
                if ($i === 0) {
                    $row['no'] = $invoiceNumber;
                    $row['payment_date'] = \Carbon\Carbon::parse($invoice->paid_at)
                        ->tz(config('school.timezone'))
                        ->format('Y-m-d');
                    $row['invoice_date'] = $invoice->document_date;
                    $row['invoice_no'] = $invoice->reference_no;
                    $row['bill_to_name'] = $invoice->billTo->getTranslation('name', 'zh') ?? '-';
                    $row['bill_to_reference_no'] = $invoice->bill_to_reference_number;
                    $row['class'] = $invoice->class_name ?? '-';
                    $row['amount'] = number_format(
                        array_sum(array_column($filteredLineItems, 'amount_before_tax')), 
                        2, '.', ''
                    );
                } else {
                    // Subsequent rows are empty for merged cells
                    $row['no'] = '';
                    $row['payment_date'] = '';
                    $row['invoice_date'] = '';
                    $row['invoice_no'] = '';
                    $row['bill_to_name'] = '';
                    $row['bill_to_reference_no'] = '';
                    $row['class'] = '';
                    $row['amount'] = '';
                }

                // Description (line items)
                $validLineItems = array_values($filteredLineItems);
                $row['description'] = isset($validLineItems[$i]) ? $validLineItems[$i]['description'] : '';

                // Payment information
                if (isset($invoice->payments[$i])) {
                    $payment = $invoice->payments[$i];
                    $row['payment'] = $payment?->paymentMethod->code . ' (' . $payment->payment_reference_no . ')';
                    
                    // Bank information
                    if (isset($payment->paymentSource?->bank)) {
                        $bank = $payment->paymentSource->bank;
                        $row['bank'] = $bank->name . ' (' . $bank->swift_code . ')';
                    } else {
                        $row['bank'] = '';
                    }
                } else {
                    $row['payment'] = '';
                    $row['bank'] = '';
                }

                $exportData->push($row);
            }

            $invoiceNumber++;
        }

        // Add total amount row
        $exportData->push([
            'no' => '',
            'payment_date' => '',
            'invoice_date' => '',
            'invoice_no' => '',
            'bill_to_name' => '',
            'bill_to_reference_no' => '',
            'class' => '',
            'description' => __('general.total_amount') . ' :',
            'amount' => number_format($this->totalAmount, 2, '.', ''),
            'payment' => '',
            'bank' => ''
        ]);

        // Add net amount row if applicable
        if ($this->netAmount > 0) {
            $exportData->push([
                'no' => '',
                'payment_date' => '',
                'invoice_date' => '',
                'invoice_no' => '',
                'bill_to_name' => '',
                'bill_to_reference_no' => '',
                'class' => '',
                'description' => __('general.net_amount') . ' (After Payment Gateway Charges) :',
                'amount' => number_format($this->netAmount, 2, '.', ''),
                'payment' => '',
                'bank' => ''
            ]);
        }

        return $exportData;
    }

    public function headings(): array
    {
        return [
            __('general.no'),
            __('general.payment_date'),
            __('general.invoice_date'),
            __('general.invoice_no'),
            __('general.bill_to_name'),
            __('general.bill_to_reference_no'),
            __('general.class'),
            __('general.description'),
            __('general.amount') . ' (' . config('school.currency_code') . ')',
            __('general.payment'),
            __('general.bank'),
        ];
    }

    public function map($row): array
    {
        return [
            $row['no'],
            $row['payment_date'],
            $row['invoice_date'],
            $row['invoice_no'],
            $row['bill_to_name'],
            $row['bill_to_reference_no'],
            $row['class'],
            $row['description'],
            $row['amount'],
            $row['payment'],
            $row['bank'],
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Apply styles to header row
        $sheet->getStyle('A1:K1')->applyFromArray([
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ]);

        // Apply borders to all data
        $lastRow = $sheet->getHighestRow();
        $sheet->getStyle("A1:K{$lastRow}")->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'alignment' => [
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // Style total rows (last two rows if net amount exists, otherwise just last row)
        $totalRowStart = $this->netAmount > 0 ? $lastRow - 1 : $lastRow;
        $sheet->getStyle("A{$totalRowStart}:K{$lastRow}")->applyFromArray([
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_RIGHT,
            ],
        ]);

        return [];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 8,   // No
            'B' => 15,  // Payment Date
            'C' => 15,  // Invoice Date
            'D' => 20,  // Invoice No
            'E' => 25,  // Bill To Name
            'F' => 20,  // Bill To Reference No
            'G' => 15,  // Class
            'H' => 30,  // Description
            'I' => 15,  // Amount
            'J' => 25,  // Payment
            'K' => 25,  // Bank
        ];
    }

    public function title(): string
    {
        return 'Daily Collection Report';
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->mergeCellsForInvoices($event->sheet);
            },
        ];
    }

    private function mergeCellsForInvoices(Worksheet $sheet)
    {
        $currentRow = 2; // Start after header
        $invoiceNumber = 1;

        foreach ($this->data as $invoice) {
            $filteredLineItems = $invoice->lineItems->toArray();
            $lineItemsCount = count($filteredLineItems);
            $paymentsCount = count($invoice->payments);
            $rowCount = max($lineItemsCount, $paymentsCount, 1);

            if ($rowCount > 1) {
                $endRow = $currentRow + $rowCount - 1;

                // Merge cells for columns that should span multiple rows
                $columnsToMerge = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'I']; // No, Payment Date, Invoice Date, etc.

                foreach ($columnsToMerge as $column) {
                    $sheet->mergeCells("{$column}{$currentRow}:{$column}{$endRow}");
                }
            }

            $currentRow += $rowCount;
            $invoiceNumber++;
        }
    }
}
