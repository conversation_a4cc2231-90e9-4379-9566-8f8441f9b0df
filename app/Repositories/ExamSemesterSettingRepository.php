<?php

namespace App\Repositories;

use App\Models\ExamSemesterSetting;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ExamSemesterSettingRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ExamSemesterSetting::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['category']), function (Builder $query) use ($filters) {
                $query->where('category', $filters['category']);
            })
            ->when(isset($filters['grade_id']), function (Builder $query) use ($filters) {
                $query->where('grade_id', $filters['grade_id']);
            })
            ->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
                $query->where('semester_setting_id', $filters['semester_setting_id']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
