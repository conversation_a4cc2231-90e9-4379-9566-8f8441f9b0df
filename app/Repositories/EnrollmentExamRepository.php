<?php

namespace App\Repositories;

use App\Models\EnrollmentExam;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class EnrollmentExamRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return EnrollmentExam::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['id']), function (Builder $query) use ($filters) {
                if (is_array($filters['id'])) {
                    $query->whereIn('id', $filters['id']);
                } else {
                    $query->where('id', $filters['id']);
                }
            })
            ->when(isset($filters['exam_slip_number']), function (Builder $query) use ($filters) {
                if (is_array($filters['exam_slip_number'])) {
                    $query->whereIn('exam_slip_number', $filters['exam_slip_number']);
                } else {
                    $query->where('exam_slip_number', $filters['exam_slip_number']);
                }
            })
            ->when(isset($filters['enrollment_session_id']), function (Builder $query) use ($filters) {
                $query->whereRelation('enrollment', 'enrollment_session_id', '=', $filters['enrollment_session_id']);
            })
        ;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
