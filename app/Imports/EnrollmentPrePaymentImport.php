<?php

namespace App\Imports;

use App\Models\EnrollmentSession;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStartRow;

class EnrollmentPrePaymentImport implements WithMapping, WithStartRow, SkipsEmptyRows
{
    public function __construct(
        protected EnrollmentSession $enrollmentSession,
    ) {
    }

    public function map($row): array
    {
        // trim whitespaces from all cells
        $row = array_map('trim', $row);

        $current_index = 0;

        $adjusted = [
            'number' => (string) $row[$current_index++],
            'exam_slip_number' => (string) $row[$current_index++],
            'student_name_en' => (string) $row[$current_index++],
            'student_name_zh' => (string) $row[$current_index++],
            'nric' => (string) $row[$current_index++],
            'passport_number' => (string) $row[$current_index++],
            'religion' => (string) $row[$current_index++],
            'gender' => (string) $row[$current_index++],
            'guardian_phone_number' => (string) $row[$current_index++],
            'guardian_email' => (string) $row[$current_index++],
            'guardian_name' => (string) $row[$current_index++],
            'guardian_type' => (string) $row[$current_index++],
            'total_average' => (string) $row[$current_index++],
        ];

        $enrollment_session = $this->enrollmentSession->loadMissing(['examSubjects']);

        // Add subject fields
        foreach ($enrollment_session->getExamSubjectsSortedBySubjectCode() as $subject) {
            $adjusted[$subject->code] = (string) $row[$current_index++];
        }

        $adjusted['status'] = (string) $row[$current_index++];
        $adjusted['address'] = (string) $row[$current_index++];
        $adjusted['primary_school'] = (string) $row[$current_index++];
        $adjusted['hostel'] = $this->convertToBoolean($row[$current_index++]);
        $adjusted['have_siblings'] = $this->convertToBoolean($row[$current_index++]);
        $adjusted['dietary_restriction'] = (string) $row[$current_index++];
        $adjusted['health_concern'] = (string) $row[$current_index++];
        $adjusted['foreigner'] = $this->convertToBoolean($row[$current_index++]);
        $adjusted['conduct'] = (string) $row[$current_index++];
        $adjusted['remarks'] = (string) $row[$current_index++];

        return $adjusted;
    }

    public function startRow(): int
    {
        return 2;
    }

    /**
     * Convert Excel value to boolean.
     */
    private function convertToBoolean($value): mixed
    {
        $truthy['1'] = true;
        $truthy['true'] = true;
        $truthy['TRUE'] = true;
        $truthy['yes'] = true;
        $truthy['on'] = true;

        $falsy['0'] = true;
        $falsy['false'] = true;
        $falsy['FALSE'] = true;
        $falsy['no'] = true;
        $falsy['off'] = true;
        $falsy[''] = true;

        $value = strtolower(trim((string) $value));

        if (isset($truthy[$value])) {
            return true;
        } elseif (isset($falsy[$value])) {
            return false;
        } else {
            return null;
        }
    }
}
