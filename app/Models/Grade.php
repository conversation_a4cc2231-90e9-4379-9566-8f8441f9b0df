<?php

namespace App\Models;

use App\Traits\Models\HasTranslations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class Grade extends Model implements Auditable
{
    use HasFactory, HasTranslations, \OwenIt\Auditing\Auditable;

    public $translatable = ['name'];
    protected $table = 'master_grades';
    protected $guarded = ['id'];

    public function classes(): HasMany
    {
        return $this->hasMany(ClassModel::class);
    }
}
