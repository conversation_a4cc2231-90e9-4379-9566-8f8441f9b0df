<?php

namespace App\Models;

use App\Interfaces\Deletable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use OwenIt\Auditing\Contracts\Auditable;

class SemesterClass extends Model implements Deletable, Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    protected $guarded = ['id'];

    public function semesterSetting(): BelongsTo
    {
        return $this->belongsTo(SemesterSetting::class);
    }

    public function classModel(): BelongsTo
    {
        return $this->belongsTo(ClassModel::class, 'class_id');
    }

    public function homeroomTeacher(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'homeroom_teacher_id');
    }

    public function studentClasses(): HasMany
    {
        return $this->hasMany(StudentClass::class);
    }

    public function activeStudentClasses(): HasMany
    {
        return $this->hasMany(StudentClass::class)->where('is_active', true);
    }

    public function students()
    {
        return $this->belongsToMany(Student::class, 'student_classes');
    }

    public function classSubjects(): HasMany
    {
        return $this->hasMany(ClassSubject::class, 'semester_class_id');
    }

    public function defaultGradingFramework()
    {
        return $this->belongsTo(GradingFramework::class, 'default_grading_framework_id');
    }

    public function canBeDeleted(): bool
    {
        if ($this->studentClasses->count()) {
            return false;
        }

        if ($this->classSubjects->count()) {
            $class_subject_ids = $this->classSubjects->pluck('id');

            if (ClassSubjectStudent::whereIn('class_subject_id', $class_subject_ids)->exists()) {
                return false;
            }
        }

        return true;
    }

    public function latestPrimaryClassBySemesterSettings(): HasMany
    {
        return $this->hasMany(LatestPrimaryClassBySemesterSettingView::class, 'semester_class_id');
    }

    public function timetable(): HasOne
    {
        return $this->hasOne(Timetable::class);
    }
}
