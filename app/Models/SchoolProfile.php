<?php

namespace App\Models;

use App\Traits\Models\HasMediaInteractions;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\Translatable\HasTranslations;

class SchoolProfile extends Model implements HasMedia
{
    use HasFactory, HasTranslations, HasMediaInteractions;

    protected $table = 'master_school_profiles';

    public $translatable = ['name'];
    public $fillable = [
        'name',
        'code',
        'short_name',
        'address',
        'country_id',
        'state_id',
        'city',
        'postcode',
        'phone_1',
        'phone_2',
        'fax_1',
        'fax_2',
        'email',
        'url'
    ];

    public function getLogoUrlAttribute()
    {
        $media = $this->getMedia('logo')->first();

        if($media) {
            return $media->getTemporaryUrl(now()->addSeconds(config('filesystems.s3_expires_seconds')));
        }

        return null;
    }
}
