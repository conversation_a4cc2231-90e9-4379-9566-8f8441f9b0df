<?php

namespace App\Models;

use App\Traits\Models\HasTranslations;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResultSourceSubjectComponent extends Model
{
    use HasFactory, HasTranslations;

    protected $table = 'result_source_subject_components';

    protected $guarded = ['id'];

    public $translatable = ['name'];

    public function dataEntryEmployee() {
        $this->belongsTo(Employee::class, 'data_entry_employee_id');
    }

}
