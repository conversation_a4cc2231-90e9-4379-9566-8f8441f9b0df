<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SemesterSetting extends Model
{
    use HasFactory;

    protected $table = 'master_semester_settings';

    protected $guarded = ['id'];

    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    public function semesterYearSetting(): BelongsTo
    {
        return $this->belongsTo(SemesterYearSetting::class, 'semester_year_setting_id');
    }

    public function defaultGradingFramework() {
        return $this->belongsTo(GradingFramework::class);
    }
}
