<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum EnrollmentPaymentStatus: string implements IEnum
{
    use EnumOption;

    case UNPAID = 'UNPAID';
    case PAID = 'PAID';
    case PENDING = 'PENDING';
    case NOT_REQUIRED = 'NOT_REQUIRED';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::UNPAID => 'Unpaid',
            self::PAID => 'Paid',
            self::PENDING => 'Pending',
            self::NOT_REQUIRED => 'Not Required',
        };
    }
}
