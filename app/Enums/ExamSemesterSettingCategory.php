<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum ExamSemesterSettingCategory: string implements IEnum
{
    use EnumOption;

    case ATTENDANCE = 'ATTENDANCE';
    case MERIT_DEMERIT = 'MERIT_DEMERIT';
    case MARK_DEDUCT = 'MARK_DEDUCT';
    case MARK_DEDUCT_RETAIN = 'MARK_DEDUCT_RETAIN';
    case OFF_CAMPUS = 'OFF_CAMPUS';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::ATTENDANCE => 'Attendance',
            self::MERIT_DEMERIT => 'Merit/Demerit',
            self::MARK_DEDUCT => 'Mark Deduct',
            self::MARK_DEDUCT_RETAIN => 'Mark Deduct (Retain)',
            self::OFF_CAMPUS => 'Off Campus',
        };
    }
}
