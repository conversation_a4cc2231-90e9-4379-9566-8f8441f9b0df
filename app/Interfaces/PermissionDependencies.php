<?php

namespace App\Interfaces;

/**
 * To be implemented by classes that can be printed to (e.g. billing document)
 */
interface PermissionDependencies
{
    const STUDENT_SEARCH_ENGINE = [
        'master-grade-view',
        'semester-class-view',
        'master-semester-setting-view',
        'student-view'
    ];

    const BED_SEARCH_ENGINE = [
        'hostel-block-view',
        'hostel-room-view',
        'hostel-room-bed-view'
    ];

    const EMPLOYEE_SEARCH_ENGINE = [
        'master-country-view',
        'employee-view',
        'master-race-view',
        'master-religion-view',
        'master-employee-job-title-view',
        'master-state-view'
    ];

    const CLASS_SEARCH_ENGINE = [
        'class-view',
        'master-grade-view'
    ];

    const BOOK_SEARCH_ENGINE = [
        'book-view',
        'master-book-sub-classification-view',
        'master-author-view'
    ];

    const DEPENDENCIES = [
        'master-school-create' => [
            'master-country-view',
            'master-state-view',
            'student-view'
        ],
        'master-school-update' => [
            'master-country-view',
            'master-state-view',
            'semester-class-view',
        ],
        'master-school-profile-update' => [
            'master-country-view',
            'master-state-view'
        ],
        'master-semester-setting-create' => [
            'master-semester-year-setting-view',
            'master-semester-year-setting-create'
        ],
        'master-semester-setting-update' => [
            'master-semester-year-setting-view',
            'master-semester-year-setting-create'
        ],
        'master-state-create' => [
            'master-country-view',
            'master-state-view'
        ],
        'master-state-update' => [
            'master-country-view',
            'master-state-view'
        ],
        'terminal-create' => [
            'merchant-view'
        ],
        'terminal-update' => [
            'merchant-view'
        ],
        'pos-terminal-key-create' => [
            'terminal-view'
        ],
        'pos-terminal-key-update' => [
            'terminal-view'
        ],
        'enrollment-create' => [
            'master-country-view',
            'master-race-view',
            'master-religion-view',
            'master-state-view'
        ],
        'enrollment-update' => [
            'master-country-view',
            'master-race-view',
            'master-religion-view',
            'master-state-view'
        ],
        'student-create' => [
            'master-country-view',
            'master-education-view',
            'master-grade-view',
            'master-race-view',
            'master-religion-view',
            'master-state-view',
            'master-health-concern-view',
            'master-school-view',
            'guardian-view',
        ],
        'student-update' => [
            'master-country-view',
            'master-education-view',
            'master-grade-view',
            'master-race-view',
            'master-religion-view',
            'master-state-view',
            'master-health-concern-view',
            'master-school-view',
            'guardian-view',
        ],
        'employee-create' => [
            'master-country-view',
            'master-employee-category-view',
            'master-employee-job-title-view',
            'master-employee-session-view',
            'master-race-view',
            'master-religion-view',
            'master-state-view'
        ],
        'employee-update' => [
            'master-country-view',
            'master-employee-category-view',
            'master-employee-job-title-view',
            'master-employee-session-view',
            'master-race-view',
            'master-religion-view',
            'master-state-view'
        ],
        'card-view' => [
            'contractor-view',
            ...self::STUDENT_SEARCH_ENGINE,
            ...self::EMPLOYEE_SEARCH_ENGINE,
        ],
        'hostel-room-create' => [
            'hostel-block-view'
        ],
        'hostel-room-update' => [
            'hostel-block-view'
        ],
        'hostel-room-bed-create' => [
            'hostel-room-view',
            'hostel-block-view'
        ],
        'hostel-room-bed-update' => [
            'hostel-room-view',
            'hostel-block-view'
        ],
        'hostel-bed-assignment-assign' => [

            ...self::STUDENT_SEARCH_ENGINE,
            ...self::EMPLOYEE_SEARCH_ENGINE,
            ...self::BED_SEARCH_ENGINE
        ],
        'hostel-bed-assignment-unassign' => [

            ...self::STUDENT_SEARCH_ENGINE,
            ...self::EMPLOYEE_SEARCH_ENGINE,
            ...self::BED_SEARCH_ENGINE
        ],
        'hostel-bed-assignment-change' => [

            ...self::STUDENT_SEARCH_ENGINE,
            ...self::EMPLOYEE_SEARCH_ENGINE,
            ...self::BED_SEARCH_ENGINE
        ],
        'reward-punishment-record-create' => [
            'hostel-merit-demerit-setting-view',
            'hostel-reward-punishment-setting-view',
            'reward-punishment-view',
            ...self::EMPLOYEE_SEARCH_ENGINE,
            ...self::STUDENT_SEARCH_ENGINE,
        ],
        'reward-punishment-record-update' => [
            'hostel-merit-demerit-setting-view',
            'hostel-reward-punishment-setting-view',
            'reward-punishment-view',
            ...self::EMPLOYEE_SEARCH_ENGINE,
            ...self::STUDENT_SEARCH_ENGINE,
        ],
        'hostel-in-out-record-create' => [
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'hostel-savings-account-deposit' => [
            'master-withdrawal-reason-view',
            'master-payment-method-view',
            'master-bank-view',
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'hostel-savings-account-withdraw' => [
            'master-withdrawal-reason-view',
            'master-payment-method-view',
            'master-bank-view',
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'library-member-create' => [
            'master-country-view',
            'master-config-view',
            'master-race-view',
            'master-religion-view',
            'master-state-view'
        ],
        'library-member-update' => [
            'master-country-view',
            'master-config-view',
            'master-race-view',
            'master-religion-view',
            'master-state-view'
        ],
        'book-create' => [
            'master-author-view',
            'master-book-category-view',
            'master-book-source-view',
            'master-book-classification-view',
            'master-book-sub-classification-view',
            'master-config-view'
        ],
        'book-update' => [
            'master-author-view',
            'master-book-category-view',
            'master-book-source-view',
            'master-book-classification-view',
            'master-book-sub-classification-view',
            'master-config-view'
        ],
        'master-book-sub-classification-create' => [
            'master-book-classification-view'
        ],
        'master-book-sub-classification-update' => [
            'master-book-classification-view'
        ],
        'book-loan-create' => [
            'book-view',
            'library-member-view'
        ],
        'book-loan-return' => [
            'library-member-view'
        ],
        'book-loan-extend' => [
            'library-member-view'
        ],
        'class-create' => [
            'master-grade-view',
            'master-semester-setting-view',
        ],
        'class-update' => [
            'master-grade-view',
            'master-semester-setting-view',
            ...self::CLASS_SEARCH_ENGINE,
        ],
        'semester-class-update' => [
            'master-semester-setting-view',
            ...self::CLASS_SEARCH_ENGINE
        ],
        'class-seat-assignment-update' => [
            'master-semester-setting-view',
            'semester-class-view',
        ],
        'subject-create' => [
            'club-view'
        ],
        'subject-view' => [
            'club-view'
        ],
        'subject-update' => [
            'club-view'
        ],
        'club-create' => [
            'club-category-view'
        ],
        'club-update' => [
            'club-category-view'
        ],
        'master-society-position-view ' => [
            'semester-class-view',
            'student-view',
            'master-society-position-view'
        ],
        'student-society-position-update' => [
            'semester-class-view',
            'student-view',
            'master-society-position-view'
        ],
        'cocurriculum-trainer-detail-report' => [
            'master-semester-setting-view'
        ],
        'conduct-deadline-view' => [
            'master-semester-setting-view',
            'semester-class-view'
        ],
        'conduct-deadline-update' => [
            'master-semester-setting-view',
            'semester-class-view'
        ],
        'conduct-setting-view' => [
            'master-semester-setting-view',
            'semester-class-view'
        ],
        'conduct-setting-update' => [
            'master-semester-setting-view',
            'semester-class-view'
        ],
        'comprehensive-assessment-question-create' => [
            'comprehensive-assessment-category-view'
        ],
        'comprehensive-assessment-question-update' => [
            'comprehensive-assessment-category-view'
        ],
        'comprehensive-assessment-record-view' => [
            'student-view',
            'semester-class-view',
            'comprehensive-assessment-question-view'
        ],
        'leadership-position-record-create' => [
            'master-leadership-position-view',
            'leadership-position-record-view',
            'semester-class-view',
            'master-semester-setting-view',
        ],
        'reward-punishment-create' => [
            'merit-demerit-setting-view',
            'master-reward-punishment-category-view',
            'master-reward-punishment-sub-category-view'
        ],
        'reward-punishment-update' => [
            'merit-demerit-setting-view',
            'master-reward-punishment-category-view',
            'master-reward-punishment-sub-category-view'
        ],
        'enrollment-session-create' => [
            'subject-view',
            'course-view',
        ],
        'enrollment-session-update' => [
            'subject-view',
            'course-view',
        ],
        'competition-create' => [
            'department-view',
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'competition-update' => [
            'department-view',
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'counselling-case-record-create' => [
            'student-view'
        ],
        'product-group-create' => [
            'product-view'
        ],
        'product-group-view' => [
            'product-view'
        ],
        'product-tag-create' => [
            'product-view'
        ],
        'product-tag-update' => [
            'product-view'
        ],
        'product-create' => [
            'merchant-view',
            'product-group-view',
            'product-category-view',
            'product-sub-category-view',
            'product-tag-view'
        ],
        'product-update' => [
            'merchant-view',
            'product-group-view',
            'product-category-view',
            'product-sub-category-view',
            'product-tag-view'
        ],
        'ecommerce-bookshops-orders-report' => [
            'master-semester-setting-view',
            'semester-class-view'
        ],
        'ecommerce-bookshops-students-report' => [
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'ecommerce-order-items-bookshops-report' => [
            'master-semester-setting-view',
            'master-grade-view',
        ],
        'ecommerce-canteens-classes-weekly-report' => [
            'master-semester-setting-view',
            'semester-class-view',
        ],
        'ecommerce-canteens-classes-date-range-report' => [
            'master-semester-setting-view',
            'semester-class-view',
        ],
        'ecommerce-canteens-by-student-report' => [
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'ecommerce-canteens-merchants-report' => [
            'master-semester-setting-view',
            'semester-class-view',
        ],
        'ecommerce-canteens-by-merchant-daily-sales-report' => [
            'merchant-view'
        ],
        'enrollment-by-daily-collection-report' => [
            'master-product-view',
        ],
        'hostel-by-boarders-name-list-report' => [
            'master-grade-view',
            'master-semester-setting-view',
            'semester-class-view',
        ],
        'hostel-by-checkout-record-report' => [
            'master-semester-setting-view',
        ],
        'hostel-by-change-room-record-report' => [
            'master-semester-setting-view',
        ],
        'hostel-by-boarders-list-info-report' => [
            'master-semester-setting-view',
        ],
        'hostel-by-boarders-contact-info-report' => [
            'master-semester-setting-view',
        ],
        'hostel-by-boarders-date-of-birth-report' => [
            'master-semester-setting-view',
        ],
        'library-book-loans-report' => [
            'master-book-category-view',
            'master-semester-setting-view',
            'semester-class-view',
        ],
        'library-top-borrowers-report' => [
            'master-grade-view',
            'semester-class-view',
            'master-semester-setting-view',
        ],
        'library-top-borrowed-books-report' => [
            'book-language-view'
        ],
        'library-school-rate-borrow-books-report' => [
            'master-semester-setting-view',
        ],
        'library-book-borrow-records-report' => [
            ...self::BOOK_SEARCH_ENGINE
        ],
        'semester-class-student-contacts-report' => [
            'master-semester-setting-view',
            'semester-class-view',
        ],
        'semester-class-student-details-report' => [
            'master-semester-setting-view',
            'semester-class-view',
        ],
        'semester-class-homeroom-teachers-report' => [
            'master-semester-setting-view',
        ],
        'semester-class-by-students-in-class-report' => [
            'master-semester-setting-view',
            'semester-class-view',
        ],
        'wallet-report' => [
            'terminal-view'
        ],
        'announcement-group-create' => [
            'announcement-group-view',
            ...self::STUDENT_SEARCH_ENGINE,
            ...self::EMPLOYEE_SEARCH_ENGINE
        ],
        'announcement-group-update' => [
            ...self::STUDENT_SEARCH_ENGINE,
            ...self::EMPLOYEE_SEARCH_ENGINE
        ],
        'master-product-create' => [
            'master-uom-view',
            'master-gl-account-view'
        ],
        'master-product-upate' => [
            'master-uom-view',
            'master-gl-account-view'
        ],
        'fees-create-and-assign' => [
            'master-product-view',
            'master-currency-view'
        ],
        'student-attendance-report' => [
            'semester-class-view',
            'master-semester-setting-view',
        ],
        'primary-semester-class-view' => [
            'semester-class-view',
            'employee-view',
            'master-semester-setting-view',
            'semester-class-student-contacts-report',
            'semester-class-homeroom-teachers-report',
            'semester-class-student-details-report',
            'semester-class-by-students-in-class-report',
            'subject-view',
            ...self::CLASS_SEARCH_ENGINE,
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'english-semester-class-view' => [
            'semester-class-view',
            'employee-view',
            'master-semester-setting-view',
            'semester-class-student-contacts-report',
            'semester-class-homeroom-teachers-report',
            'semester-class-student-details-report',
            'semester-class-by-students-in-class-report',
            'subject-view',
            ...self::CLASS_SEARCH_ENGINE,
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'society-semester-class-view' => [
            'semester-class-view',
            'employee-view',
            'master-semester-setting-view',
            'semester-class-student-contacts-report',
            'semester-class-homeroom-teachers-report',
            'semester-class-student-details-report',
            'semester-class-by-students-in-class-report',
            'subject-view',
            ...self::CLASS_SEARCH_ENGINE,
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'primary-class-subject-view' => [
            'master-semester-setting-view',
            'semester-class-view',
            'subject-view'
        ],
        'primary-class-subject-update' => [
            'master-semester-setting-view',
            'semester-class-view',
            'subject-view',
            ...self::EMPLOYEE_SEARCH_ENGINE,
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'english-class-subject-view' => [
            'master-semester-setting-view',
            'semester-class-view',
            'subject-view',
        ],
        'english-class-subject-update' => [
            'master-semester-setting-view',
            'semester-class-view',
            'subject-view',
            ...self::EMPLOYEE_SEARCH_ENGINE,
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'society-class-subject-view' => [
            'master-semester-setting-view',
            'semester-class-view',
            'subject-view',
        ],
        'society-class-subject-update' => [
            'master-semester-setting-view',
            'semester-class-view',
            'subject-view',
            ...self::EMPLOYEE_SEARCH_ENGINE,
            ...self::STUDENT_SEARCH_ENGINE
        ],
        'substitute-record-view' => [
            'primary-class-view',
            'subject-view',
            'employee-view',
        ],
        'substitute-record-bulk-create-update-view' => [
            'employee-view'
        ],
        'timetable-view' => [
            'primary-class-subject-view'
        ],
        'timetable-update' => [
            'employee-view'
        ],
        'class-attendance-taking-status-report' => [
            'employee-view'
        ]
    ];
}
