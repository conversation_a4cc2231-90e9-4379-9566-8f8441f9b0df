<?php

namespace App\Services\Reports;

use App\Exports\BillingDocumentsByDailyCollectionExport;
use App\Factories\ExportAdapterFactory;
use App\Repositories\BillingDocumentRepository;
use App\Services\BaseReportService;
use App\Services\ReportPrintService;
use Illuminate\Support\Collection;

class BillingDocumentReportService extends BaseReportService
{
    public function __construct(
        protected BillingDocumentRepository $billingDocumentRepository,
        protected ReportPrintService $reportPrintService,
    ) {
    }

    public function getDailyCollectionReportData(array $filters = []): mixed
    {
        $data = $this->billingDocumentRepository->getPaidInvoiceReportData($filters);

        $total_amount = 0;
        $net_amount = 0;

        if (isset($filters['product_ids']) && count($filters['product_ids'])) {
            $line_items = $this->filterLineItemsByProductIds($data, $filters['product_ids']);

            $data = $line_items['data'];
            $total_amount = $line_items['total_amount'];

        } else {
            $total_amount = $data->sum('amount_after_tax');
            $net_amount = $total_amount;
            $data->each(function ($item) use (&$net_amount) {
                $payex_charge = $item->amount_after_tax <= 250 ? bcadd('0.6', bcmul('0.001', $item->amount_after_tax, 2), 2) : 0.85;
                $net_amount -= $payex_charge;
            });
        }


        $report_data = [
            'data' => $data,
            'total_amount' => $total_amount,
            'net_amount' => $net_amount
        ];

        // !!!IMPORTANT, Currently only support excel, please change this if got PDF format!!!
        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new BillingDocumentsByDailyCollectionExport($report_data['data'], $report_data['total_amount'], $report_data['net_amount']))
            ->setReportViewName($this->getReportViewName());

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    private function filterLineItemsByProductIds(Collection $data, array $product_ids): array
    {
        $total_amount = 0;

        foreach ($data as &$billing_document) {
            $line_items = $billing_document->lineItems;
            $final_line_item_ids = collect();

            $related_line_items = $line_items
                ->filter(function ($item) use ($product_ids) {
                    // filter line items that are not discounts + (line items that is in $product_ids or advance line item)
                    return (
                        !$item->is_discount &&
                        (in_array($item->product_id, $product_ids) || $item->offset_billing_document_id !== null)
                    );
                });

            $final_line_item_ids = $final_line_item_ids->merge($related_line_items->pluck('id'));

            $related_discount_line_items = $line_items
                ->filter(function ($item) use ($related_line_items) {
                    // filter line items that are discounts and related to the line items to be included
                    return $item->is_discount &&
                        !empty($item->discount_original_line_item_id) &&
                        $related_line_items->pluck('id')->contains($item->discount_original_line_item_id);
                });

            $final_line_item_ids = $final_line_item_ids->merge($related_discount_line_items->pluck('id'));

            // get filtered line items
            $filtered_line_items = $line_items->whereIn('id', $final_line_item_ids);

            // set the filtered line items back to the billing document
            $billing_document->setRelation('lineItems', $filtered_line_items);

            // add the amount of the filtered line items to the total amount
            $total_amount = bcadd($total_amount, $filtered_line_items->sum('amount_before_tax'), 2);
        }

        return [
            'data' => $data,
            'total_amount' => $total_amount,
        ];
    }
}

