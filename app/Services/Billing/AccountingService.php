<?php

namespace App\Services\Billing;

use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Helpers\ErrorCodeHelper;
use App\Helpers\SystemHelper;
use App\Models\BillingDocument;
use App\Models\GlAccount;
use App\Models\PaymentMethod;
use App\Models\PaymentRequest;
use App\Models\UnpaidItem;
use App\Models\User;
use App\Repositories\GuardianRepository;
use App\Repositories\PaymentMethodRepository;
use App\Repositories\UnpaidItemRepository;
use App\Services\PaymentGatewayLogService;
use App\Services\PaymentGatewayService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Cache\Lock;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class AccountingService
{
    const UNPAID_ITEM_LOCK_RELEASE_IN_SECONDS = 60;

    protected PaymentGatewayLogService $paymentGatewayLogService;
    protected PaymentMethodRepository $paymentMethodRepository;
    protected GuardianRepository $guardianRepository;
    protected PaymentGatewayService $paymentGatewayService;
    protected Authenticatable|User|Model $user;
    protected UnpaidItemRepository $unpaidItemRepository;
    protected ?string $returnUrl = null;

    protected Collection $unpaidItems;
    protected Collection $existingUnpaidItems;
    protected BillingDocument $billingDocument;

    public function __construct(
        PaymentGatewayLogService $payment_gateway_log_service,
        PaymentGatewayService $payment_gateway_service,
        UnpaidItemRepository $unpaidItemRepository,
        PaymentMethodRepository $payment_method_repository,
        GuardianRepository $guardian_repository,
    ) {
        $this->paymentGatewayLogService = $payment_gateway_log_service;
        $this->paymentGatewayService = $payment_gateway_service;
        $this->unpaidItemRepository = $unpaidItemRepository;
        $this->paymentMethodRepository = $payment_method_repository;
        $this->guardianRepository = $guardian_repository;
    }

    public function getUser(): User|Authenticatable|Model
    {
        return $this->user;
    }

    public function setUser(User|Authenticatable|Model $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function processManualPayment(array $input): bool
    {
        $billing_document = $this->billingDocument;

        $this->validateManualPayment($input);

        $payment_method_codes = collect($input['payments'])->pluck('payment_method_code')->unique()->toArray();

        $payment_methods = $this->paymentMethodRepository->getAll([
            'code' => $payment_method_codes
        ])->keyBy('code');

        $bill_to = $billing_document->billTo;

        DB::transaction(function () use ($input, $billing_document, $payment_methods, $bill_to) {

            foreach ($input['payments'] as $input_payment) {
                $input_payment_method = $payment_methods[$input_payment['payment_method_code']];

                /** @var PaymentRequestService */
                $payment_request_service = app()->make(PaymentRequestService::class);

                $payment_request = $payment_request_service
                    ->setUserable($bill_to)
                    ->setBillingDocument($billing_document)
                    ->setPaymentMethod($input_payment_method)
                    ->setStatus(PaymentRequest::STATUS_APPROVED)
                    ->setAmount($input_payment['amount_received'])
                    ->setBankId($input_payment['bank_id'] ?? null)
                    ->setPaymentReferenceNo($input_payment['payment_reference_no'] ?? $billing_document->reference_no)
                    ->setApprovedAt(now())
                    ->setApprovedByEmployee($this->getUser()->employee)
                    ->create()
                    ->getPaymentRequest();

                // upload file for payment proof
                if (!empty($input_payment['file']) && is_file($input_payment['file'])) {
                    $payment_request->replaceMedia('file', $input_payment['file']);

                    $payment_request->proof_of_payment_url = $payment_request->file;
                    $payment_request->save();
                }

                /** @var PaymentService */
                $payment_service = app()->make(PaymentService::class);

                $payment_service
                    ->setBillingDocument($billing_document->refresh())
                    ->setPaymentRequestAndPopulateData($payment_request)
                    ->setRemarks($input['remarks'])
                    ->create()
                    ->triggerPostPaymentProcesses();
            }
        });

        return true;
    }

    public function createBillingDocumentFromUnpaidItem(): Model
    {
        if ($this->unpaidItems->isEmpty()) {
            throw new \Exception('Items to be paid is empty');
        }

        $lock = $this->getUnpaidItemPaymentLock();

        try {

            if (!$lock->get()) {
                throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::PAYMENT_GATEWAY_ERROR, 2005), 2005);
            }

            return $this->generateBillingDocumentFromUnpaidItems()
                ->determineBillingDocumentPayableStatus()
                ->getBillingDocument();

        } finally {
            optional($lock)->release();
        }

    }

    public function determineBillingDocumentPayableStatus()
    {
        $billing_document = $this->billingDocument;

        // if billing_document->amount_after_tax is RM0.00, then mark as paid
        if (bccomp($billing_document->amount_after_tax, 0, 2) === 0) {
            app()->make(PaymentService::class)
                ->setSystemInitiatedAsPaid($billing_document)
                ->create();

            app()->make(BillingDocumentService::class)
                ->setBillingDocument($billing_document)
                ->changePaymentStatusTo(BillingDocument::PAYMENT_STATUS_PAID, now(config('school.timezone')));

            $this->billingDocument = $billing_document->refresh();
        }

        return $this;
    }

    public function generatePaymentGatewayLogWithPaymentLink()
    {
        $bill_to = $this->billingDocument->billTo;

        $payment_gateway_log = $this->paymentGatewayLogService
            ->setProvider(PaymentProvider::PAYEX)
            ->setUser($this->getUser())
            ->setTransactionLoggable($this->billingDocument)
            ->setBillingDocument($this->billingDocument)
            ->setPaymentMethod(PaymentMethod::where('code', PaymentMethod::CODE_FPX)->firstOrFail())
            ->createPayUnpaidItemTransaction($this->billingDocument->amount_after_tax);

        $payment_gateway_response = $this->paymentGatewayService
            ->setProvider(PaymentProvider::PAYEX)
            ->setPaymentTypes([PaymentMethod::CODE_FPX])
            ->setCustomerEmail($bill_to->email)
            ->setCustomerName($bill_to->getTranslation('name', 'en'))
            ->setPaymentGatewayLog($payment_gateway_log)
            ->setReturnUrl($this->returnUrl ?? null)
            ->request();


        $this->paymentGatewayLogService
            ->setRequestData($payment_gateway_response['request_data'])
            ->setResponseData($payment_gateway_response['response_data'])
            ->setOrderId($payment_gateway_response['order_id']);

        if (Arr::get($payment_gateway_response, 'status') == PaymentStatus::FAILED) {

            $this->paymentGatewayLogService
                ->setRemark($payment_gateway_response['remark'])
                ->setStatus(PaymentStatus::FAILED)
                ->updatePaymentGatewayLog();

            throw new \Exception($payment_gateway_response['remark']);

        }

        return $this->paymentGatewayLogService
            ->setPaymentUrl($payment_gateway_response['payment_url'])
            ->updatePaymentGatewayLog();

    }

    public function generateBillingDocumentFromUnpaidItems(): static
    {
        $bill_to = $this->unpaidItems->first()->billTo;

        DB::transaction(function () use (&$bill_to) {

            /** @var BillingDocumentService $invoice_service */
            $invoice_service = app()->make(BillingDocumentService::class);

            $invoice_service
                ->init()
                ->setType(BillingDocument::TYPE_INVOICE)
                ->setSubType(BillingDocument::SUB_TYPE_FEES)
                ->setDefaultValuesForPayingUnpaidItems()
                ->setCurrency(config('school.currency_code'))
                ->setStatus(BillingDocument::STATUS_CONFIRMED)
                ->setBillToParty($bill_to);

            foreach ($this->unpaidItems as $unpaid_item) {
                /** @var BillingDocumentLineItemService $line_item_service */
                $line_item_service = app()->make(BillingDocumentLineItemService::class);

                $line_item = $line_item_service
                    ->setBillableItem($unpaid_item)
                    ->setQuantity($unpaid_item->quantity)
                    ->setUnitPrice($unpaid_item->unit_price)
                    ->setGlAccountCode($unpaid_item->gl_account_code)
                    ->setDescription($unpaid_item->description)
                    ->setAmountBeforeTax($unpaid_item->amount_before_tax)
                    ->setProduct($unpaid_item->product)
                    ->setCurrency($unpaid_item->currency_code)
                    ->make();

                $invoice_service->addLineItem($line_item);
            }

            $billing_document = $invoice_service
                ->calculateAmountBeforeTax()
                ->applyTax(SystemHelper::getNotApplicableTax())
                ->calculatePaymentDueDate()
                ->generateReferenceNumber()
                ->create()
                ->calculateEligibleDiscounts()
                ->addDiscountLineItems()
                ->applyAdvanceOffset()      // deduct advance
                ->createAdvanceOffsetTransactions()
                ->recalculateAndUpdateAfterDiscount()
                ->getBillingDocument();

            // assign billing document to unpaid items, marking unpaid items status as PENDING
            app()->make(UnpaidItemService::class)
                ->setUnpaidItems($this->unpaidItems)
                ->assignBillingDocument($billing_document)
                ->save();

            $this->billingDocument = $billing_document;

        });

        return $this;
    }

    public function validateManualPayment(array $input): void
    {
        /**
         * throw error if bill is not valid status for payment
         */

        if (!isset($this->billingDocument)) {
            throw new Exception('Billing document is not set');
        }

        if (!$this->billingDocument->isValidStatusForPayment()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::BILLING_DOCUMENT_ERROR, 37001);
        }

        /**
         * throw error if total amount_received is not equal to billing_document->amount_after_tax
         */
        $total_amount_received = 0;

        foreach ($input['payments'] as $payment) {
            $total_amount_received = bcadd($total_amount_received, $payment['amount_received'], 2);
        }

        $total_fees = $this->billingDocument->amount_after_tax;

        if (bccomp($total_amount_received, $total_fees, 2) !== 0) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36008);
        }
    }

    /**
     * FE validation only
     */
    public function validateUserCreateBillingDocument(): self
    {
        $this->validateCommonCreateBillingDocumentRules();

        $existing_items = $this->getExistingUnpaidItems();
        $unpaid_items = $this->unpaidItems;
        $user = $this->getUser();
        $bill_to = $unpaid_items->first()->billTo;

        /**
         *
         * if user cannot access unpaid items
         *
         */
        foreach ($unpaid_items as $unpaid_item) {
            if (!$user->canAccessUserable($unpaid_item->bill_to_type, $unpaid_item->bill_to_id)) {
                ErrorCodeHelper::throwError(ErrorCodeHelper::GENERIC_ERROR, 403);
            }
        }

        $pending_unpaid_items_filters = [
            'bill_to_type' => get_class($bill_to),
            'bill_to_id' => $bill_to->id,
            'status' => UnpaidItem::STATUS_PENDING,
        ];

        /**
         *
         * if the current user has a pending UnpaidItem
         *
         */
        if ($this->unpaidItemRepository->exists($pending_unpaid_items_filters)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36009);
        }


        /**
         *
         * if paying unpaid_items not in sequence, must clear out the first one.
         * e.g If got unpaid_items JAN, FEB, cannot str8 pay FEB first
         *
         */
        $payload_by_gl_account = $unpaid_items->sortBy(function ($value) {
            return Carbon::parse($value->period)->timestamp;
        })->groupBy('gl_account_code');

        $existing_by_gl_account = $existing_items->sortBy(function ($value) {
            return Carbon::parse($value->period)->timestamp;
        })->groupBy('gl_account_code');

        foreach ($payload_by_gl_account as $gl_account_code => $payload_group) {
            $existing_group = $existing_by_gl_account[$gl_account_code];

            if (!$this->validateUnpaidItemPaymentSequence($payload_group, $existing_group)) {
                ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36001);
            }
        }

        /**
         *
         * if in the period chosen, there are existing unpaid items not paid during the same month
         * e.g in the 2024 FEB, have 5 UnpaidItem, select to pay only 4 UnpaidItem in FEB then throw error
         *
         */
        $payload_unpaid_items = $unpaid_items->sortBy(function ($value) {
            return Carbon::parse($value->period)->timestamp;
        })->groupBy('period');

        $existing_unpaid_items = $existing_items->sortBy(function ($value) {
            return Carbon::parse($value->period)->timestamp;
        })->groupBy('period');

        $first_payload_group = $payload_unpaid_items->first();
        $first_existing_unpaid_item_group = $existing_unpaid_items->first();

        if ($first_payload_group->count() !== $first_existing_unpaid_item_group->count()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36002);
        }

        return $this;
    }

    /**
     * BO validation only
     */
    public function validateAdminCreateBillingDocument(): self
    {
        $this->validateCommonCreateBillingDocumentRules();

        $user = $this->getUser();

        if (!$user->isEmployee()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::GENERIC_ERROR, 403);
        }

        return $this;
    }

    public function validateCommonCreateBillingDocumentRules(): self
    {
        $unpaid_items = $this->unpaidItems;


        /**
         *
         * if no unpaid items to be paid
         *
         */
        if ($unpaid_items->isEmpty()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36004);
        }

        $unique_userables = collect([]);


        /**
         *
         * if unpaid item is not valid for payment
         *
         */
        foreach ($unpaid_items as $unpaid_item) {
            if ($unpaid_item->isNotValidForPayment()) {
                ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36005);
            }

            $unique_userables->push($unpaid_item->bill_to_type . '-' . $unpaid_item->bill_to_id);
        }


        /**
         *
         * if unpaid items dont belong to same bill_to
         *
         */
        if ($unique_userables->unique()->count() > 1) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36003);
        }


        $bill_to = $unpaid_items->first()->billTo;

        $existing_items = $this->unpaidItemRepository->getAll([
            'bill_to_type' => get_class($bill_to),
            'bill_to_id' => $bill_to->id,
            'status' => UnpaidItem::STATUS_UNPAID,
        ]);

        $this->setExistingUnpaidItems($existing_items);

        /**
         *
         * if items to be paid is more than existing_items
         *
         */
        if ($unpaid_items->count() > $existing_items->count()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36006);
        }


        /**
         *
         * if billing_document->amount_after_tax is between RM0.01 and RM1
         *
         */
        $this->validateBillingDocumentAmountAfterTax($unpaid_items, $bill_to);


        return $this;
    }

    public function validateBillingDocumentAmountAfterTax($unpaid_items, $bill_to): void
    {
        /**
         * throw error if billing_document->amount_after_tax is between RM0.01 and RM1
         *
         * simulate creating billing_document and check the amount_after_tax must not be between RM0.01 and RM0.99
         */

        /** @var BillingDocumentService $billing_document_service */
        $billing_document_service = app()->make(BillingDocumentService::class);

        $billing_document_service
            ->init()
            ->setType(BillingDocument::TYPE_INVOICE)
            ->setSubType(BillingDocument::SUB_TYPE_FEES)
            ->setDefaultValuesForPayingUnpaidItems()
            ->setCurrency(config('school.currency_code'))
            ->setStatus(BillingDocument::STATUS_CONFIRMED)
            ->setBillToParty($bill_to)
            ->setDocumentDate(now()->startOfDay());

        foreach ($unpaid_items as $unpaid_item) {
            /** @var BillingDocumentLineItemService $line_item_service */
            $line_item_service = app()->make(BillingDocumentLineItemService::class);

            $line_item = $line_item_service
                ->setBillableItem($unpaid_item)
                ->setQuantity($unpaid_item->quantity)
                ->setUnitPrice($unpaid_item->unit_price)
                ->setGlAccountCode($unpaid_item->gl_account_code)
                ->setDescription($unpaid_item->description)
                ->setAmountBeforeTax($unpaid_item->amount_before_tax)
                ->setProduct($unpaid_item->product)
                ->setCurrency($unpaid_item->currency_code)
                ->make();

            $billing_document_service->addLineItem($line_item);
        }

        $amount_after_tax = $billing_document_service
            ->calculateEligibleDiscounts()
            ->addDiscountLineItems()
            ->applyAdvanceOffset()
            ->getAmountAfterTax();

        // if billing_document->amount_after_tax is between RM0.01 and RM0.99, then throw error
        if (bccomp($amount_after_tax, '0.01', 2) >= 0 && bccomp($amount_after_tax, 1, 2) === -1) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36010);
        }
    }

    public function validateUnpaidItemPaymentSequence($payload_items, $existing_unpaid_items): bool
    {
        $payload_items = $payload_items->sortBy(function ($value) {
            return Carbon::parse($value->period)->timestamp;
        });

        $existing_unpaid_items = $existing_unpaid_items->sortBy(function ($value) {
            return Carbon::parse($value->period)->timestamp;
        });

        // must start paying at the first available unpaid item, sorted by period ASC
        if ($existing_unpaid_items->first()->id !== $payload_items->first()->id) {
            return false;
        }

        // Make sure payload_items doesn't skip sequence (e.g. pay Jan 2025, March 2025, April 2025 fees skipping over Feb 2025)
        $expected_items_in_sequence = $existing_unpaid_items->take($payload_items->count());

        return $expected_items_in_sequence->pluck('id')->toArray() === $payload_items->pluck('id')->toArray();
    }

    private function getUnpaidItemPaymentLock(): Lock
    {
        return Cache::lock(
            'unpaid-item-payment-lock-' . $this->getUser()->id,
            self::UNPAID_ITEM_LOCK_RELEASE_IN_SECONDS
        );
    }

    public function getUnpaidItems(): Collection
    {
        return $this->unpaidItems;
    }

    public function setUnpaidItems(Collection $unpaidItems): AccountingService
    {
        $this->unpaidItems = $unpaidItems;
        return $this;
    }

    public function getExistingUnpaidItems(): Collection
    {
        return $this->existingUnpaidItems;
    }

    public function setExistingUnpaidItems(Collection $existingUnpaidItems): AccountingService
    {
        $this->existingUnpaidItems = $existingUnpaidItems;
        return $this;
    }

    public function getBillingDocument(): BillingDocument
    {
        return $this->billingDocument;
    }

    public function setBillingDocument(BillingDocument $billingDocument): AccountingService
    {
        $this->billingDocument = $billingDocument;
        return $this;
    }

    public function getReturnUrl(): ?string
    {
        return $this->returnUrl;
    }

    public function setReturnUrl(?string $return_url): AccountingService
    {
        $this->returnUrl = $return_url;
        return $this;
    }
}
