<?php

namespace App\Services;

use App\Enums\EnrollmentStatus;
use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\PaymentType;
use App\Enums\WalletTransactionStatus;
use App\Factories\BillingDocumentFactory;
use App\Interfaces\IPaymentGateway;
use App\Interfaces\TransactionLoggable;
use App\Models\BillingDocument;
use App\Models\Currency;
use App\Models\Enrollment;
use App\Models\PaymentGatewayLog;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\WalletTransaction;
use App\Services\Billing\PaymentService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class PaymentGatewayService
{
    const FAIL_STATUS = [
        Enrollment::class => EnrollmentStatus::PAYMENT_FAILED,
        WalletTransaction::class => WalletTransactionStatus::FAILED,
    ];

    protected PaymentGatewayLogService $paymentGatewayLogService;
    protected IPaymentGateway $service;

    protected PaymentProvider $provider;
    protected Model $paymentGatewayLog;
    protected array $paymentTypes;
    protected string $customerName;
    protected ?string $customerEmail;
    protected ?string $returnUrl = null;

    protected array $callbackData;
    protected array $transaction;

    protected User $user;
    protected float $amount;
    protected string $description;
    protected Currency $currency;
    protected PaymentStatus $status;
    protected PaymentType $type;
    protected TransactionLoggable $transactionLoggable;
    private BillingDocument $billingDocument;
    private PaymentMethod $paymentMethod;

    public function __construct(
        PaymentGatewayLogService $payment_gateway_log_service
    ) {
        $this->paymentGatewayLogService = $payment_gateway_log_service;
    }

    public function setProvider(PaymentProvider $provider): self
    {
        $this->provider = $provider;

        switch ($provider) {
            case PaymentProvider::PAYEX:
                $this->service = app(PayexService::class);
                break;
        }

        if (!$this->service) {
            throw new Exception('Payment gateway not supported.');
        }

        return $this;
    }

    public function getProvider(): PaymentProvider
    {
        return $this->provider;
    }

    public function setPaymentGatewayLog(Model $payment_gateway_log): self
    {
        $this->paymentGatewayLog = $payment_gateway_log;

        return $this;
    }

    public function setPaymentTypes(array $payment_types): self
    {
        $this->paymentTypes = $payment_types;

        return $this;
    }

    public function getPaymentTypes(): array
    {
        return $this->paymentTypes;
    }

    public function setCustomerName(string $customer_name): self
    {
        $this->customerName = $customer_name;

        return $this;
    }

    public function getCustomerName(): string
    {
        return $this->customerName;
    }

    public function setCustomerEmail(?string $customer_email): self
    {
        $this->customerEmail = $customer_email;

        return $this;
    }

    public function getCustomerEmail(): string
    {
        return $this->customerEmail;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setCurrency(Currency $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    public function setStatus(PaymentStatus $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getStatus(): PaymentStatus
    {
        return $this->status;
    }

    public function setType(PaymentType $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getType(): PaymentType
    {
        return $this->type;
    }

    public function setTransactionLoggable(TransactionLoggable $transaction_loggable): self
    {
        $this->transactionLoggable = $transaction_loggable;

        return $this;
    }

    public function getTransactionLoggable(): TransactionLoggable
    {
        return $this->transactionLoggable;
    }

    public function setBillingDocument(BillingDocument $billing_document)
    {
        $this->billingDocument = $billing_document;

        return $this;
    }

    public function getBillingDocument(): BillingDocument
    {
        return $this->billingDocument;
    }

    public function setPaymentMethod(PaymentMethod $payment_method): self
    {
        $this->paymentMethod = $payment_method;

        return $this;
    }

    public function getPaymentMethod(): PaymentMethod
    {
        return $this->paymentMethod;
    }

    public function setReturnUrl(?string $return_url): self
    {
        $this->returnUrl = $return_url;
        return $this;
    }

    public function getReturnUrl(): string
    {
        return $this->returnUrl;
    }

    public function request(): array
    {
        if (!isset($this->provider)) {
            throw new Exception('Payment provider is no set.');
        }

        $order_id = $this->service->generateOrderId($this->paymentGatewayLog);

        $data = [
            'order_id' => $order_id,
        ];
        try {
            $url = $this->service
                ->setOrderId($order_id)
                ->setAmount($this->paymentGatewayLog->amount)
                ->setCurrency($this->paymentGatewayLog->currency_code)
                ->setPaymentTypes($this->paymentTypes)
                ->setCustomerName($this->customerName)
                ->setCustomerEmail($this->customerEmail)
                ->setDescription($this->paymentGatewayLog->description)
                ->setCallbackId($this->paymentGatewayLog->id)
                ->setCallbackToken($this->paymentGatewayLog->token)
                ->setReturnUrl($this->returnUrl)
                ->request();

            $data['payment_url'] = $url;
        } catch (Exception $e) {
            $data['remark'] = $e->getMessage();
            $data['status'] = PaymentStatus::FAILED;
        }

        $data['request_data'] = $this->service->getRequestData();
        $data['response_data'] = $this->service->getResponseData();

        return $data;
    }

    public function setCallbackData(array $callback_data): self
    {
        $this->callbackData = $callback_data;
        return $this;
    }

    public function validateCallbackData(): self
    {
        if (!isset($this->provider)) {
            throw new Exception('Payment provider is not set.');
        }

        $this->service->validateCallbackData($this->callbackData);

        return $this;
    }

    public function getTransaction(): array
    {
        if (!isset($this->provider)) {
            throw new Exception('Payment provider is no set.');
        }

        $this->transaction = $this->service->getTransaction($this->callbackData);

        return $this->transaction;
    }

    public function getTransactionByReferenceNumber(string $reference_number): array
    {
        if (!isset($this->provider)) {
            throw new Exception('Payment provider is no set.');
        }

        $this->transaction = $this->service->getTransactionByReferenceNumber($reference_number);

        return $this->transaction;
    }

    public function getTransactionFromCallback(array $callback_data): array
    {
        if (!isset($this->provider)) {
            throw new Exception('Payment provider is no set.');
        }

        $this->transaction = $callback_data;

        return $this->transaction;
    }

    public function getTransactionAmount(): float
    {
        $this->checkProviderAndTransaction();

        return $this->service->getTransactionAmount();
    }

    public function getTransactionOrderId(): string
    {
        $this->checkProviderAndTransaction();

        return $this->service->getTransactionOrderId();
    }

    public function getTransactionStatus(): PaymentStatus
    {
        $this->checkProviderAndTransaction();

        return $this->service->getTransactionStatus();
    }

    public function getTransactionResponse(): ?string
    {
        $this->checkProviderAndTransaction();

        return $this->service->getTransactionResponse();
    }

    public function getTransactionId(): ?string
    {
        $this->checkProviderAndTransaction();

        return $this->service->getTransactionId();
    }

    public function getTransactionDate(): Carbon|null
    {
        $this->checkProviderAndTransaction();

        return $this->service->getTransactionDate();
    }

    public function checkProviderAndTransaction(): void
    {
        if (!isset($this->provider)) {
            throw new Exception('Payment provider is no set.');
        }

        if (!isset($this->transaction)) {
            throw new Exception('Transaction is no set.');
        }
    }

    /**
     * @throws Exception
     */
    public function handleCallback(array $input, string $provider, PaymentGatewayLog $payment_gateway_log): void
    {
        $provider = PaymentProvider::getEnum($provider);

        $this->setPaymentGatewayLog($payment_gateway_log)
            ->setProvider($provider)
            ->setCallbackData($input)
            ->validateCallbackData();

        $this->service->setTransaction($input);
        $this->getTransactionFromCallback($input);

        $this->paymentGatewayLogService
            ->setModel($payment_gateway_log)
            ->setReferenceId($this->getTransactionId())
            ->setTransactionDatetime($this->getTransactionDate())
            ->setCallbackData($input)
            ->setRemark($this->getTransactionResponse())
            ->setStatus($this->getTransactionStatus())
            ->updatePaymentGatewayLog();

        $this->processTransactionLoggableCallback($payment_gateway_log, $this->getTransactionStatus());
    }

    public function createPaymentTransaction()
    {
        $payment_gateway_log = $this->paymentGatewayLogService
            ->setProvider($this->getProvider()) // TODO: To add other provider in the future if any
            ->setUser($this->getUser())
            ->setTransactionLoggable($this->getTransactionLoggable())
            ->setType($this->getType())
            ->setDescription($this->getDescription())
            ->setAmount($this->getAmount())
            ->setCurrency($this->getTransactionLoggable()->getCurrency())
            ->setStatus(PaymentStatus::PENDING)
            ->setBillingDocument($this->getBillingDocument())
            ->setPaymentMethod($this->getPaymentMethod())
            ->createPaymentGatewayLog();

        $payment_gateway_response = $this
            ->setPaymentTypes($this->getPaymentTypes())
            ->setCustomerEmail($this->getCustomerEmail())
            ->setCustomerName($this->getCustomerName())
            ->setPaymentGatewayLog($payment_gateway_log)
            ->request();

        $this->paymentGatewayLogService
            ->setRequestData($payment_gateway_response['request_data'])
            ->setResponseData($payment_gateway_response['response_data'])
            ->setOrderId($payment_gateway_response['order_id']);

        if (Arr::get($payment_gateway_response, 'status') == PaymentStatus::FAILED) {
            $this->processTransactionLoggableCallback($payment_gateway_log, PaymentStatus::FAILED);

            $this->paymentGatewayLogService
                ->setRemark($payment_gateway_response['remark'])
                ->setStatus(PaymentStatus::FAILED)
                ->updatePaymentGatewayLog();

            throw new \Exception($payment_gateway_response['remark']);
        }

        return $this->paymentGatewayLogService
            ->setPaymentUrl($payment_gateway_response['payment_url'])
            ->updatePaymentGatewayLog();
    }

    private function processTransactionLoggableCallback(PaymentGatewayLog $payment_gateway_log, PaymentStatus $payment_status): void
    {
        if ($payment_status === PaymentStatus::SUCCESS) {

            app()->make(PaymentService::class)
                ->setPaymentGatewayLogAndPopulateData($payment_gateway_log)
                ->create()
                ->triggerPostPaymentProcesses();

        } else {
            if ($payment_status === PaymentStatus::FAILED) {

                // for those things that needs to be marked as failed upon failed payment (cannot retry payment)
                switch ($payment_gateway_log->transactionLoggable?->getMorphClass()) {
                    case Enrollment::class:
                        $enrollment_service = resolve(EnrollmentService::class);
                        $enrollment_service->updateEnrollmentStatus($payment_gateway_log->transactionLoggable, EnrollmentStatus::PAYMENT_FAILED);

                        break;

                    case WalletTransaction::class:

                        // for wallet transaction, only allow 1 payment attempt per billing document
                        // void billing document if payment failed
                        $billing_document = $payment_gateway_log->billingDocument;

                        if ($billing_document !== null) {
                            $billing_document_service = BillingDocumentFactory::getInstance($billing_document->type);

                            $billing_document_service->setBillingDocument($billing_document)
                                ->changeStatusTo(BillingDocument::STATUS_VOIDED);
                        }

                        break;
                }

            }
        }

    }
}
