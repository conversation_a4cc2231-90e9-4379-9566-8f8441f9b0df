<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Models\LeaveApplicationType;
use App\Repositories\LeaveApplicationTypeRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class LeaveApplicationTypeService
{
    private LeaveApplicationTypeRepository $leaveApplicationTypeRepository;
    private LeaveApplicationType $leaveApplicationType;
    private array $name;
    private bool $isPresent;
    private float $averagePointDeduction;
    private float $conductPointDeduction;
    
    public function __construct(LeaveApplicationTypeRepository $leaveApplicationTypeRepository)
    {
        $this->leaveApplicationTypeRepository = $leaveApplicationTypeRepository;
    }

    public function getAllPaginatedLeaveApplicationTypes($filters = []): LengthAwarePaginator
    {
        return $this->leaveApplicationTypeRepository->getAllPaginated($filters);
    }

    public function getAllLeaveApplicationTypes($filters = []): Collection
    {
        return $this->leaveApplicationTypeRepository->getAll($filters);
    }

    public function setName(array $name): LeaveApplicationTypeService
    {
        $this->name = $name;
        return $this;
    }

    public function setIsPresent(bool $is_present): LeaveApplicationTypeService
    {
        $this->isPresent = $is_present;
        return $this;
    }

    public function setConductPointDeduction(float $conductPointDeduction): LeaveApplicationTypeService
    {
        $this->conductPointDeduction = $conductPointDeduction;
        return $this;
    }

    public function setAveragePointDeduction(float $averagePointDeduction): LeaveApplicationTypeService
    {
        $this->averagePointDeduction = $averagePointDeduction;
        return $this;
    }

    public function createLeaveApplicationType(): ?LeaveApplicationType
    {
        $leave_application_type_data = [
            'name' => $this->name,
            'is_present' => $this->isPresent,
            'average_point_deduction' => $this->averagePointDeduction,
            'conduct_point_deduction' => $this->conductPointDeduction,
        ];

        $this->leaveApplicationType =  $this->leaveApplicationTypeRepository->create($leave_application_type_data);
        return $this->leaveApplicationType;
    }

    public function updateLeaveApplicationType(): ?LeaveApplicationType
    {
        $leave_application_type_data = [
            'name' => $this->name,
            'is_present' => $this->isPresent,
            'average_point_deduction' => $this->averagePointDeduction,
            'conduct_point_deduction' => $this->conductPointDeduction,
        ];

        return $this->leaveApplicationTypeRepository->update($this->leaveApplicationType, $leave_application_type_data);
    }

    public function deleteLeaveApplicationType(): bool
    {
        if ( !$this->leaveApplicationType->canBeDeleted() ) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::LEAVE_APPLICATION_ERROR, 50001);
        }

        return $this->leaveApplicationTypeRepository->delete($this->leaveApplicationType);
    }

    public function getLeaveApplicationType(): LeaveApplicationType
    {
        return $this->leaveApplicationType;
    }

    public function setLeaveApplicationType(LeaveApplicationType $leave_application_type): LeaveApplicationTypeService
    {
        $this->leaveApplicationType = $leave_application_type;
        return $this;
    }
}