<?php

namespace App\Services\Report;

use App\Enums\ClassType;
use App\Enums\GuardianType;
use App\Enums\SortDirection;
use App\Repositories\SemesterClassRepository;
use App\Repositories\StudentRepository;
use App\Services\SemesterSettingService;
use Illuminate\Database\Eloquent\Builder;

class SemesterClassReportService
{
    public function __construct(
        protected StudentRepository $studentRepository,
        protected SemesterSettingService $semesterSettingService,
        protected SemesterClassRepository $semesterClassRepository
    ) {
    }

    public function getStudentContactsReportData(array $filters): array
    {
        $filters['includes'] = [
            'media',
            'guardians',
            'latestPrimaryClassBySemesterSettings.semesterClass.homeroomTeacher',
            'latestPrimaryClassBySemesterSettings.semesterClass.classModel',
        ];

        if (isset($filters['semester_class_id'])) {
            $filters['latest_semester_class_id'] = $filters['semester_class_id'];
            unset($filters['semester_class_id']);
        }

        if (isset($filters['semester_setting_id'])) {
            $filters['latest_class_semester_setting_id'] = $filters['semester_setting_id'];
            unset($filters['semester_setting_id']);
        }

        $filters['includes']['latestPrimaryClassBySemesterSettings'] = function ($query) use (&$filters) {
            $query->when(!empty($filters['latest_class_semester_setting_id']), function (Builder $query) use ($filters) {
                $query->where('semester_setting_id', $filters['latest_class_semester_setting_id']);
            })
                ->when(!empty($filters['latest_semester_class_id']), function (Builder $query) use ($filters) {
                    $query->where('semester_class_id', $filters['latest_semester_class_id']);
                });
        };

        $filters['order_by'] = ['id' => 'asc'];
        $students = $this->studentRepository->getAll($filters);

        $semester_setting = !empty($filters['latest_semester_class_id'])
            ? $this->semesterClassRepository->findOrFail($filters['latest_semester_class_id'])->semesterSetting
            : $this->semesterSettingService->firstById($filters['latest_class_semester_setting_id']);

        $data = [
            'semester_name' => $semester_setting->name,
            'classes' => []
        ];

        foreach ($students as $student) {
            $student_semester_class = $student->latestPrimaryClassBySemesterSettings[0];

            if (!isset($data['classes'][$student_semester_class->semester_class_id])) {
                $data['classes'][$student_semester_class->semester_class_id] = [
                    'class_name' => $student_semester_class->semesterClass->classModel->name,
                    'homeroom_teacher_name' => $student_semester_class->semesterClass->homeroomTeacher?->getFormattedTranslations('name'),
                    'students' => []
                ];
            }

            $media_url = $this->getMediaUrl($student->media);

            $student_data = [
                'photo' => $media_url,
                'seat_number' => $student_semester_class->seat_no,
                'student_number' => $student->student_number,
                'name' => $student->getFormattedTranslations('name'),
                'gender' => $student->gender->value,
                'nric' => $student->nric,
                'address' => $student->address,
                'guardians' => []
            ];

            foreach ($student->guardians as $guardian) {
                $student_data['guardians'][] = [
                    'name' => $guardian->getFormattedTranslations('name'),
                    'phone_number' => $guardian->phone_number,
                    'type' => $guardian->pivot->type
                ];
            }

            $data['classes'][$student_semester_class->semester_class_id]['students'][] = $student_data;
        }

        //sort by class id asc
        ksort($data['classes']);


        $data['classes'] = array_values($data['classes']);

        //sort by seat number
        foreach ($data['classes'] as &$class) {
            $class['students'] = collect($class['students'])->sortBy('seat_number')->toArray();
            $class['students'] = array_values($class['students']);
        }

        return $data;
    }

    public function getStudentDetailsReportData(array $filters): array
    {
        $filters['includes'] = [
            'primarySchool',
            'guardians.country',
            'guardians.religion',
            'guardians.education',
            'religion',
            'latestPrimaryClassBySemesterSettings.semesterClass.classModel',
            'media'
        ];

        $filters['latest_semester_class_id'] = $filters['semester_class_id'];
        unset($filters['semester_class_id']);

        $filters['includes']['latestPrimaryClassBySemesterSettings'] = function ($query) use ($filters) {
            $query->where('semester_class_id', $filters['latest_semester_class_id']);
            $query->with(['semesterClass.classModel']);
        };

        $filters['order_by'] = ['id'];

        $students = $this->studentRepository->getAll($filters);
        $response = [];

        foreach ($students as $student) {
            $media_url = $this->getMediaUrl($student->media);

            $data = [
                'photo' => $media_url,
                'name' => $student->getFormattedTranslations('name'),
                'student_number' => $student->student_number,
                'address' => $student->address,
                'class_name' => $student->latestPrimaryClassBySemesterSettings[0]->semesterClass->classModel->name,
                'semester_name' => $student->latestPrimaryClassBySemesterSettings[0]->semesterSetting->name,
                'primary_school_name' => $student->primarySchool?->name,
                'date_of_birth' => $student->date_of_birth,
                'birthplace' => $student->birthplace,
                'religion' => $student->religion->name,
                'guardians' => [
                    GuardianType::FATHER->value => [
                        'name' => null,
                        'nationality' => null,
                        'email' => null,
                        'education' => null,
                        'religion' => null,
                        'phone_number' => null,
                        'occupation' => null
                    ],
                    GuardianType::MOTHER->value => [
                        'name' => null,
                        'nationality' => null,
                        'email' => null,
                        'education' => null,
                        'religion' => null,
                        'phone_number' => null,
                        'occupation' => null
                    ],
                    GuardianType::GUARDIAN->value => [],
                ]
            ];

            foreach ($student->guardians as $guardian) {
                //it only have one father/mother but might have many guardians
                $guardian_data = [
                    'name' => $guardian->getFormattedTranslations('name'),
                    'nationality' => $guardian->country?->name,
                    'email' => $guardian->email,
                    'education' => $guardian->education?->name,
                    'religion' => $guardian->religion?->name,
                    'phone_number' => $guardian->phone_number,
                    'occupation' => $guardian->occupation,
                ];
                if ($guardian->pivot->type->value == GuardianType::GUARDIAN->value) {
                    $data['guardians'][$guardian->pivot->type->value][] = $guardian_data;
                } else {
                    $data['guardians'][$guardian->pivot->type->value] = $guardian_data;
                }
            }

            $response[] = $data;
        }

        return $response;
    }

    public function getReportHomeroomTeachers(array $filters): array
    {
        $filters['class_type'] = ClassType::PRIMARY;
        $filters['includes'] = ['classModel.grade', 'homeroomTeacher'];
        $filters['order_by'] = [
            'class' => [
                'name' => [
                    'en' => SortDirection::ASC->value,
                ]
            ],
        ];

        $homeroom_teachers = $this->semesterClassRepository->getAll($filters);

        return [
            'semester_name' => $this->semesterSettingService->firstById($filters['semester_setting_id'])?->name,
            'homeroom_teachers' => $homeroom_teachers
        ];
    }

    public function getAllStudentsBySemesterClassReportData(array $filters = []): mixed
    {
        $filters = [
            'latest_semester_class_id' => $filters['semester_class_id'],
        ];

        $filters['includes']['latestPrimaryClassBySemesterSettings'] = function ($query) use ($filters) {
            $query->where('semester_class_id', $filters['latest_semester_class_id']);
        };

        $data = $this->studentRepository->getAll($filters)->sortBy(function ($student) {
            // order by seat_no
            $seat_no = $student->latestPrimaryClassBySemesterSettings->first()->seat_no;

            return [$seat_no === null ? 1 : 0, $seat_no];
        })->values();

        $semester_class = (new SemesterClassRepository)
            ->getQuery(['includes' => ['homeroomTeacher', 'classModel', 'semesterSetting.semesterYearSetting']])
            ->find($filters['latest_semester_class_id']);

        return [
            'homeroom_teacher' => $semester_class->homeroomTeacher,
            'class_model' => $semester_class->classModel,
            'semester_setting' => $semester_class->semesterSetting,
            'students' => $data,
        ];
    }

    protected function getMediaUrl($student_media)
    {
        $media = $student_media->where('collection_name', 'photo')->first();

        $media_url = $media?->getUrl('thumb');

        if (!$media_url) {
            return null;
        }

        return $media_url;

//        try {
//            if (!file_get_contents($media_url)) {
//                return null;
//            }
//
//            return $media_url;
//        } catch (\Exception $e) {
//            return null;
//        }
    }
}
