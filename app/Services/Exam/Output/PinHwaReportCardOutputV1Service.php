<?php

namespace App\Services\Exam\Output;

use App\Enums\ClassType;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Enums\RewardPunishmentRecordStatus;
use App\Exceptions\BadExpressionException;
use App\Interfaces\IReportCardOutput;
use App\Models\CompetitionRecord;
use App\Models\LeadershipPositionRecord;
use App\Models\LeaveApplication;
use App\Models\PeriodAttendance;
use App\Models\PromotionMark;
use App\Models\ResultSourceSubject;
use App\Models\RewardPunishmentRecord;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentSocietyPosition;
use App\Services\ConductRecordService;
use DateTime;

class PinHwaReportCardOutputV1Service extends GeneralReportCardOutputService implements IReportCardOutput
{
    const array FORMULA = [
        'Gross Total' => [
            'value' => 'GETGROSSTOTAL',
            'args' => ["Result Source Code"]
        ],
        'Gross Weightage' => [
            'value' => 'GETGROSSWEIGHTAGE',
            'args' => ["Result Source Code"]
        ],
        'Gross Average' => [
            'value' => 'GETGROSSAVERAGE',
            'args' => ["Output Code"]
        ],
        'Marks Added' => [
            'value' => 'GETMARKSADDED',
            'args' => ["Semester Setting Code", "From Date", "To Date"]
        ],
        'Marks Subtracted' => [
            'value' => 'GETMARKSSUBTRACTED',
            'args' => ["From Date", "To Date"]
        ],
        'Conduct' => [
            'value' => 'GETCONDUCTSCORE',
            'args' => ["Semester Setting Code", "From Date", "To Date"]
        ],
        'Promotion Retention' => [
            'value' => 'GETPROMOTEDRETAINED',
            'args' => ["Semester Setting Code", "Output Code"]
        ],
        'Number of School Days' => [
            'value' => 'ROUND',
            'args' => ['Number of School Days', "Decimal Place"]
        ],
        'Class Positions' => [
            'value' => 'GETCLASSLEADERSHIPPOSITIONS',
            'args' => ['Semester Setting Code']
        ],
        'Society' => [
            'value' => 'GETSOCIETY',
            'args' => ['Semester Setting Code']
        ],
        'Absence' => [
            'value' => 'GETABSENCE',
            'args' => ['From Date', 'To Date']
        ],
        'Merit' => [
            'value' => 'GETMERIT',
            'args' => ['From Date', 'To Date']
        ],
        'Exceptional Performance' => [
            'value' => 'GETEXCEPTIONALPERFORMANCE',
            'args' => ['From Date', 'To Date']
        ],
        '+' => ['value' => '+'],
        '-' => ['value' => '-'],
        '*' => ['value' => '*'],
        '/' => ['value' => '/']
    ];

    protected ConductRecordService $conductRecordService;

    const array PERIOD_ATTENDANCE_MARK_CONFIG = [
        PeriodAttendanceStatus::ABSENT->value => 0.04,
        PeriodAttendanceStatus::LATE->value => 0.02
    ];

    public function __construct()
    {
        parent::__construct();
        $this->conductRecordService = app()->make(ConductRecordService::class);
    }

    public function customFunctionRouter($function_name, &$value)
    {
        switch ($function_name) {
            case 'getgrosstotal':
                $value = ['ref' => [$this, 'getGrossTotal'], 'arc' => 1];
                break;
            case 'getgrossweightage':
                $value = ['ref' => [$this, 'getGrossWeightage'], 'arc' => 1];
                break;
            case 'getgrossaverage':
                $value = ['ref' => [$this, 'getGrossAverage'], 'arc' => 1];
                break;
            case 'getmarksadded':
                $value = ['ref' => [$this, 'getMarksAdded'], 'arc' => 3];
                break;
            case 'getmarkssubtracted':
                $value = ['ref' => [$this, 'getMarksSubtracted'], 'arc' => 2];
                break;
            case 'getconductscore':
                $value = ['ref' => [$this, 'getConductScore'], 'arc' => 3];
                break;
            case 'getpromotedretained':
                $value = ['ref' => [$this, 'getPromotedRetained'], 'arc' => 2];
                break;
            case 'getclassleadershippositions':
                $value = ['ref' => [$this, 'getClassLeadershipPositions'], 'arc' => 1];
                break;
            case 'getsociety':
                $value = ['ref' => [$this, 'getSociety'], 'arc' => 1];
                break;
            case 'getabsence':
                $value = ['ref' => [$this, 'getAbsence'], 'arc' => 2];
                break;
            case 'getmerit':
                $value = ['ref' => [$this, 'getMerit'], 'arc' => 2];
                break;
            case 'getexceptionalperformance':
                $value = ['ref' => [$this, 'getExceptionalPerformance'], 'arc' => 2];
                break;
            default:
                throw new BadExpressionException("Unknown custom function '$function_name'");
        }
    }

    public function getGrossTotal(string $result_source_code)
    {
        if ($this->isValidation()) {
            return true;
        }

        $gross_total = ResultSourceSubject::where(['grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE])
            ->whereRelation('resultSource', 'code', $result_source_code)
            ->whereRelation('resultSource', 'student_grading_framework_id', $this->getStudentGradingFramework()->id)
            ->get()
            ->sum('weightedScore');

        return $gross_total;
    }

    public function getGrossWeightage(string $result_source_code)
    {
        if ($this->isValidation()) {
            return true;
        }

        $gross_weightage = ResultSourceSubject::where([
            'grading_type' => ResultSourceSubject::GRADING_TYPE_SCORE,
            'is_exempted' => false,
        ])
            ->whereRelation('resultSource', 'code', $result_source_code)
            ->whereRelation('resultSource', 'student_grading_framework_id', $this->getStudentGradingFramework()->id)
            ->get()
            ->sum('weightage_multiplier');

        return $gross_weightage;
    }

    public function getGrossAverage(string $output_code)
    {
        if ($this->isValidation()) {
            return true;
        }

        $gross_total = $this->getOutputValues()[$output_code]['GT']['total'];
        $gross_weightage = $this->getOutputValues()[$output_code]['GW']['total'];
        return round(($gross_total / $gross_weightage), 2);
    }

    public function getFloat(string $float)
    {
        if ($this->isValidation()) {
            return true;
        }

        return (float) $float;
    }

    public function getMarksAdded(string $semester_setting_code, string $date_from, string $date_to)
    {

        // total = competition_marks + conduct marks with scheme + avg_exam_marks (>0 only)
        if ($this->isValidation()) {
            return true;
        }

        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        $component = $this->getReportCardOutputComponent();

        // CompetitionRecord->Competition->date
        // competition marks
        $competition_marks = CompetitionRecord::where('student_id', $this->studentGradingFramework->student_id)
            ->whereHas('competition', function ($query) use ($date_from, $date_to){
                $query->whereBetween('date', [$date_from, $date_to]);
            })
            ->sum('mark');

        // conduct marks
        $conduct_marks = $this->conductRecordService->calculateConductMarks([
            'semester_setting_code' => $semester_setting_code,
            'student_id' => $this->studentGradingFramework->student_id,
            'date_from' => $date_from,
            'date_to' => $date_to
        ]);

        $conduct_grade = $this->gradingSchemeService
            ->setGradingScheme($component->gradingScheme)
            ->applyAndGetGrade($conduct_marks);

        // avg_exam_marks
        $reward_punishment_avg_exam_marks = RewardPunishmentRecord::query()
            ->where([
                'student_id' => $this->studentGradingFramework->student_id,
                'status' => RewardPunishmentRecordStatus::POSTED,
            ])
            ->where('average_exam_marks', '>', 0)
            ->whereBetween('date', [$date_from, $date_to])
            ->sum('average_exam_marks');

        $total_marks = round($competition_marks + $conduct_grade['extra_marks'] + $reward_punishment_avg_exam_marks, 2);

        return $total_marks;
    }

    public function getMarksSubtracted(string $date_from, string $date_to)
    {
        if ($this->isValidation()) {
            return true;
        }

        // total = attendance_marks + reward punishment avg_exam_marks (<0 only)
        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        // get marks deducted for attendance absent
        $period_attendances = PeriodAttendance::with('leaveApplication')
            ->where('student_id', $this->studentGradingFramework->student_id)
            ->whereIn('status', [PeriodAttendanceStatus::ABSENT, PeriodAttendanceStatus::LATE])
            ->whereBetween('date', [$date_from, $date_to])
            ->get();

        $attendance_marks = 0;

        $period_attendances->each(function ($period) use (&$attendance_marks) {
            $status = $period->status;
            $marks = self::PERIOD_ATTENDANCE_MARK_CONFIG[$status];

            if ($status === PeriodAttendanceStatus::ABSENT->value) {
                // if absent, we check whether got point deduction from leave application first, if none, then we deduct a hardcoded value 0.04
                $marks = $period->leaveApplication?->average_point_deduction ?? self::PERIOD_ATTENDANCE_MARK_CONFIG[$status];
            }

            $attendance_marks = $attendance_marks + $marks;
        });

        // reward punishment avg_exam_marks
        $reward_punishment_avg_exam_marks = RewardPunishmentRecord::query()
            ->where([
                'student_id' => $this->studentGradingFramework->student_id,
                'status' => RewardPunishmentRecordStatus::POSTED
            ])
            ->whereBetween('date', [$date_from, $date_to])
            ->where('average_exam_marks', '<', 0)
            ->sum('average_exam_marks');

        return round($attendance_marks + abs($reward_punishment_avg_exam_marks), 2);

    }

    public function getConductScore(string $semester_setting_code, string $date_from, string $date_to)
    {
        if ($this->isValidation()) {
            return true;
        }

        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        return $this->conductRecordService->calculateConductMarks([
            'student_id' => $this->studentGradingFramework->student_id,
            'semester_setting_code' => $semester_setting_code,
            'date_from' => $date_from,
            'date_to' => $date_to
        ], true);

    }

    public function getPromotedRetained(string $semester_setting_code, string $result_code)
    {
        if ($this->isValidation()) {
            return true;
        }

        // can remove result code if FINALEXAM is always going to be constant
        // must set priority to higher (later) than CONDUCT and SYS_NET_AVG
        $conduct_marks = $this->getOutputValues()[$result_code]['CONDUCT']['total'];
        $net_average = $this->getOutputValues()[$result_code]['SYS_NET_AVG']['total'];

        $primary_class = StudentClass::with('semesterClass.classModel')
            ->where([
                'student_id' => $this->studentGradingFramework->student->id,
                'class_type' => ClassType::PRIMARY->value,
                //'is_latest_class_in_semester' => true
            ])
            ->whereRelation('semesterSetting', 'code', $semester_setting_code)
            ->first();

        $promotion = PromotionMark::query()
            ->where('semester_class_id', $primary_class->semesterClass->id)
            ->first();

        if ($promotion === null) {
            return '';
        }

        if (bccomp($conduct_marks, $promotion->conduct_mark_for_promotion, 2) >= 0
            && bccomp($net_average, $promotion->net_average_for_promotion, 2) >= 0) {

            // TODO: really need to fix this after UAT, mb just use the promotion mark text way,  this is horrible
            return $primary_class->semesterClass->classModel->grade_id == 7 ? trans('exam.senior_promote', [], 'zh') : trans('exam.promote', [], 'zh');
        }

        // TODO: like really really horrible
        return $primary_class->semesterClass->classModel->grade_id == 7 ? trans('exam.senior_retained', [], 'zh') : trans('exam.retained', [], 'zh');
    }

    public function getClassLeadershipPositions(string $semester_setting_code)
    {

        if ($this->isValidation()) {
            return true;
        }

        // if got multiple, always return the first one only
        $leadership_position_record = LeadershipPositionRecord::with('leadershipPosition')
            ->where('student_id', $this->studentGradingFramework->student_id)
            ->whereRelation('semesterSetting', 'code', $semester_setting_code)
            ->orderBy('created_at', 'desc')
            ->first();

        return $leadership_position_record?->leadershipPosition?->name;

    }

    public function getSociety(string $semester_setting_code)
    {
        if ($this->isValidation()) {
            return true;
        }

        $student_class = StudentClass::with('semesterClass.classModel')
            ->where([
                'student_id' => $this->studentGradingFramework->student_id,
                'class_type' => ClassType::SOCIETY
            ])
            ->whereRelation('semesterSetting', 'code', $semester_setting_code)
            ->where('is_latest_class_in_semester', true)
            ->first();

        if (!isset($student_class)) {
            return null;
        }

        $society_position = StudentSocietyPosition::with('societyPosition')
            ->where([
                'student_id' => $this->studentGradingFramework->student_id,
                'semester_class_id' => $student_class->semester_class_id
            ])
            ->first();


        $society_name = $student_class->semesterClass->classModel->name;
        $society_position = isset($society_position) ? ' (' . $society_position->societyPosition->name . ')' : '';

        return $society_name . $society_position;
    }

    public function getAbsence(string $date_from, string $date_to)
    {
        if ($this->isValidation()) {
            return true;
        }

        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        $label = null;

        // Need attendance module
        $absent_periods = PeriodAttendance::query()
            ->where([
                'student_id' => $this->studentGradingFramework->student_id,
                'has_mark_deduction' => true
            ])
            ->whereIn('status', [PeriodAttendanceStatus::ABSENT, PeriodAttendanceStatus::LATE])
            ->whereNull('leave_application_id')
            ->whereBetween('date', [$date_from, $date_to])
            ->get();

        if ($absent_periods->count()) {
            $label = trans('exam.absent', [], 'zh') . ":" . $absent_periods->count() . trans('exam.period', [], 'zh');
        }

        $leave_applications = LeaveApplication::with("leaveApplicationType")
            ->where([
                'leave_applicable_type' => Student::class,
                'leave_applicable_id' => $this->studentGradingFramework->student_id,
                'status' => LeaveApplicationStatus::APPROVED->value,
            ])
            ->whereHas('leaveApplicationPeriods', function ($query) use ($date_from, $date_to) {
                $query->whereBetween('date', [$date_from, $date_to]);
            })
            ->withCount([
                'leaveApplicationPeriods' => function ($query) use ($date_from, $date_to) {
                    $query->whereBetween('date', [$date_from, $date_to]);
                }
            ])
            ->get();

        $leave_application_types = [];
        foreach ($leave_applications as $leave_application) {
            if (isset($leave_application_types[$leave_application->leave_application_type_id])) {
                $leave_application_types[$leave_application->leave_application_type_id]['count'] += $leave_application->leave_application_periods_count;
            } else {
                $leave_application_types[$leave_application->leave_application_type_id] = [
                    'count' => $leave_application->leave_application_periods_count,
                    'name' => $leave_application->leaveApplicationType->getTranslation('name', 'zh')
                ];
            }
        }

        foreach ($leave_application_types as $leave_application_type) {
            $to_be_added_label = null;

            if (!empty($label)) {
                $to_be_added_label .= "; ";
            }
            $to_be_added_label .= $leave_application_type['name'] . ":" . $leave_application_type['count'] . trans('exam.period', [], 'zh');

            if (mb_strlen($label) + mb_strlen($to_be_added_label) > 80) {
                break;
            }

            $label .= $to_be_added_label;
        }

        return $label;
    }

    public function getMerit(string $date_from, string $date_to)
    {
        if ($this->isValidation()) {
            return true;
        }

        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        $reward_punishment_records = RewardPunishmentRecord::with('rewardPunishment.meritDemeritSettings')
            ->where([
                'student_id' => $this->studentGradingFramework->student_id,
                'display_in_report_card' => true,
                'status' => RewardPunishmentRecordStatus::POSTED
            ])
            ->whereBetween('date', [$date_from, $date_to])
            ->orderBy('date', 'DESC')
            ->get();

        if ($reward_punishment_records->isEmpty()) {
            return null;
        }

        $reward_punishment_records->transform(function ($record) {
            $data = [
                'line_item' =>
                    $record->rewardPunishment->meritDemeritSettings->implode('name', ', ') . " (" . $record->rewardPunishment->name . ")"
            ];
            return $data;
        });

        $reward_punishment_records = $reward_punishment_records->filter(function ($item) {
            ;
            return mb_strlen($item['line_item']) <= 50;
        })->slice(0, 3);


        if ($reward_punishment_records->isEmpty()) {
            return trans('exam.reference', [], 'zh');
        }

        return $reward_punishment_records->implode('line_item', '; ');
    }

    public function getExceptionalPerformance($date_from, $date_to)
    {
        if ($this->isValidation()) {
            return true;
        }

        $date_from = $this->validateDate($date_from);
        $date_to = $this->validateDate($date_to);

        $competition_records = CompetitionRecord::query()
            ->join('competitions', 'competitions.id', '=', 'competition_records.competition_id')
            ->where('student_id', $this->studentGradingFramework->student_id)
            ->whereBetween('competitions.date', [$date_from, $date_to])
            ->orderBy('competitions.date', 'DESC')
            ->get();

        if ($competition_records->isEmpty()) {
            return null;
        }

        $competition_records = $competition_records->filter(function ($item) {
            return mb_strlen($item->competition->name) <= 50;
        })->slice(0, 3);

        // todo: Are we confirm to blindly drop competition name length > 50? even if student has some competition < 50 and some > 50?
        if ($competition_records->isEmpty()) {
            return trans('exam.reference', [], 'zh');
        }

        return $competition_records->implode('competition.name', '; ');
    }

    public function validateDate(string $date_string)
    {

        $date = DateTime::createFromFormat("Y-m-d", $date_string);
        if (!$date) {
            throw new BadExpressionException('Not in date format:' . $date);
        }
        return $date;
    }
}
