<?php

namespace App\Services;

use App\Enums\DietaryRestriction;
use App\Enums\EnrollmentPaymentStatus;
use App\Enums\EnrollmentStatus;
use App\Enums\GuardianType;
use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\SchoolLevel;
use App\Imports\EnrollmentPostPaymentImport;
use App\Imports\EnrollmentPrePaymentImport;
use App\Jobs\NotifyGuardianOfEnrollmentStatusJob;
use App\Jobs\RenotifyGuardianOfEnrollmentStatusJob;
use App\Models\BillingDocument;
use App\Models\Enrollment;
use App\Models\EnrollmentSession;
use App\Models\EnrollmentUser;
use App\Models\PaymentGatewayLog;
use App\Models\PaymentMethod;
use App\Models\Religion;
use App\Repositories\EnrollmentExamMarkRepository;
use App\Repositories\EnrollmentExamRepository;
use App\Repositories\EnrollmentGuardianRepository;
use App\Repositories\EnrollmentRepository;
use App\Repositories\EnrollmentSessionRepository;
use App\Repositories\EnrollmentUserRepository;
use App\Repositories\HealthConcernRepository;
use App\Repositories\ReligionRepository;
use App\Repositories\SchoolRepository;
use App\Repositories\StudentRepository;
use App\Services\Billing\BillingDocumentService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Propaganistas\LaravelPhone\PhoneNumber;

class EnrollmentService
{
    private EnrollmentRepository $enrollmentRepository;
    private EnrollmentSessionRepository $enrollmentSessionRepository;
    private EnrollmentGuardianRepository $enrollmentGuardianRepository;
    private ReligionRepository $religionRepository;
    private HealthConcernRepository $healthConcernRepository;
    private StudentRepository $studentRepository;
    private EnrollmentExamRepository $enrollmentExamRepository;
    private EnrollmentExamMarkRepository $enrollmentExamMarkRepository;
    private SchoolRepository $schoolRepository;
    private PaymentGatewayLogService $paymentGatewayLogService;
    private PaymentGatewayService $paymentGatewayService;
    private array $data;
    private array $toBeCreatedReligions = [];
    private array $toBeCreatedPrimarySchools = [];
    private array $toBeCreatedHealthConcerns = [];
    private array $enrollmentByNricMap = [];
    private array $enrollmentByPassportMap = [];
    private ?string $returnUrl = null;
    private EnrollmentSession $enrollmentSession;
    private UploadedFile $importFile;
    private int $importErrorCount = 0;
    private int $importWarningCount = 0;
    private EnrollmentUser $enrollmentUser;
    private BillingDocument $billingDocument;
    private Enrollment $enrollment;
    private const AVAILABLE_LOCALES = ['en', 'zh'];

    public function __construct(
        EnrollmentRepository $enrollment_repository,
        EnrollmentSessionRepository $enrollment_session_repository,
        EnrollmentGuardianRepository $enrollment_guardian_repository,
        ReligionRepository $religion_repository,
        HealthConcernRepository $health_concern_repository,
        StudentRepository $student_repository,
        EnrollmentExamRepository $enrollment_exam_repository,
        SchoolRepository $school_repository,
        EnrollmentExamMarkRepository $enrollment_exam_mark_repository,
        PaymentGatewayLogService $payment_gateway_log_service,
        PaymentGatewayService $payment_gateway_service,
    ) {
        $this->enrollmentRepository = $enrollment_repository;
        $this->enrollmentSessionRepository = $enrollment_session_repository;
        $this->enrollmentGuardianRepository = $enrollment_guardian_repository;
        $this->religionRepository = $religion_repository;
        $this->healthConcernRepository = $health_concern_repository;
        $this->studentRepository = $student_repository;
        $this->enrollmentExamRepository = $enrollment_exam_repository;
        $this->schoolRepository = $school_repository;
        $this->enrollmentExamMarkRepository = $enrollment_exam_mark_repository;
        $this->paymentGatewayLogService = $payment_gateway_log_service;
        $this->paymentGatewayService = $payment_gateway_service;
    }

    public function setData(array $data): self
    {
        $this->data = $data;
        return $this;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function setImportFile(UploadedFile $import_file): self
    {
        $this->importFile = $import_file;
        return $this;
    }

    public function getImportFile(): UploadedFile
    {
        return $this->importFile;
    }

    public function setEnrollmentSession(EnrollmentSession $enrollment_session): self
    {
        $this->enrollmentSession = $enrollment_session;
        return $this;
    }

    public function getEnrollmentSession(): EnrollmentSession
    {
        return $this->enrollmentSession;
    }

    public function setEnrollmentByNricMap(array $enrollment_by_nric_map): self
    {
        $this->enrollmentByNricMap = $enrollment_by_nric_map;
        return $this;
    }

    public function getEnrollmentByNricMap(): array
    {
        return $this->enrollmentByNricMap;
    }

    public function setEnrollmentByPassportMap(array $enrollment_by_passport_map): self
    {
        $this->enrollmentByPassportMap = $enrollment_by_passport_map;
        return $this;
    }

    public function getEnrollmentByPassportMap(): array
    {
        return $this->enrollmentByPassportMap;
    }

    public function setEnrollmentUser(EnrollmentUser $enrollment_user): self
    {
        $this->enrollmentUser = $enrollment_user;
        return $this;
    }

    public function getEnrollmentUser(): EnrollmentUser
    {
        return $this->enrollmentUser;
    }

    public function setBillingDocument(BillingDocument $billing_document): self
    {
        $this->billingDocument = $billing_document;
        return $this;
    }

    public function getBillingDocument(): BillingDocument
    {
        return $this->billingDocument;
    }

    public function setEnrollment(Enrollment $enrollment): self
    {
        $this->enrollment = $enrollment;
        return $this;
    }

    public function getEnrollment(): Enrollment
    {
        return $this->enrollment;
    }

    public function setToBeCreatedReligions(array $to_be_created_religions): self
    {
        $this->toBeCreatedReligions = $to_be_created_religions;
        return $this;
    }

    public function getToBeCreatedReligions(): array
    {
        return $this->toBeCreatedReligions;
    }

    public function setToBeCreatedPrimarySchools(array $to_be_created_primary_schools): self
    {
        $this->toBeCreatedPrimarySchools = $to_be_created_primary_schools;
        return $this;
    }

    public function getToBeCreatedPrimarySchools(): array
    {
        return $this->toBeCreatedPrimarySchools;
    }

    public function setToBeCreatedHealthConcerns(array $to_be_created_health_concerns): self
    {
        $this->toBeCreatedHealthConcerns = $to_be_created_health_concerns;
        return $this;
    }

    public function getToBeCreatedHealthConcerns(): array
    {
        return $this->toBeCreatedHealthConcerns;
    }

    public function setImportErrorCount(int $import_error_count): self
    {
        $this->importErrorCount = $import_error_count;
        return $this;
    }

    public function getImportErrorCount(): int
    {
        return $this->importErrorCount;
    }

    public function setImportWarningCount(int $import_warning_count): self
    {
        $this->importWarningCount = $import_warning_count;
        return $this;
    }

    public function getImportWarningCount(): int
    {
        return $this->importWarningCount;
    }

    public function getAllPaginatedEnrollments($filters = []): LengthAwarePaginator
    {
        return $this->enrollmentRepository->getAllPaginated($filters);
    }

    public function getAllEnrollments($filters = []): Collection
    {
        return $this->enrollmentRepository->getAll($filters);
    }

    public function setReturnUrl(?string $return_url): self
    {
        $this->returnUrl = $return_url;
        return $this;
    }

    public function delete(Enrollment $enrollment): bool
    {
        $enrollment->canBeDeleted();

        return DB::transaction(function () use ($enrollment) {

            // delete enrollment exams
            $enrollment->enrollmentExams()->each(function ($exam) {
                $exam->examMarks()->delete();
                $exam->delete();
            });

            // // will not delete enrollmentUser because this user might have other enrollments
            // $enrollment->enrollmentUser()->delete();

            // delete guardians
            $enrollment->guardians()->delete();

            // delete enrollment
            return $this->enrollmentRepository->delete($enrollment);
        });

    }

    public function update(Enrollment $enrollment, array $data, bool $is_admin = false): ?Enrollment
    {
        $payload = $this->prepareUpdatePayload($data, $is_admin);

        return DB::transaction(function () use ($enrollment, $payload, $data) {
            /** @var Enrollment */

            $enrollment = $this->enrollmentRepository->update($enrollment, $payload);

            if (isset($data['guardians'])) {
                $enrollment->guardians()->delete();

                $guardians_payload = [];

                foreach ($data['guardians'] as $guardian) {
                    $guardians_payload[] = [
                        'enrollment_id' => $enrollment->id,
                        'name' => json_encode($guardian['name']),
                        'email' => $guardian['email'] ?? null,
                        'phone_number' => $guardian['phone_number'],
                        'nric' => $guardian['nric'] ?? null,
                        'passport_number' => $guardian['passport_number'] ?? null,
                        'nationality_id' => $guardian['nationality_id'] ?? null,
                        'race_id' => $guardian['race_id'] ?? null,
                        'religion_id' => $guardian['religion_id'] ?? null,
                        'education_id' => $guardian['education_id'] ?? null,
                        'married_status' => $guardian['married_status'] ?? null,
                        'live_status' => $guardian['live_status'] ?? null,
                        'occupation' => $guardian['occupation'] ?? null,
                        'occupation_description' => $guardian['occupation_description'] ?? null,
                        'remarks' => $guardian['remarks'] ?? null,
                        'is_primary' => $guardian['is_primary'] ?? false,
                        'guardian_type' => $guardian['guardian_type'] ?? 'GUARDIAN',
                        'created_at' => now()->toDateTimeString(),
                        'updated_at' => now()->toDateTimeString(),
                    ];
                }

                $this->enrollmentGuardianRepository->insert($guardians_payload);
            }

            if (isset($data['enrollment_exams'])) {

                $enrollment->loadMissing(['enrollmentExams.examMarks']);

                $existing_exams = $enrollment->enrollmentExams->keyBy('id');

                foreach ($data['enrollment_exams'] as $exam_payload) {

                    $existing_exam = $existing_exams[$exam_payload['exam_id']] ?? null;

                    // update existing exam
                    if ($existing_exam) {
                        // dont allow updating exam slip number
                        // $existing_exam->exam_slip_number = $exam_payload['exam_slip_number'];
                        $existing_exam->total_average = $exam_payload['total_average'];
                        $existing_exam->save();

                        $existing_exam_marks = $existing_exam->examMarks->keyBy('id');

                        foreach ($exam_payload['marks'] as $mark_payload) {
                            $existing_mark = $existing_exam_marks[$mark_payload['exam_mark_id']] ?? null;

                            if ($existing_mark) {
                                // update existing mark
                                $existing_mark->mark = $mark_payload['mark'];
                                $existing_mark->save();
                            }
                        }
                    }
                }
            }

            return $enrollment->refresh();
        });
    }

    public function extendExpiry(Enrollment $enrollment, Carbon $date): ?Enrollment
    {
        return $this->enrollmentRepository->update($enrollment, [
            'expiry_date' => $date->toDateString(),
        ]);
    }

    public function getTemplateData(int $enrollment_session_id, bool $post_payment): array
    {
        $enrollment_session = $this->enrollmentSessionRepository->first([
            'includes' => ['examSubjects'],
            'id' => $enrollment_session_id,
        ], false);

        if (!$enrollment_session) {
            return [];
        }

        if ($post_payment) {
            // post payment template has lesser data
            $data = [
                'number' => '1',
                'exam_slip_number' => 'R6566 - SAMPLEDATA',
                'student_name_en' => 'John Doe - SAMPLEDATA',
                'nric' => '110113101982 - SAMPLEDATA',
                'passport_number' => 'A1234567 - SAMPLEDATA',
                'hostel' => 'TRUE - SAMPLEDATA',
                'total_average' => '80.05',
                'status' => 'APPROVED - SAMPLEDATA',
            ];
        } else {
            $data = [
                'number' => '1',
                'exam_slip_number' => 'R6566 - SAMPLEDATA',
                'student_name_en' => 'John Doe - SAMPLEDATA',
                'student_name_zh' => '约翰·多 - SAMPLEDATA',
                'nric' => '110113101982 - SAMPLEDATA',
                'passport_number' => 'A1234567 - SAMPLEDATA',
                'religion' => 'Christian - SAMPLEDATA',
                'gender' => 'MALE - SAMPLEDATA',
                'guardian_phone_number' => '0123456789 - SAMPLEDATA',
                'guardian_email' => '<EMAIL> - SAMPLEDATA',
                'guardian_name' => 'Jane Doe - SAMPLEDATA',
                'guardian_type' => 'MOTHER - SAMPLEDATA',
                'total_average' => '80.05',
                'status' => 'APPROVED - SAMPLEDATA',
                'address' => '123 Sample Street - SAMPLEDATA',
                'primary_school' => 'Pin Hwa - SAMPLEDATA',
                'hostel' => 'TRUE - SAMPLEDATA',
                'have_siblings' => 'FALSE - SAMPLEDATA',
                'dietary_restriction' => 'NONE - SAMPLEDATA',
                'health_concern' => 'NONE - SAMPLEDATA',
                'foreigner' => 'FALSE - SAMPLEDATA',
                'conduct' => 'A+ - SAMPLEDATA',
                'remarks' => 'remarks - SAMPLEDATA',
                'register_date' => '2021/01/01 - SAMPLEDATA',
                'expiry_date' => '2021/01/01 - SAMPLEDATA',
            ];
        }

        $subject_lists = [];

        foreach ($enrollment_session->getExamSubjectsSortedBySubjectCode() as $subject) {
            $data[$subject->code] = '80.05';

            $subject_lists[$subject->code] = $subject;
        }

        return [
            'data' => [$data],
            'subject_lists' => $subject_lists,
        ];
    }

    /**
     * @throws \Exception
     */
    public function transformExcelToCollection(): self
    {
        if (!isset($this->importFile) && !isset($this->enrollmentSession)) {
            throw new \Exception('Import file not set');
        }

        $enrollment_session = $this->enrollmentSession->loadMissing(['examSubjects']);

        $data = Excel::toCollection(new EnrollmentPrePaymentImport($enrollment_session), $this->importFile)->first();

        $this->setData($data->toArray());

        return $this;
    }

    /**
     * @throws \Exception
     */
    public function transformPostPaymentExcelToCollection(): self
    {
        if (!isset($this->importFile) && !isset($this->enrollmentSession)) {
            throw new \Exception('Import file not set');
        }

        $enrollment_session = $this->enrollmentSession->loadMissing(['examSubjects']);

        $data = Excel::toCollection(new EnrollmentPostPaymentImport($enrollment_session), $this->importFile)->first();

        $this->setData($data->toArray());

        return $this;
    }

    public function validateBaseForImport()
    {
        /**
         * validate all fields are required except remarks
         * validate either nric or passport_number must be filled
         * validate status is only [SHORLISTED, REJECTED, ACCEPTED]
         * validate nric is not duplicated in all rows
         * validate nric is 12 digits
         * validate passport_number is not duplicated in all rows
         * validate hostel is boolean
         * validate exam_slip_number is not duplicated in all rows
         * validate email format is valid
         *
         */
        $data = $this->getData();

        $nric_numbers = [];
        $passport_numbers = [];
        $exam_slip_numbers = [];

        $optional_fields = [
            'remarks',
            'nric',
            'passport_number',
            'hostel',
            'have_siblings',
            'foreigner',
            'exam_slip_number',
            'register_date',
            'expiry_date',
            'total_average', // total_average is now optional - 05 June 2025
        ];

        // subject codes are optional fields
        $enrollment_session = $this->getEnrollmentSession();
        $subject_codes = $enrollment_session->examSubjects->pluck('code')->toArray();

        $optional_fields = array_merge($subject_codes, $optional_fields);

        foreach ($data as $data_index => $row) {
            // all fields are required except remarks
            foreach ($row as $key => $value) {
                if (in_array($key, $optional_fields)) {
                    continue;
                }

                if (empty($value)) {
                    $this->importErrorCount++;

                    $formatted_key = ucwords(str_replace('_', ' ', $key));

                    $data[$data_index]['errors'][$key][] = __('validation.required', [
                        'attribute' => $formatted_key,
                    ]);
                }
            }

            // either nric or passport_number must be filled
            if (empty($row['nric']) && empty($row['passport_number'])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['nric'][] = __('system_error.5003');
            }

            // nric is not duplicated in all rows
            if (isset($nric_numbers[$row['nric']])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['nric'][] = __('system_error.5005');
            }

            // nric is 12 digits
            if (!empty($row['nric']) && !preg_match('/^\d{12}$/', $row['nric'])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['nric'][] = __('system_error.5006');
            }

            // passport_number is not duplicated in all rows
            if (isset($passport_numbers[$row['passport_number']])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['passport_number'][] = __('system_error.5007');
            }

            // hostel is boolean
            if (!is_bool($row['hostel'])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['hostel'][] = __('system_error.5008');
            }

            // exam_slip_number is not duplicated in all rows
            if (empty($row['exam_slip_number'])) {
                $this->importWarningCount++;
                $data[$data_index]['warnings']['exam_slip_number'][] = __('system_error.5026');
            } elseif (isset($exam_slip_numbers[$row['exam_slip_number']])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['exam_slip_number'][] = __('system_error.5009');
            }

            // validate email format
            if (!empty($row['guardian_email']) && !filter_var($row['guardian_email'], FILTER_VALIDATE_EMAIL)) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['guardian_email'][] = __('validation.email', [
                    'attribute' => 'guardian email',
                ]);
            }

            $exam_slip_numbers[$row['exam_slip_number']] = 1;

            if (!empty($row['nric'])) {
                $nric_numbers[$row['nric']] = 1;
            }

            if (!empty($row['passport_number'])) {
                $passport_numbers[$row['passport_number']] = 1;
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validatePrePaymentInfoForImport(): self
    {
        /**
         * validate guardian type must only be [GUARDIAN, FATHER, MOTHER]
         * validate have_siblings is boolean
         * validate dietary_restriction is DietaryRestriction enum
         * validate foreigner is boolean
         */

        $data = $this->getData();

        foreach ($data as $data_index => $row) {
            if (!in_array($row['guardian_type'], [GuardianType::GUARDIAN->value, GuardianType::FATHER->value, GuardianType::MOTHER->value])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['guardian_type'][] = __('system_error.5010');
            }

            if (!is_bool($row['have_siblings'])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['have_siblings'][] = __('system_error.5011');
            }

            $dietary_restrictions = DietaryRestriction::values();
            if (!in_array($row['dietary_restriction'], $dietary_restrictions)) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['dietary_restriction'][] = __('system_error.5012', ['dietary_restrictions' => implode(', ', $dietary_restrictions)]);
            }

            if (!is_bool($row['foreigner'])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['foreigner'][] = __('system_error.5013');
            }

            if (empty($row['register_date'])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['register_date'][] = __('validation.required', [
                    'attribute' => 'register date',
                ]);
            } else {
                try {
                    $data[$data_index]['register_date'] = Carbon::parse($row['register_date'])->toDateString();
                } catch (\Throwable $e) {
                    $this->importErrorCount++;
                    $data[$data_index]['errors']['register_date'][] = __('validation.date', [
                        'attribute' => 'register date',
                    ]);
                }
            }

            if (empty($row['expiry_date'])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['expiry_date'][] = __('validation.required', [
                    'attribute' => 'expiry date',
                ]);
            } else {
                try {
                    $data[$data_index]['expiry_date'] = Carbon::parse($row['expiry_date'])->toDateString();
                } catch (\Throwable $e) {
                    $this->importErrorCount++;
                    $data[$data_index]['errors']['expiry_date'][] = __('validation.date', [
                        'attribute' => 'expiry date',
                    ]);
                }
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validateReligionForImport(): self
    {
        /**
         * religion must exist in DB, if religion doesnt exist then new religion will be created (master_religions)
         */

        $data = $this->getData();

        $collection = collect($data);

        $payload_religions = $collection->pluck('religion')->unique();

        $existing_religions = $this->religionRepository->getByNames($payload_religions->toArray());

        $religion_map = $this->extractAllTranslatableName($existing_religions);

        foreach ($data as $data_index => $row) {
            $religion = $row['religion'];

            if (!isset($religion_map[$religion])) {
                $this->importWarningCount++;
                $data[$data_index]['warnings']['religion'][] = __('system_error.5014', ['key' => 'Religion', 'value' => $religion]);

                if (!in_array($religion, $this->toBeCreatedReligions)) {
                    $this->toBeCreatedReligions[] = $religion;
                }
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validateNRICForImport(): self
    {
        /**
         * nric must not be already exist in DB
         */

        $data = $this->getData();

        $nric_payload = collect($data)->pluck('nric')->unique()->toArray();

        $existing_students = $this->studentRepository->getAll([
            'nric' => $nric_payload,
        ])->keyBy('nric');

        $existing_enrollments = $this->enrollmentRepository->getPartialEnrollments([
            'nric' => $nric_payload,
            'enrollment_session_id' => $this->getEnrollmentSession()->id,
        ])->keyBy('nric');

        foreach ($data as $data_index => $row) {
            $nric = $row['nric'];

            if (isset($existing_students[$nric])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['nric'][] = __('system_error.5015', ['key' => 'NRIC', 'value' => $nric]);
            }

            if (isset($existing_enrollments[$nric])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['nric'][] = __('system_error.5027', ['attribute' => 'NRIC']);
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validatePassportNumberForImport(): self
    {
        /**
         * passport_number must not be already exist in DB
         */
        $data = $this->getData();

        $passport_number_payload = collect($data)->pluck('passport_number')->unique()->toArray();

        $existing_students = $this->studentRepository->getAll([
            'passport_number' => $passport_number_payload,
        ])->keyBy('passport_number');

        $existing_enrollments = $this->enrollmentRepository->getPartialEnrollments([
            'passport_number' => $passport_number_payload,
            'enrollment_session_id' => $this->getEnrollmentSession()->id,
        ])->keyBy('passport_number');

        foreach ($data as $data_index => $row) {
            $passport_number = $row['passport_number'];

            if (isset($existing_students[$passport_number])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['passport_number'][] = __('system_error.5015', ['key' => 'Passport number', 'value' => $passport_number]);
            }

            if (isset($existing_enrollments[$passport_number])) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['passport_number'][] = __('system_error.5027', ['attribute' => 'Passport Number']);
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validatePrimarySchoolForImport(): self
    {
        /**
         * primary_school must exist in DB, if primary_school doesnt exist then new primary_school will be created (master_schools)
         */

        $data = $this->getData();

        $collection = collect($data);

        $payload_primary_schools = $collection->pluck('primary_school')->unique();

        $existing_primary_schools = $this->schoolRepository->getByNames($payload_primary_schools->toArray());

        $primary_school_map = $this->extractAllTranslatableName($existing_primary_schools);

        foreach ($data as $data_index => $row) {
            $primary_school = $row['primary_school'];

            if (!isset($primary_school_map[$primary_school])) {
                $this->importWarningCount++;
                $data[$data_index]['warnings']['primary_school'][] = __('system_error.5014', ['key' => 'School', 'value' => $primary_school]);

                if (!in_array($primary_school, $this->toBeCreatedPrimarySchools)) {
                    $this->toBeCreatedPrimarySchools[] = $primary_school;
                }
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validateHealthConcernForImport(): self
    {
        /**
         * health_concern must exist in DB, if health_concern doesnt exist then new health_concern will be created (master_health_concerns)
         */

        $data = $this->getData();

        $collection = collect($data);

        $payload_health_concerns = $collection->pluck('health_concern')->unique();

        $existing_health_concerns = $this->healthConcernRepository->getByNames($payload_health_concerns->toArray());

        $health_concern_map = $this->extractAllTranslatableName($existing_health_concerns);

        foreach ($data as $data_index => $row) {
            $health_concern = $row['health_concern'];

            if (!isset($health_concern_map[$health_concern])) {
                $this->importWarningCount++;
                $data[$data_index]['warnings']['health_concern'][] = __('system_error.5014', ['key' => 'Health concern', 'value' => $health_concern]);

                if (!in_array($health_concern, $this->toBeCreatedHealthConcerns)) {
                    $this->toBeCreatedHealthConcerns[] = $health_concern;
                }
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validateSubjectForImport(): self
    {
        /**
         * subjects->code in enrollment_session must exist in the import file
         */

        $enrollment_session = $this->getEnrollmentSession()->loadMissing(['examSubjects']);

        $data = $this->getData();

        foreach ($data as $data_index => $row) {
            // check if all subjects are present in each row
            foreach ($enrollment_session->examSubjects as $subject) {
                if (!isset($row[$subject->code])) {
                    $this->importErrorCount++;
                    $data[$data_index]['errors'][$subject->code][] = __('system_error.5016', ['code' => $subject->code]);
                }
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validatePhoneNumberForImport(): self
    {
        $data = $this->getData();

        foreach ($data as $data_index => $row) {
            $phone_number_payload = $row['guardian_phone_number'];

            // if phone_number contains +
            if (strpos($phone_number_payload, '+') !== false) {
                $is_valid = (new PhoneNumber($phone_number_payload))->isValid();

                if (!$is_valid) {
                    $this->importErrorCount++;
                    $data[$data_index]['errors']['guardian_phone_number'][] = __('system_error.5018');
                } else {
                    // reassign the phone number back to the data
                    $data[$data_index]['guardian_phone_number'] = (new PhoneNumber($phone_number_payload))->formatE164();
                }
            } else {
                // if phone_number does not contain +, then it might be a local phone number, will format it to E164 format
                try {
                    $formatted_phone_number = (new PhoneNumber($phone_number_payload, 'MY'))->formatE164();

                    // reassign the formatted phone number back to the data
                    $data[$data_index]['guardian_phone_number'] = $formatted_phone_number;
                } catch (\Throwable $th) {
                    $this->importErrorCount++;
                    $data[$data_index]['errors']['guardian_phone_number'][] = __('system_error.5019');
                }
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validatePhoneNumberAndEmailUniquenessForImport(): self
    {
        $data = $this->getData();

        $guardians = (new EnrollmentUserRepository)->getAll();

        $guardian_by_phone_number = $guardians->keyBy('phone_number');
        $guardian_by_email = $guardians->keyBy('email');

        foreach ($data as $data_index => $row) {
            $phone_number_payload = $row['guardian_phone_number'];
            $email_payload = $row['guardian_email'];

            // if there is existing guardian with the same phone number but different email payload
            if (
                isset($guardian_by_phone_number[$phone_number_payload]) &&
                $guardian_by_phone_number[$phone_number_payload]->email !== $email_payload
            ) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['guardian_phone_number'][] = __('system_error.5020');
            }

            // if there is existing guardian with the same email but different phone number payload
            if (
                isset($guardian_by_email[$email_payload]) &&
                $guardian_by_email[$email_payload]->phone_number !== $phone_number_payload
            ) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['guardian_email'][] = __('system_error.5020');
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validateEnrollmentFromDatabaseForImport(): self
    {
        $data = $this->getData();

        $map = $this->fetchEnrollmentsMapping();
        $enrollment_by_nric_map = $map['enrollment_by_nric_map'];
        $enrollment_by_passport_map = $map['enrollment_by_passport_map'];

        foreach ($data as $data_index => $row) {
            $nric_payload = $row['nric'];
            $passport_number_payload = $row['passport_number'];

            // both NRIC and passport are provided in import data but dont exist in the database
            if (
                !empty($nric_payload) &&
                !empty($passport_number_payload) &&
                !isset($enrollment_by_nric_map[$nric_payload]) &&
                !isset($enrollment_by_passport_map[$passport_number_payload])
            ) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['nric'][] = __('system_error.5023', ['nric' => $nric_payload, 'passport_number' => $passport_number_payload]);
            } // only NRIC is provided
            else {
                if (
                    !empty($nric_payload) &&
                    !isset($enrollment_by_nric_map[$nric_payload])
                ) {
                    $this->importErrorCount++;
                    $data[$data_index]['errors']['nric'][] = __('system_error.5024', ['nric' => $nric_payload]);
                } // only passport_number is provided
                else {
                    if (
                        !empty($passport_number_payload) &&
                        !isset($enrollment_by_passport_map[$passport_number_payload])
                    ) {
                        $this->importErrorCount++;
                        $data[$data_index]['errors']['passport_number'][] = __('system_error.5025', ['passport_number' => $passport_number_payload]);
                    }
                }
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validateStatusForImport(bool $is_pre_payment = true): self
    {
        /**
         * validate STATUS for import
         *  - if pre-payment import, validate status is only [APPROVED]
         *  - if post-payment import, validate status is only [REJECTED, APPROVED]
         */
        $data = $this->getData();

        $statuses = $is_pre_payment
            ? [EnrollmentStatus::APPROVED->value]
            : [EnrollmentStatus::REJECTED->value, EnrollmentStatus::APPROVED->value];

        foreach ($data as $data_index => $row) {
            if (!in_array($row['status'], $statuses)) {
                $this->importErrorCount++;
                $data[$data_index]['errors']['status'][] = __('system_error.5004', ['attributes' => join(', ', $statuses)]);
            }
        }

        $this->setData($data);

        return $this;
    }

    public function validatePrepaymentImport(): self
    {
        $this->validateBaseForImport()
            ->validateStatusForImport(true)
            ->validatePrePaymentInfoForImport()
            ->validateReligionForImport()
            ->validateNRICForImport()
            ->validatePassportNumberForImport()
            ->validatePrimarySchoolForImport()
            ->validateHealthConcernForImport()
            // ->validateSubjectForImport() // no need to validate subject as required : 05 June 2025
            ->validatePhoneNumberForImport()
            ->validatePhoneNumberAndEmailUniquenessForImport();

        return $this;
    }

    public function validatePostPaymentImport(): self
    {
        $this->validateBaseForImport()
            ->validateStatusForImport(false)
            // ->validateSubjectForImport() // no need to validate subject as required : 05 June 2025
            ->validateEnrollmentFromDatabaseForImport();

        return $this;
    }

    public function getValidatedData(): array
    {
        return [
            'data' => $this->getData(),
            'subject_lists' => $this->getEnrollmentSession()->getExamSubjectsSortedBySubjectCode()->keyBy('code'),
            'error_count' => $this->getImportErrorCount(),
            'warning_count' => $this->getImportWarningCount(),
        ];
    }

    public function savePrePaymentImportedData(): self
    {
        if ($this->getImportErrorCount() > 0) {
            return $this;
        }

        $notification_jobs = [];

        DB::transaction(function () use (&$notification_jobs) {

            $data = $this->getData();

            $subjects = $this->getEnrollmentSession()->examSubjects->keyBy('code');

            // create bulk religions, primary schools, health concerns
            $this->createReligionInBulk();
            $this->createPrimarySchoolInBulk();
            $this->createHealthConcernInBulk();

            $map = $this->fetchAllMapping();

            $religion_map = $map['religion_map'];
            $health_concern_map = $map['health_concern_map'];
            $primary_school_map = $map['primary_school_map'];
            $guardian_by_phone_map = $map['guardian_by_phone_map'];
            $guardian_by_email_map = $map['guardian_by_email_map'];

            $all_marks = [];

            foreach ($data as $row) {
                $religion_in_db = $religion_map[$row['religion']] ?? null;
                $health_concern_in_db = $health_concern_map[$row['health_concern']] ?? null;
                $primary_school_in_db = $primary_school_map[$row['primary_school']] ?? null;

                $enrollment = $this->enrollmentRepository->create([
                    'name' => [
                        'en' => mb_strtoupper($row['student_name_en']),
                        'zh' => $row['student_name_zh'],
                    ],
                    'nric' => $row['nric'],
                    'passport_number' => $row['passport_number'],
                    'gender' => $row['gender'],
                    'address' => $row['address'],
                    'is_hostel' => $row['hostel'],
                    'have_siblings' => $row['have_siblings'],
                    'dietary_restriction' => $row['dietary_restriction'],
                    'is_foreigner' => $row['foreigner'],
                    'conduct' => $row['conduct'],
                    'remarks' => $row['remarks'] ?? null,
                    'enrollment_status' => $row['status'],
                    'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
                    'token' => Str::uuid()->toString(),
                    'enrollment_session_id' => $this->getEnrollmentSession()->id,
                    'religion_id' => $religion_in_db ? $religion_in_db->id : null,
                    'health_concern_id' => $health_concern_in_db ? $health_concern_in_db->id : null,
                    'primary_school_id' => $primary_school_in_db ? $primary_school_in_db->id : null,
                    'registration_date' => $row['register_date'],
                    'expiry_date' => $row['expiry_date'],
                ]);

                // Create guardian
                $enrollment->guardians()->create([
                    'name' => ['en' => $row['guardian_name'], 'zh' => $row['guardian_name']],
                    'email' => $row['guardian_email'],
                    'phone_number' => $row['guardian_phone_number'],
                    'guardian_type' => $row['guardian_type'],
                ]);


                // Create enrollment user
                $guardian = $guardian_by_phone_map[$row['guardian_phone_number']] ?? $guardian_by_email_map[$row['guardian_email']] ?? null;

                if (!$guardian) {
                    $guardian = EnrollmentUser::create([
                        'name' => [
                            'en' => $row['guardian_name'],
                            'zh' => $row['guardian_name'],
                        ],
                        'phone_number' => $row['guardian_phone_number'],
                        'email' => $row['guardian_email'],
                    ]);

                    $guardian_by_phone_map[$row['guardian_phone_number']] = $guardian;
                    $guardian_by_email_map[$row['guardian_email']] = $guardian;
                }

                $enrollment->enrollment_user_id = $guardian->id;
                $enrollment->save();

                // If exam_slip_number + total_average is provided, create enrollment exam, else skip
                if (!empty($row['exam_slip_number']) && !empty($row['total_average'])) {
                    // ENROLLMENT_EXAM related
                    $enrollment_exam = $this->enrollmentExamRepository->create([
                        'enrollment_id' => $enrollment->id,
                        'exam_slip_number' => $row['exam_slip_number'],
                        'total_average' => $row['total_average'],
                    ]);

                    foreach ($subjects as $subject) {
                        if (isset($row[$subject->code])) {
                            $all_marks[] = [
                                'enrollment_exam_id' => $enrollment_exam->id,
                                'subject_id' => $subject->id,
                                'mark' => $row[$subject->code],
                                'created_at' => now()->toDateTimeString(),
                                'updated_at' => now()->toDateTimeString(),
                            ];
                        }
                    }
                }

                $notification_jobs[] = ['enrollment_id' => $enrollment->id, 'email' => $row['guardian_email'], 'phone_number' => $row['guardian_phone_number']];
            }

            // Insert all marks in bulk
            if (!empty($all_marks)) {
                $this->enrollmentExamMarkRepository->insert($all_marks);
            }
        });

        // To notify guardian of Enrollment status
        foreach ($notification_jobs as $job) {
            NotifyGuardianOfEnrollmentStatusJob::dispatch($job['enrollment_id'], $job['email'], $job['phone_number']);
        }

        return $this;
    }

    public function savePostPaymentImportedData(): self
    {
        if ($this->getImportErrorCount() > 0) {
            return $this;
        }

        $notification_jobs = [];

        DB::transaction(function () use (&$notification_jobs) {

            $data = $this->getData();

            $subjects = $this->getEnrollmentSession()->examSubjects->keyBy('code');

            if (count($this->enrollmentByNricMap) === 0 && count($this->enrollmentByPassportMap) === 0) {
                $map = $this->fetchEnrollmentsMapping();
                $enrollment_by_nric_map = $map['enrollment_by_nric_map'];
                $enrollment_by_passport_map = $map['enrollment_by_passport_map'];
            } else {
                $enrollment_by_nric_map = $this->enrollmentByNricMap;
                $enrollment_by_passport_map = $this->enrollmentByPassportMap;
            }

            foreach ($data as $row) {
                $nric_payload = $row['nric'];
                $passport_number_payload = $row['passport_number'];
                $enrollment = $enrollment_by_nric_map[$nric_payload] ?? $enrollment_by_passport_map[$passport_number_payload] ?? null;

                if (!$enrollment) {
                    continue; // skip this row if enrollment not found, but should never happen because of previous validation
                }

                // *** UPDATE enrollment
                $this->enrollmentRepository->update($enrollment, [
                    'is_hostel' => $row['hostel'],
                    'enrollment_status' => $row['status'],
                ]);


                $enrollent_exams = $enrollment
                    ->enrollmentExams
                    ->keyBy('exam_slip_number');

                $exam = $enrollent_exams[$row['exam_slip_number']] ?? null;

                $exam_marks = [];

                if (!$exam) {
                    $enrollment_exam = $enrollment->enrollmentExams()->create([
                        'exam_slip_number' => $row['exam_slip_number'],
                        'total_average' => $row['total_average'],
                    ]);
                } else {
                    $enrollment_exam = $exam;

                    // *** UPDATE exam with total_average
                    $this->enrollmentExamRepository->update($exam, [
                        'total_average' => $row['total_average'],
                    ]);

                    $exam_marks = $exam->examMarks->keyBy('subject.code');
                }

                // *** UPDATE exam marks
                foreach ($subjects as $subject) {
                    if (isset($row[$subject->code])) {
                        if (isset($exam_marks[$subject->code])) {
                            $this->enrollmentExamMarkRepository->update($exam_marks[$subject->code], [
                                'mark' => $row[$subject->code],
                            ]);
                        } else {
                            $enrollment_exam->examMarks()->create([
                                'subject_id' => $subject->id,
                                'mark' => $row[$subject->code],
                            ]);
                        }
                    }
                }

                // only UNPAID && ACCEPTED enrollments will be notified
                if (
                    $enrollment->payment_status->value === EnrollmentPaymentStatus::UNPAID->value &&
                    $row['status'] === EnrollmentStatus::APPROVED->value
                ) {
                    $notification_jobs[] = ['enrollment_id' => $enrollment->id];
                }
            }
        });

        // To re-notify guardian of Enrollment status
        foreach ($notification_jobs as $job) {
            RenotifyGuardianOfEnrollmentStatusJob::dispatch($job['enrollment_id']);
        }

        return $this;
    }

    public function fetchAllMapping(): array
    {
        $data = collect($this->getData());

        $unique_religions = $data->pluck('religion')->filter()->unique()->values()->toArray();
        $unique_health_concerns = $data->pluck('health_concern')->filter()->unique()->values()->toArray();
        $unique_primary_schools = $data->pluck('primary_school')->filter()->unique()->values()->toArray();
        $unique_phone_numbers = $data->pluck('guardian_phone_number')->filter()->unique()->values()->toArray();
        $unique_emails = $data->pluck('guardian_email')->filter()->unique()->values()->toArray();

        // fetch all religions, health concerns, primary schools - refetch from DB because of newly created ones
        $all_religions = $this->religionRepository->getByNames($unique_religions);
        $all_health_concerns = $this->healthConcernRepository->getByNames($unique_health_concerns);
        $all_primary_schools = $this->schoolRepository->getByNames($unique_primary_schools);

        // fetch all guardians via phone number and email
        $all_guardians = EnrollmentUser::where(function ($query) use ($unique_phone_numbers, $unique_emails) {
            $query
                ->whereIn('phone_number', $unique_phone_numbers)
                ->orWhereIn('email', $unique_emails);
        })->select(['id', 'name', 'phone_number', 'email'])->get();

        // loopup maps for religions
        $religion_map = [];
        foreach ($all_religions as $religion) {
            $religion_map[$religion->getTranslation('name', 'en')] = $religion;
            $religion_map[$religion->getTranslation('name', 'zh')] = $religion;
        }

        // loopup maps for health concerns
        $health_concern_map = [];
        foreach ($all_health_concerns as $health_concern) {
            $health_concern_map[$health_concern->getTranslation('name', 'en')] = $health_concern;
            $health_concern_map[$health_concern->getTranslation('name', 'zh')] = $health_concern;
        }

        // loopup maps for primary schools
        $primary_school_map = [];
        foreach ($all_primary_schools as $primary_school) {
            $primary_school_map[$primary_school->getTranslation('name', 'en')] = $primary_school;
            $primary_school_map[$primary_school->getTranslation('name', 'zh')] = $primary_school;
        }

        // loopup maps for guardians
        $guardian_by_phone_map = [];
        $guardian_by_email_map = [];
        foreach ($all_guardians as $guardian) {
            // For each guardian, check if its phone number matches any of the input phone numbers
            foreach ($unique_phone_numbers as $phone) {
                if (
                    $guardian->phone_number === $phone &&
                    !isset($guardian_by_phone_map[$phone]) // only add to map if not exist
                ) {
                    $guardian_by_phone_map[$phone] = $guardian;
                }
            }

            // For each guardian, check if its email matches any of the input emails
            foreach ($unique_emails as $email) {
                if (
                    $guardian->email === $email &&
                    !isset($guardian_by_email_map[$email]) // only add to map if not exist
                ) {
                    $guardian_by_email_map[$email] = $guardian;
                }
            }
        }

        return [
            'religion_map' => $religion_map,
            'health_concern_map' => $health_concern_map,
            'primary_school_map' => $primary_school_map,
            'guardian_by_phone_map' => $guardian_by_phone_map,
            'guardian_by_email_map' => $guardian_by_email_map,
        ];
    }

    public function fetchEnrollmentsMapping(): array
    {
        $collection = collect($this->getData());

        $payload_nrics = $collection->pluck('nric')->filter()->unique();
        $payload_passport_numbers = $collection->pluck('passport_number')->filter()->unique();

        // fetch all enrollments where nric or passport_number matches the payload
        $existing_enrollments = Enrollment::where('enrollment_session_id', $this->getEnrollmentSession()->id)
            ->where(function ($query) use ($payload_nrics, $payload_passport_numbers) {
                $query
                    ->whereIn('nric', $payload_nrics)
                    ->orWhereIn('passport_number', $payload_passport_numbers);
            })
            ->select(['id', 'name', 'nric', 'passport_number', 'is_hostel', 'enrollment_status', 'payment_status'])
            ->with([
                'enrollmentExams' => function ($query) {
                    $query->with(['examMarks.subject:id,code']);
                }
            ])
            ->get();

        // lookup maps for enrollments
        $enrollment_by_nric_map = [];
        $enrollment_by_passport_map = [];
        foreach ($existing_enrollments as $enrollment) {
            foreach ($payload_nrics as $nric) {
                if (
                    $enrollment->nric === $nric &&
                    !isset($enrollment_by_nric_map[$nric]) // only add to map if not exist
                ) {
                    $enrollment_by_nric_map[$nric] = $enrollment;
                }
            }

            foreach ($payload_passport_numbers as $passport_number) {
                if (
                    $enrollment->passport_number === $passport_number &&
                    !isset($enrollment_by_passport_map[$passport_number]) // only add to map if not exist
                ) {
                    $enrollment_by_passport_map[$passport_number] = $enrollment;
                }
            }
        }

        $this->setEnrollmentByNricMap($enrollment_by_nric_map);
        $this->setEnrollmentByPassportMap($enrollment_by_passport_map);

        return [
            'enrollment_by_nric_map' => $enrollment_by_nric_map,
            'enrollment_by_passport_map' => $enrollment_by_passport_map,
        ];
    }

    public function createReligionInBulk(): self
    {
        if (count($this->getToBeCreatedReligions()) > 0) {
            $religions_payload = [];

            // get max_sequence from existing religions
            $max_sequence_for_religion = Religion::max('sequence') ?? -1;

            foreach ($this->getToBeCreatedReligions() as $religion) {
                $max_sequence_for_religion++;  // Always increment

                $religions_payload[] = [
                    'name' => json_encode([
                        'en' => $religion,
                        'zh' => $religion,
                    ]),
                    'sequence' => $max_sequence_for_religion,
                ];
            }

            if (count($religions_payload) > 0) {
                $this->religionRepository->insert($religions_payload);
            }
        }

        return $this;
    }

    public function createPrimarySchoolInBulk(): self
    {
        if (count($this->getToBeCreatedPrimarySchools()) > 0) {
            $primary_schools_payload = [];

            foreach ($this->getToBeCreatedPrimarySchools() as $primary_school) {
                $primary_schools_payload[] = [
                    'name' => json_encode([
                        'en' => $primary_school,
                        'zh' => $primary_school,
                    ]),
                    'level' => SchoolLevel::PRIMARY,
                ];
            }

            if (count($primary_schools_payload) > 0) {
                $this->schoolRepository->insert($primary_schools_payload);
            }
        }

        return $this;
    }

    public function generatePaymentGatewayLogWithPaymentLink()
    {
        $bill_to = $this->billingDocument->billTo;

        $payment_gateway_log = $this->paymentGatewayLogService
            ->setProvider(PaymentProvider::PAYEX)
            ->setUser($this->getEnrollmentUser())
            ->setTransactionLoggable($this->enrollment)
            ->setBillingDocument($this->billingDocument)
            ->setPaymentMethod(PaymentMethod::where('code', PaymentMethod::CODE_FPX)->firstOrFail())
            ->createUnpaidItemForEnrollmentTransaction($this->billingDocument->amount_after_tax);

        $payment_gateway_response = $this->paymentGatewayService
            ->setProvider(PaymentProvider::PAYEX)
            ->setPaymentTypes([PaymentMethod::CODE_FPX])
            ->setCustomerEmail($bill_to->email)
            ->setCustomerName($bill_to->getTranslation('name', 'en'))
            ->setPaymentGatewayLog($payment_gateway_log)
            ->setReturnUrl($this->returnUrl ?? null)
            ->request();

        $this->paymentGatewayLogService
            ->setRequestData($payment_gateway_response['request_data'])
            ->setResponseData($payment_gateway_response['response_data'])
            ->setOrderId($payment_gateway_response['order_id']);

        if (Arr::get($payment_gateway_response, 'status') == PaymentStatus::FAILED) {

            $this->paymentGatewayLogService
                ->setRemark($payment_gateway_response['remark'])
                ->setStatus(PaymentStatus::FAILED)
                ->updatePaymentGatewayLog();

            throw new \Exception($payment_gateway_response['remark']);

        }

        return $this->paymentGatewayLogService
            ->setPaymentUrl($payment_gateway_response['payment_url'])
            ->updatePaymentGatewayLog();

    }


    public function createHealthConcernInBulk(): self
    {
        if (count($this->getToBeCreatedHealthConcerns()) > 0) {
            $health_concerns_payload = [];

            foreach ($this->getToBeCreatedHealthConcerns() as $health_concern) {
                $health_concerns_payload[] = [
                    'name' => json_encode([
                        'en' => $health_concern,
                        'zh' => $health_concern,
                    ]),
                ];
            }

            if (count($health_concerns_payload) > 0) {
                $this->healthConcernRepository->insert($health_concerns_payload);
            }
        }

        return $this;
    }

    private function extractAllTranslatableName($existing_data): array
    {
        $map = [];

        foreach ($existing_data as $row) {
            foreach (self::AVAILABLE_LOCALES as $locale) {
                $map[$row->getTranslation('name', $locale)] = true;
            }
        }

        return $map;
    }

    public function requestPaymentLink(): PaymentGatewayLog
    {
        if (!isset($this->billingDocument) && !isset($this->enrollmentUser)) {
            throw new Exception('Billing document or enrollmentUser is not set.');
        }

        // initial validation for billing document
        app()->make(BillingDocumentService::class)
            ->setBillingDocument($this->billingDocument)
            ->validateRequestPaymentLink();

        return $this->generatePaymentGatewayLogWithPaymentLink();
    }

    private function prepareUpdatePayload(array $data, bool $is_admin = false): array
    {
        $payload = [
            'email' => $data['email'] ?? null,
            'phone_number' => $data['phone_number'],
            'phone_number_2' => $data['phone_number_2'] ?? null,
            'birthplace' => $data['birthplace'],
            'nationality_id' => $data['nationality_id'],
            'date_of_birth' => $data['date_of_birth'],
            'gender' => $data['gender'],
            'birth_cert_number' => $data['birth_cert_number'],
            'race_id' => $data['race_id'],
            'religion_id' => $data['religion_id'],
            'address' => $data['address'],
            'postal_code' => $data['postal_code'],
            'city' => $data['city'],
            'state_id' => $data['state_id'],
            'country_id' => $data['country_id'],
            'address_2' => $data['address_2'] ?? null,
            'dietary_restriction' => $data['dietary_restriction'] ?? null,
            'health_concern_id' => $data['health_concern_id'] ?? null,
            'primary_school_id' => $data['primary_school_id'] ?? null,
        ];

        if ($is_admin) {
            $payload = array_merge($payload, [
                'name' => $data['name'],
                'nric' => $data['nric'] ?? null,
                'passport_number' => $data['passport_number'] ?? null,
                'admission_year' => $data['admission_year'] ?? null,
                'admission_grade_id' => $data['admission_grade_id'] ?? null,
                'join_date' => $data['join_date'] ?? null,
                'leave_date' => $data['leave_date'] ?? null,
                'leave_status' => $data['leave_status'] ?? null,
                'is_hostel' => $data['is_hostel'] ?? null,
                'is_active' => $data['is_active'] ?? null,
                'remarks' => $data['remarks'] ?? null,
                'custom_field' => $data['custom_field'] ?? null,
                'admission_type' => $data['admission_type'] ?? null,
                'enrollment_status' => $data['enrollment_status'] ?? null,
                'have_siblings' => $data['have_siblings'] ?? null,
                'is_foreigner' => $data['is_foreigner'] ?? null,
                'conduct' => $data['conduct'] ?? null,
            ]);
        }

        return $payload;
    }

    public function updateStatus(EnrollmentPaymentStatus $status): Enrollment
    {
        return $this->enrollmentRepository->update($this->getEnrollment(), [
            'payment_status' => $status,
        ]);
    }
}
