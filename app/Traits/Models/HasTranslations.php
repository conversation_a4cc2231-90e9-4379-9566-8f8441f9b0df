<?php

namespace App\Traits\Models;

use App\Helpers\ConfigHelper;
use Illuminate\Database\Eloquent\Builder;

/**
 * @method static void whereTranslations(string $column, array|string $search = null, string $operand = '=', bool $wildcard = false)
 */
trait HasTranslations
{
    use \Spatie\Translatable\HasTranslations;

    /**
     *  Need to pass in as below structure
     *  [
     *     ["en" => "English"],
     *     ["zh" => "Chinese"],
     *  ]
     */
    public function scopeWhereTranslations(
        Builder $query,
        string $column,
        array|string $search = null,
        string $operand = '=',
        bool $wildcard = false
    ): void {
        if (is_null($search)) {
            return;
        }

        $condition = 'and';
        if (is_string($search)) {
            $condition = 'or';
            $locales = ConfigHelper::getAvailableLocales();
            $string_search = $search;
            $search = [];

            foreach ($locales as $locale) {
                $search[$locale] = $string_search;
            }
        }

        $query->where(function (Builder $query) use ($column, $search, $operand, $wildcard, $condition) {
            foreach ($search as $locale => $value) {
                $value = $wildcard ? "%$value%" : $value;
                if ($condition === 'or') {
                    $query->orWhere("{$column}->{$locale}", $operand, $value);
                    continue;
                }
                $query->where("{$column}->{$locale}", $operand, $value);
            }
        });
    }

    // key = database column
    public function getFormattedTranslations($key)
    {
        if(isset($this->translations[$key])){
            $translations = $this->translations[$key];
            return !empty($translations) ? implode(' - ', $translations) : '-';
        }else{
            return '-';
        }
    }
}
