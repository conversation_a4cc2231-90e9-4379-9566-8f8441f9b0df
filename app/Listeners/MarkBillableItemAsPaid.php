<?php

namespace App\Listeners;

use App\Events\InvoicePaidEvent;
use App\Helpers\LogHelper;
use App\Models\BillingDocument;
use Illuminate\Contracts\Queue\ShouldQueue;
use Psr\Log\LogLevel;

class MarkBillableItemAsPaid implements ShouldQueue
{
    public $connection = 'event-listeners';
    public $queue = 'event-listeners';
    public $delay = 0;
    public $tries = 1;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(InvoicePaidEvent $event): void
    {
        $billing_document = $event->billing_document;

        // only run on invoices
        if ( $billing_document->type !== BillingDocument::TYPE_INVOICE ) {
            LogHelper::write('[MarkBillableItemAsPaid] Billing Document ' . $billing_document->id . ' is not an invoice, ignore.', null,LogLevel::DEBUG, LogHelper::LOG_NAME_ACCOUNTING);
            return;
        }

        $payments = $billing_document->payments;

        if ( $payments->count() === 0 ) {
            LogHelper::write('[MarkBillableItemAsPaid] Unable to mark as paid because no payment found for billing document ' . $billing_document->id, null,LogLevel::ERROR, LogHelper::LOG_NAME_ACCOUNTING);
            return;
        }

        foreach ( $billing_document->lineItems as $line_item ) {

            if ( $line_item->billableItem === null ){
                continue;
            }

            // billable items must implement BillableItem interface
            $line_item->billableItem->markAsPaid($payments);

        }

    }
}
