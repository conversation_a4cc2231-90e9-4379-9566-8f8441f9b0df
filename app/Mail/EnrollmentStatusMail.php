<?php

namespace App\Mail;

use App\Enums\EnrollmentStatus;
use App\Models\Enrollment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class EnrollmentStatusMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        private Enrollment $enrollment,
    ) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: config('app.name') . ': Enrollment',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            htmlString: $this->getBody(),
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    private function getBody(): string
    {
        $enrollment_session = $this->enrollment->enrollmentSession;
        $url = config('school.enrollment_url');

        switch ($this->enrollment->enrollment_status) {
            case EnrollmentStatus::APPROVED->value:
                return "
                    Status: APPROVED<br><br>
                    
                    Congratulations! Your application for {$this->enrollment->name} for 
                    {$enrollment_session->name} enrollment is successful. Please <a href='$url'>login</a> 
                    to fill in more enrollment details.
                ";

            case EnrollmentStatus::REJECTED->value:
                return "
                    Status: REJECTED<br><br>
                    
                    We are sorry, your application for {$this->enrollment->name} for 
                    {$enrollment_session->name} enrollment is unsuccessful.
                ";

            case EnrollmentStatus::SHORTLISTED->value:
                return "
                    Status: SHORTLISTED<br><br>
                    
                    Your application for {$this->enrollment->name} for {$enrollment_session->name} 
                    enrollment is being put on hold in a waitlist. You will be notified if
                    anything changes.
                ";

            default:
                return "";
        }
    }
}
