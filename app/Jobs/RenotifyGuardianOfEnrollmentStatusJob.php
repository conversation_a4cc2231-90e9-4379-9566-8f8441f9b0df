<?php

namespace App\Jobs;

use App\Mail\EnrollmentStatusMail;
use App\Models\Enrollment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class RenotifyGuardianOfEnrollmentStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public mixed $enrollmentId;

    public function __construct($enrollment_id)
    {
        $this->enrollmentId = $enrollment_id;
    }

    public function handle(): void
    {
        $enrollment = Enrollment::with('enrollmentUser')
            ->where('id', $this->enrollmentId)
            ->first();

        if (!$enrollment) {
            return;
        }

        $email = $enrollment->enrollmentUser?->email;

        Mail::to($email)->send(new EnrollmentStatusMail($enrollment));
    }
}
