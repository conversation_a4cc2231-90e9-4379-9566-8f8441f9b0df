<?php

namespace App\Jobs;

use App\Mail\EnrollmentStatusMail;
use App\Models\Enrollment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class NotifyGuardianOfEnrollmentStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public mixed $enrollmentId;
    public string $guardianEmail;

    public function __construct($enrollment_id, $guardian_email)
    {
        $this->enrollmentId = $enrollment_id;
        $this->guardianEmail = $guardian_email;
    }

    public function handle(): void
    {
        $enrollment = Enrollment::find($this->enrollmentId);

        if (!$enrollment) {
            return;
        }

        Mail::to($this->guardianEmail)->send(new EnrollmentStatusMail($enrollment));
    }
}
