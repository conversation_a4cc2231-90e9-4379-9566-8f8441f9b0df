<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SemesterClass\SemesterClassIndexRequest;
use App\Http\Requests\Api\SemesterClass\SemesterClassUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\SemesterClassGroupResource;
use App\Http\Resources\SemesterClassResource;
use App\Models\SemesterClass;
use App\Services\SemesterClassService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class SemesterClassController extends Controller
{
    use HandlesPagination;

    public function __construct(
        protected SemesterClassService $semesterClassService
    ) {
    }

    public function index(SemesterClassIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $input['includes'] = array_merge($input['includes'] ?? [], ['classModel.grade', 'homeroomTeacher']);
        $input['includes'] = array_unique($input['includes']);

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input, $this->semesterClassService, 'getAllSemesterClasses', 'getAllPaginatedSemesterClasses');
        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(SemesterClassResource::collection($data))->getResponse();
    }

    /**
     * @param  SemesterClass  $semester_class
     * @return JsonResponse
     */
    public function show(SemesterClass $semester_class): JsonResponse
    {
        $semester_class->loadMissing([
            'defaultGradingFramework',
            'semesterSetting',
            'classModel',
            'homeroomTeacher',
            'classSubjects',
            'latestPrimaryClassBySemesterSettings' => function ($query) {
                $query->with('student');
                $query->whereNull('class_leave_date');
            },
            'studentClasses' => function ($query) use (&$semester_class) {
                $query->with([
                    'semesterSetting',
                    'semesterClass',
                    'student' => function ($q) use (&$semester_class) {
                        $q->with([
                            'primaryClassAndGradeBySemesterSetting' => function ($q) use (&$semester_class) {
                                $q->where('semester_setting_id', $semester_class->semester_setting_id);
                            }
                        ]);
                    }
                ])
                    ->where('is_latest_class_in_semester', true);
            },
        ]);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new SemesterClassResource($semester_class))
            ->getResponse();
    }

    public function update(SemesterClass $semester_class, SemesterClassUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $semester_class = $this->semesterClassService->updateSemesterClass($semester_class, $input);

        $semester_class->loadMissing(['semesterSetting', 'classModel', 'homeroomTeacher', 'defaultGradingFramework', 'classSubjects']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new SemesterClassResource($semester_class))
            ->getResponse();
    }

    public function destroy(SemesterClass $semester_class): JsonResponse
    {
        $this->semesterClassService->deleteSemesterClass($semester_class);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
