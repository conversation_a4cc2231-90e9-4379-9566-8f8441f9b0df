<?php

namespace App\Http\Controllers\Api\Reports;

use App\Enums\ExportType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\FileHelper;
use App\Http\Requests\Api\Reports\SemesterClass\ReportHomeroomTeachersRequest;
use App\Http\Requests\Api\Reports\SemesterClass\ReportByStudentsInSemesterClassRequest;
use App\Http\Requests\Api\Reports\SemesterClass\ReportStudentContactsRequest;
use App\Http\Requests\Api\Reports\SemesterClass\ReportStudentDetailsRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\SemesterClassReportHomeroomTeacherResource;
use App\Services\DocumentPrintService;
use App\Services\Report\SemesterClassReportService;
use App\Services\ReportPrintService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;

class SemesterClassReportController
{
    public function __construct(
        protected SemesterClassReportService $semesterClassReportService,
        protected ReportPrintService         $reportPrintService
    ) {}

    public function reportStudentContacts(ReportStudentContactsRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $data = $this->semesterClassReportService->getStudentContactsReportData($filters);

        $export_type = Arr::get($filters, 'export_type');

        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data)
                ->getResponse();
        }

        $report_data = [
            'data' => $data,
            'title' => __('semester_class.student_contact_report') . " ({$data['semester_name']})"
        ];
        $report_view_name = 'reports.semester-classes.student-contacts';
        $file_name = 'semester-classes-report-student-contacts';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name, ExportType::getExtension($export_type)))
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function reportStudentDetails(ReportStudentDetailsRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $data = $this->semesterClassReportService->getStudentDetailsReportData($filters);

        $export_type = Arr::get($filters, 'export_type');

        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data)
                ->getResponse();
        }

        $report_data = [
            'data' => $data,
        ];
        $report_view_name = 'reports.semester-classes.student-details';
        $file_name = 'semester-classes-report-student-details';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name, ExportType::getExtension($export_type)))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function reportHomeroomTeachers(ReportHomeroomTeachersRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $data = $this->semesterClassReportService->getReportHomeroomTeachers($filters);

        $export_type = Arr::get($filters, 'export_type');

        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData(SemesterClassReportHomeroomTeacherResource::collection($data['homeroom_teachers']))
                ->getResponse();
        }

        $report_data = [
            'data' => $data['homeroom_teachers'],
            'title' => __('semester_class.homeroom_teacher_report') . " ({$data['semester_name']})"
        ];
        $report_view_name = 'reports.semester-classes.homeroom-teachers';
        $file_name = 'semester-classes-report-homeroom-teachers';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name, ExportType::getExtension($export_type)))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function reportByStudentsInSemesterClass(ReportByStudentsInSemesterClassRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $data = $this->semesterClassReportService->getAllStudentsBySemesterClassReportData($filters);

        $report_data = ['data' => $data];

        $report_view_name = 'reports.semester-classes.by-students-in-semester-class';

        $file_name = 'students-list';

        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor(ExportType::from($filters['export_type']));

        $adapter
            ->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService
            ->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }
}
