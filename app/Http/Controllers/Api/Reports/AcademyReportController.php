<?php

namespace App\Http\Controllers\Api\Reports;

use App\Enums\ExportType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Reports\Academy\StudentAnalysisReportBySemesterRequest;
use App\Http\Requests\Api\Reports\Academy\TransferredStudentListByAdmissionYearRequest;
use App\Http\Resources\ApiResponse;
use App\Models\SemesterSetting;
use App\Repositories\StudentRepository;
use App\Services\ReportPrintService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;

class AcademyReportController extends Controller
{
    public function __construct(
        protected StudentRepository $studentRepository,
        protected ReportPrintService $reportPrintService,
    ) {}

    public function transferredStudentListByAdmissionYear(TransferredStudentListByAdmissionYearRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $data = $this->studentRepository->transferredStudentListByAdmissionYearData($filters['admission_year']);

        $export_type = Arr::get($filters, 'export_type');

        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data)
                ->getResponse();
        }

        $report_data = [
            'data' => $data,
        ];

        $report_view_name = 'reports.academy.transferred-student-list';
        $file_name = 'academy-transferred-student-list';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(ReportPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function studentAnalysisReportBySemesterGroupByGrade(StudentAnalysisReportBySemesterRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $semester_setting = SemesterSetting::findOrFail($filters['semester_setting_id']);
        $data = $this->studentRepository->studentAnalysisReportBySemesterGroupByGradeData($semester_setting);

        $export_type = Arr::get($filters, 'export_type');

        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data)
                ->getResponse();
        }

        $report_data = [
            'data' => $data,
        ];

        $report_view_name = 'reports.academy.student-analysis-report-by-semester';
        $file_name = 'academy-student-analysis-report-by-semester';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(ReportPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }
}
