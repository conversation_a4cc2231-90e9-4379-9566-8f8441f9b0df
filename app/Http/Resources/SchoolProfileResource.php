<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SchoolProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'translations' => $this->translations,
            'code' => $this->code,
            'short_name' => $this->short_name,
            'address' => $this->address,
            'country_id' => $this->country_id,
            'state_id' => $this->state_id,
            'city' => $this->city,
            'postcode' => $this->postcode,
            'phone_1' => $this->phone_1,
            'phone_2' => $this->phone_2,
            'fax_1' => $this->fax_1,
            'fax_2' => $this->fax_2,
            'email' => $this->email,
            'url' => $this->url,
            'logo_url' => $this->logo_url,
        ];
    }
}
