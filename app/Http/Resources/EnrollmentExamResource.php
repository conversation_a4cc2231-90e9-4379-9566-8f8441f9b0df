<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EnrollmentExamResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'exam_slip_number' => $this->exam_slip_number,
            'total_average' => (float) $this->total_average,
            'marks' => $this->whenLoaded('examMarks', function () {
                return EnrollmentExamMarkResource::collection($this->examMarks);
            }),
        ];
    }
}
