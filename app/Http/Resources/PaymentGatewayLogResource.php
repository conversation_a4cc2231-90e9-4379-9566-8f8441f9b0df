<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PaymentGatewayLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'billing_document_id' => $this->billing_document_id,
            'order_id' => $this->order_id ?: null,
            'currency' => $this->currency_code,
            'amount' => (float) $this->amount,
            'status' => $this->status,
            'payment_url' => (string) $this->payment_url ?: null,
            'payment_required' => $this->payment_required ?? true,
        ];
    }
}
