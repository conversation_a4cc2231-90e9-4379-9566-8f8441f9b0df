<?php

namespace App\Http\Requests\Api\EcommerceOrder;

use App\Enums\EcommerceOrderBuyerType;
use App\Enums\EcommerceOrderPaymentStatus;
use App\Enums\EcommerceOrderStatus;
use App\Enums\MerchantType;
use App\Http\Requests\Api\CommonApiValidationRequest;
use App\Interfaces\Userable;
use App\Rules\AuthorizeUserableAccess;
use Illuminate\Validation\Rule;

class EcommerceOrderIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'merchant_type' => ['nullable', Rule::in(MerchantType::values())],
            'user_id' => 'nullable|integer|exists:users,id',
            'userable_id' => ['required', new AuthorizeUserableAccess],
            'userable_type' => ['required', Rule::in(Userable::USERABLE_TYPES)],
            'id' => ['nullable', 'integer'],
            'order_reference_number' => ['nullable', 'string'],
            'buyer' => ['nullable', 'array'],
            'buyer.type' => [Rule::in(EcommerceOrderBuyerType::values())],
            'buyer.id' => ['integer'],
            'status' => ['nullable', Rule::in(EcommerceOrderStatus::values())],
            'payment_status' => ['nullable', Rule::in(EcommerceOrderPaymentStatus::values())],
            'date' => ['nullable', 'date'],
        ]);
    }
}
