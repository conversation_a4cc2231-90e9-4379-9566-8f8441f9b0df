<?php

namespace App\Http\Requests\Api\Enrollment;

use App\Enums\DietaryRestriction;
use App\Enums\Gender;
use App\Enums\LiveStatus;
use App\Enums\MarriedStatus;
use App\Enums\SchoolLevel;
use App\Repositories\CountryRepository;
use App\Repositories\EducationRepository;
use App\Repositories\RaceRepository;
use App\Repositories\ReligionRepository;
use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EnrollmentUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && $this->enrollment->canBeAccessedByUser(auth()->user());
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     * @throws \Exception
     */
    public function rules(): array
    {
        $guardians_payload = collect($this->input('guardians', []));

        // NATIONALITY_ID
        $nationality_ids_payload = $guardians_payload->pluck('nationality_id')->filter()->unique()->toArray();
        $existing_nationality_ids = (new CountryRepository())->find($nationality_ids_payload)->pluck('id')->toArray();

        // RACE_ID
        $race_ids_payload = $guardians_payload->pluck('race_id')->filter()->unique()->toArray();
        $existing_race_ids = (new RaceRepository())->find($race_ids_payload)->pluck('id')->toArray();

        // RELIGION_ID
        $religion_ids_payload = $guardians_payload->pluck('religion_id')->filter()->unique()->toArray();
        $existing_religion_ids = (new ReligionRepository())->find($religion_ids_payload)->pluck('id')->toArray();

        // EDUCATION_ID
        $education_ids_payload = $guardians_payload->pluck('education_id')->filter()->unique()->toArray();
        $existing_education_ids = (new EducationRepository())->find($education_ids_payload)->pluck('id')->toArray();

        // Commented out excel import ones to prevent parents from changing
        return [
//            'name' => ['required', 'array', new RequiredLocaleValidation()],
//            'name.*' => [new InternationalizationValidation(), 'string', 'max:100'],
            'email' => ['nullable', 'email'],
            'phone_number' => ['required', 'phone', 'max:20'],
            'phone_number_2' => ['nullable', 'phone', 'max:20'],
//            'nric' => ['nullable', 'numeric', 'digits:12', 'required_without:passport_number', Rule::unique('students', 'nric')],
//            'passport_number' => ['nullable', 'string', 'max:50', 'required_without:nric', Rule::unique('students', 'passport_number')],
            'birthplace' => ['required', 'string', 'max:100'],
            'nationality_id' => ['required', 'exists:master_countries,id'],
            'date_of_birth' => ['required', 'date', 'before:today'],
            'gender' => ['required', Rule::in(Gender::values())],
            'birth_cert_number' => ['required', 'string', 'max:50', Rule::unique('students', 'birth_cert_number')],
            'race_id' => ['required', 'exists:master_races,id'],
            'religion_id' => ['required', 'exists:master_religions,id'],
            'address' => ['required', 'string', 'max:255'],
            'postal_code' => ['required', 'string', 'max:20'],
            'city' => ['required', 'string', 'max:100'],
            'state_id' => ['required', 'exists:master_states,id'],
            'country_id' => ['required', 'exists:master_countries,id'],
            'address_2' => ['nullable', 'string', 'max:255'],
            'dietary_restriction' => ['nullable', Rule::in(DietaryRestriction::values())],
            'health_concern_id' => ['nullable', 'exists:master_health_concerns,id'],
            'primary_school_id' => ['required', 'exists:master_schools,id,level,' . SchoolLevel::PRIMARY->value],
            'guardians' => ['required', 'array', 'min:1'],
            'guardians.*.name' => ['required', 'array', new RequiredLocaleValidation()],
            'guardians.*.name.*' => [new InternationalizationValidation(), 'string', 'max:100'],
            'guardians.*.email' => ['nullable', 'email', 'max:100'],
            'guardians.*.phone_number' => ['required', 'phone', 'string', 'max:20'],
            'guardians.*.nric' => ['nullable', 'string', 'max:12'],
            'guardians.*.passport_number' => ['nullable', 'string', 'max:50'],
            'guardians.*.nationality_id' => ['nullable', Rule::in($existing_nationality_ids)],
            'guardians.*.race_id' => ['nullable', Rule::in($existing_race_ids)],
            'guardians.*.religion_id' => ['nullable', Rule::in($existing_religion_ids)],
            'guardians.*.education_id' => ['nullable', Rule::in($existing_education_ids)],
            'guardians.*.married_status' => ['nullable', Rule::in(MarriedStatus::values())],
            'guardians.*.live_status' => ['nullable', Rule::in(LiveStatus::values())],
            'guardians.*.occupation' => ['nullable', 'string', 'max:100'],
            'guardians.*.occupation_description' => ['nullable', 'string', 'max:200'],
            'guardians.*.remarks' => ['nullable', 'string'],
            'guardians.*.is_primary' => ['nullable', 'boolean'],
            'guardians.*.guardian_type' => ['nullable', 'boolean'],
        ];
    }
}
