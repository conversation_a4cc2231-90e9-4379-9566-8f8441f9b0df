<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class LeaveApplicationTypeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name->en' => fake()->name,
            'name->zh' => fake()->name,
            'is_present' => false,
            'average_point_deduction' => 0,
            'conduct_point_deduction' => 0,
            'code' => fake()->unique()->word(),
        ];
    }
}
