<?php

namespace Database\Factories;

use App\Enums\DietaryRestriction;
use App\Enums\EnrollmentPaymentStatus;
use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Enums\StudentAdmissionType;
use App\Models\Country;
use App\Models\EnrollmentSession;
use App\Models\EnrollmentUser;
use App\Models\Grade;
use App\Models\HealthConcern;
use App\Models\Race;
use App\Models\Religion;
use App\Models\School;
use App\Models\State;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Enrollment>
 */
class EnrollmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(), // key to User
            'name->en' => fake()->name(),
            'name->zh' => fake('zh_CN')->name(),
            'email' => fake()->email(),
            'phone_number' => fake()->phoneNumber(),
            'phone_number_2' => fake()->phoneNumber(),
            'nric' => fake()->uuid(),
            'passport_number' => fake()->uuid(),
            'admission_year' => rand(2000, 2030),
            'admission_grade_id' => Grade::factory(), // key to MasterGrade
            'join_date' => now()->toDateString(),
            'leave_date' => null,
            'leave_status' => null,
            'student_number' => fake()->uuid(),
            'birthplace' => fake()->city(),
            'nationality_id' => Country::factory(), // key to MasterCountry
            'date_of_birth' => now()->subYears(20)->toDateString(),
            'gender' => Gender::MALE->value,
            'birth_cert_number' => fake()->uuid(),
            'race_id' => Race::factory(), // key to MasterRace
            'religion_id' => Religion::factory(), // key to MasterReligion
            'address' => fake()->address(),
            'postal_code' => fake()->postcode(),
            'city' => fake()->city(),
            'state_id' => State::factory(),
            'country_id' => Country::factory(),
            'address_2' => null,
            'is_hostel' => false,
            'is_active' => false,
            'remarks' => null,
            'custom_field' => null,
            'dietary_restriction' => DietaryRestriction::NONE->value,
            'health_concern_id' => HealthConcern::factory(), // key to MasterHealthConcern
            'primary_school_id' => School::factory(), // key to MasterSchool
            'admission_type' => StudentAdmissionType::NEW->value,
            'enrollment_status' => EnrollmentStatus::DRAFT->value,
            'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
            'have_siblings' => false,
            'is_foreigner' => false,
            'conduct' => 'A+',
            'token' => fake()->uuid(),
            'enrollment_session_id' => EnrollmentSession::factory(), // key to EnrollmentSession
            'enrollment_user_id' => EnrollmentUser::factory(), // key to EnrollmentUser
            'billing_document_id' => null,
        ];
    }
}
