<?php

namespace Database\Factories;

use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\PaymentType;
use App\Models\Currency;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Race>
 */
class PaymentGatewayLogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $currency = Currency::factory()->create();

        return [
            'requested_by_id' => User::first()?->id ?: User::factory()->create()->id,
            'type' => PaymentType::WALLET_DEPOSIT,
            'billing_document_id' => 1,
            'provider' => PaymentProvider::PAYEX,
            'payment_method_id' => 1,
            'bank_id' => null,
            'transaction_loggable_id' => null,
            'transaction_loggable_type' => null,
            'order_id' => uniqid(),
            'currency_id' => $currency->id,
            'currency_code' => $currency->code,
            'currency_name' => $currency->name,
            'amount' => rand(1, 100),
            'status' => PaymentStatus::PENDING,
            'description' => fake()->text,
            'payment_url' => fake()->url,
            'remark' => fake()->text,
            'request_data' => [],
            'response_data' => [],
            'callback_data' => [],
            'reference_id' => uniqid(),
            'transaction_datetime' => now(),
            'token' => Str::uuid()->toString(),
            'created_at' => now(),
        ];
    }

    public function success()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => PaymentStatus::SUCCESS,
            ];
        });
    }
}
