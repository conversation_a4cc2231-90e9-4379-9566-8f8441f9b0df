<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enrollments', function (Blueprint $table) {
            $table->id();
            $table->integer('admission_year');
            $table->integer('admission_grade_id'); // key to MasterGrade
            $table->jsonb('student_name');
            $table->string('nric_no')->nullable();
            $table->string('passport_no')->nullable();
            $table->foreignId('birthplace_id')->nullable(); // key to MasterCountry
            $table->foreignId('nationality_id')->nullable(); // key to MasterCountry
            $table->date('date_of_birth')->nullable();
            $table->string('gender')->nullable();
            $table->string('birth_cert_no')->nullable();
            $table->foreignId('race_id')->nullable(); // key to MasterRace
            $table->foreignId('religion_id')->nullable(); // key to MasterReligion
            $table->string('phone_no')->nullable();
            $table->string('email')->nullable();
            $table->string('address')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('city')->nullable();
            $table->foreignId('state_id')->nullable();
            $table->foreignId('country_id')->nullable();
            $table->text('remarks')->nullable();
            $table->string('status');
            $table->integer('step');
            $table->foreignId('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enrollments');
    }
};
