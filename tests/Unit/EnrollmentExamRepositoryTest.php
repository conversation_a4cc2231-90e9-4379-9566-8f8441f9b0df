<?php

use App\Models\EnrollmentExam;
use App\Repositories\EnrollmentExamRepository;

beforeEach(function () {
    $this->enrollmentExamRepository = resolve(EnrollmentExamRepository::class);
});

test('getModelClass()', function () {
    $response = $this->enrollmentExamRepository->getModelClass();

    expect($response)->toEqual(EnrollmentExam::class);
});

test('getAll()', function () {
    $first_exam = EnrollmentExam::factory()->create();
    $second_exam = EnrollmentExam::factory()->create();
    $third_exam = EnrollmentExam::factory()->create();

    $response = $this->enrollmentExamRepository->getAll()->toArray();

    expect($response)->toEqual([
        $first_exam->toArray(),
        $second_exam->toArray(),
        $third_exam->toArray(),
    ]);
});

test('getAllPaginated()', function (int $expected_count, mixed $filter_by, mixed $filter_value, array $expected_model) {
    $exams = [
        'first' => EnrollmentExam::factory()->create()->load('enrollment.enrollmentSession'),
        'second' => EnrollmentExam::factory()->create()->load('enrollment.enrollmentSession'),
        'third' => EnrollmentExam::factory()->create()->load('enrollment.enrollmentSession'),
        'fourth' => EnrollmentExam::factory()->create()->load('enrollment.enrollmentSession'),
    ];

    $temp_filters = [
        'filter_id_1' => [
            'id' => $exams['first']->id,
        ],
        'filter_id_2' => [
            'id' => [
                $exams['first']->id,
                $exams['second']->id,
            ],
        ],
        'filter_exam_slip_number_1' => [
            'exam_slip_number' => $exams['first']->exam_slip_number,
        ],
        'filter_exam_slip_number_2' => [
            'exam_slip_number' => [
                $exams['first']->exam_slip_number,
                $exams['second']->exam_slip_number,
            ],
        ],
        'filter_enrollment_session_id' => [
            'enrollment_session_id' => $exams['first']->enrollment->enrollment_session_id,
        ],
    ];

    $actual_filters =
        isset($temp_filters[$filter_by]) ?
        $temp_filters[$filter_by] : (isset($filter_by) && isset($filter_value)
            ? [$filter_by => $filter_value] : []
        );

    $actual_filters['includes'] = ['enrollment.enrollmentSession'];

    $result = $this->enrollmentExamRepository->getAllPaginated($actual_filters)->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($exams[$value]->toArray());
    }
})->with([
    'no filters' => [4, null, null, ['first', 'second', 'third', 'fourth']],
    'filter by id single' => [1, 'filter_id_1', null, ['first']],
    'filter by id multiple' => [2, 'filter_id_2', null, ['first', 'second']],
    'filter by exam slip number single' => [1, 'filter_exam_slip_number_1', null, ['first']],
    'filter by exam slip number multiple' => [2, 'filter_exam_slip_number_2', null, ['first', 'second']],
    'filter by enrollment session id' => [1, 'filter_enrollment_session_id', null, ['first']],
    'sort by id asc' => [4, 'order_by', ['id' => 'asc'], ['first', 'second', 'third', 'fourth']],
    'sort by id desc' => [4, 'order_by', ['id' => 'desc'], ['fourth', 'third', 'second', 'first']],
]);
