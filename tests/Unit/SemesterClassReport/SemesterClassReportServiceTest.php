<?php

use App\Enums\ExportType;
use App\Enums\Gender;
use App\Enums\GuardianType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Models\ClassModel;
use App\Models\Employee;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\School;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use App\Services\DocumentPrintService;
use App\Services\Report\SemesterClassReportService;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->semesterClassReportService = app(SemesterClassReportService::class);
    $this->reportPrintService = app(ReportPrintService::class);

    $this->user = User::factory()->create();
    $this->school = School::factory()->create();

    //prepare data
    $this->students = Student::factory(5)->state(new Sequence(
        [
            'name->en' => 'Student 1',
            'primary_school_id' => $this->school->id,
        ],
        [
            'name->en' => 'Student 2',
            'primary_school_id' => null,
        ],
        [
            'name->en' => 'Student 3',
        ],
        [
            'name->en' => 'Student 4',
        ],
        [
            'name->en' => 'Student 5',
        ],
    ))->create();

    $this->guardians = Guardian::factory(8)->state(new Sequence(
        [
            'name->en' => 'Father',
        ],
        [
            'name->en' => 'Mother',
        ],
        [
            'name->en' => 'Father',
        ],
        [
            'name->en' => 'Father',
        ],
        [
            'name->en' => 'Father',
        ],
        [
            'name->en' => 'Father',
        ],
        [
            'name->en' => 'Guardian 1',
        ],
        [
            'name->en' => 'Guardian 2',
        ],
    ))->create();

    GuardianStudent::factory(8)->state(new Sequence(
        [
            'type' => GuardianType::FATHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[0]->id,
            'guardian_id' => $this->guardians[0]->id,
        ],
        [
            'type' => GuardianType::MOTHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[0]->id,
            'guardian_id' => $this->guardians[1]->id,
        ],
        [
            'type' => GuardianType::GUARDIAN,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[0]->id,
            'guardian_id' => $this->guardians[6]->id,
        ],
        [
            'type' => GuardianType::GUARDIAN,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[0]->id,
            'guardian_id' => $this->guardians[7]->id,
        ],
        [
            'type' => GuardianType::FATHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[1]->id,
            'guardian_id' => $this->guardians[2]->id,
        ],
        [
            'type' => GuardianType::FATHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[2]->id,
            'guardian_id' => $this->guardians[3]->id,
        ],
        [
            'type' => GuardianType::FATHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[3]->id,
            'guardian_id' => $this->guardians[4]->id,
        ],
        [
            'type' => GuardianType::FATHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[4]->id,
            'guardian_id' => $this->guardians[5]->id,
        ],
    ))->create();

    $this->classes = ClassModel::factory(3)->state(new Sequence(
        [
            'name' => 'J111',
        ],
        [
            'name' => 'J112',
        ],
        [
            'name' => 'J222',
        ],
    ))->create();

    $this->semester_year_setting = SemesterYearSetting::factory()->create(['year' => 2024]);

    $this->semester_settings = SemesterSetting::factory(2)
        ->state(new Sequence(
            [
                'name' => 'Semester 1',
                'semester_year_setting_id' => $this->semester_year_setting->id,
                'is_current_semester' => true
            ],
            [
                'name' => 'Semester 2',
                'semester_year_setting_id' => $this->semester_year_setting->id,
                'is_current_semester' => true
            ],
        ))
        ->create();
    $this->employees = Employee::factory(3)->state(new Sequence(
        [
            'name->en' => 'Teacher 1',
        ],
        [
            'name->en' => 'Teacher 2',
        ],
        [
            'name->en' => 'Teacher 3',
        ]

    ))->create();
    $this->semester_classes = SemesterClass::factory(3)
        ->state(new Sequence(
            [
                'class_id' => $this->classes[0]->id,
                'semester_setting_id' => $this->semester_settings[0]->id,
                'homeroom_teacher_id' => $this->employees[0]->id,
            ],
            [
                'class_id' => $this->classes[1]->id,
                'semester_setting_id' => $this->semester_settings[0]->id,
                'homeroom_teacher_id' => $this->employees[1]->id,
            ],
            [
                'class_id' => $this->classes[2]->id,
                'semester_setting_id' => $this->semester_settings[1]->id,
                'homeroom_teacher_id' => $this->employees[2]->id,
            ],
        ))
        ->create();

    $this->student_classes = StudentClass::factory(5)->state(new Sequence(
        [
            'student_id' => $this->students[0]->id,
            'seat_no' => 1,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $this->semester_classes[0]->id,
        ],
        [
            'student_id' => $this->students[1]->id,
            'seat_no' => 2,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $this->semester_classes[0]->id,
        ],
        [
            'student_id' => $this->students[2]->id,
            'seat_no' => 3,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $this->semester_classes[1]->id,
        ],
        [
            'student_id' => $this->students[3]->id,
            'seat_no' => 4,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $this->semester_classes[1]->id,
        ],
        [
            'student_id' => $this->students[4]->id,
            'seat_no' => 5,
            'semester_setting_id' => $this->semester_settings[1]->id,
            'semester_class_id' => $this->semester_classes[2]->id,
        ],
    ))->create();

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');
    SnappyPdf::fake();
    Excel::fake();
});

test('getStudentContactsReportData() filter by semester setting', function () {
    $filters = [
        'semester_setting_id' => $this->semester_settings[0]->id,
    ];

    $response = $this->semesterClassReportService->getStudentContactsReportData($filters);

    expect($response['semester_name'])->toBe($this->semester_settings[0]->name)
        ->and($response['classes'])->sequence(
            function ($data) {
                $data->toMatchArray([
                    'class_name' => $this->classes[0]->name,
                    'homeroom_teacher_name' => $this->employees[0]->getFormattedTranslations('name'),
                    'students' => [
                        [
                            'photo' => null,
                            'student_number' => $this->students[0]->student_number,
                            'seat_number' => $this->student_classes[0]->seat_no,
                            'name' => $this->students[0]->getFormattedTranslations('name'),
                            'gender' => $this->students[0]->gender->value,
                            'nric' => $this->students[0]->nric,
                            'address' => $this->students[0]->address,
                            'guardians' => [
                                [
                                    'name' => $this->guardians[0]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[0]->phone_number,
                                    'type' => GuardianType::FATHER,
                                ],
                                [
                                    'name' => $this->guardians[1]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[1]->phone_number,
                                    'type' => GuardianType::MOTHER,
                                ],
                                [
                                    'name' => $this->guardians[6]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[6]->phone_number,
                                    'type' => GuardianType::GUARDIAN,
                                ],
                                [
                                    'name' => $this->guardians[7]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[7]->phone_number,
                                    'type' => GuardianType::GUARDIAN,
                                ],
                            ]
                        ],
                        [
                            'photo' => null,
                            'student_number' => $this->students[1]->student_number,
                            'seat_number' => $this->student_classes[1]->seat_no,
                            'name' => $this->students[1]->getFormattedTranslations('name'),
                            'gender' => $this->students[1]->gender->value,
                            'nric' => $this->students[1]->nric,
                            'address' => $this->students[1]->address,
                            'guardians' => [
                                [
                                    'name' => $this->guardians[2]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[2]->phone_number,
                                    'type' => GuardianType::FATHER,
                                ]
                            ]
                        ]
                    ]
                ]);
            },
            function ($data) {
                $data->toMatchArray([
                    'class_name' => $this->classes[1]->name,
                    'homeroom_teacher_name' => $this->employees[1]->getFormattedTranslations('name'),
                    'students' => [
                        [
                            'photo' => null,
                            'student_number' => $this->students[2]->student_number,
                            'seat_number' => $this->student_classes[2]->seat_no,
                            'name' => $this->students[2]->getFormattedTranslations('name'),
                            'gender' => $this->students[2]->gender->value,
                            'nric' => $this->students[2]->nric,
                            'address' => $this->students[2]->address,
                            'guardians' => [
                                [
                                    'name' => $this->guardians[3]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[3]->phone_number,
                                    'type' => GuardianType::FATHER,
                                ]
                            ]
                        ],
                        [
                            'photo' => null,
                            'student_number' => $this->students[3]->student_number,
                            'seat_number' => $this->student_classes[3]->seat_no,
                            'name' => $this->students[3]->getFormattedTranslations('name'),
                            'gender' => $this->students[3]->gender->value,
                            'nric' => $this->students[3]->nric,
                            'address' => $this->students[3]->address,
                            'guardians' => [
                                [
                                    'name' => $this->guardians[4]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[4]->phone_number,
                                    'type' => GuardianType::FATHER,
                                ]
                            ]
                        ]
                    ]
                ]);
            }
        );
});

test('getStudentContactsReportData() filter by semester class', function () {
    $filters = [
        'semester_class_id' => $this->semester_classes[0]->id,
    ];

    $response = $this->semesterClassReportService->getStudentContactsReportData($filters);

    expect($response['semester_name'])->toBe($this->semester_settings[0]->name)
        ->and($response['classes'])->sequence(
            function ($data) {
                $data->toMatchArray([
                    'class_name' => $this->classes[0]->name,
                    'homeroom_teacher_name' => $this->employees[0]->getFormattedTranslations('name'),
                    'students' => [
                        [
                            'photo' => null,
                            'student_number' => $this->students[0]->student_number,
                            'seat_number' => $this->student_classes[0]->seat_no,
                            'name' => $this->students[0]->getFormattedTranslations('name'),
                            'gender' => $this->students[0]->gender->value,
                            'nric' => $this->students[0]->nric,
                            'address' => $this->students[0]->address,
                            'guardians' => [
                                [
                                    'name' => $this->guardians[0]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[0]->phone_number,
                                    'type' => GuardianType::FATHER,
                                ],
                                [
                                    'name' => $this->guardians[1]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[1]->phone_number,
                                    'type' => GuardianType::MOTHER,
                                ],
                                [
                                    'name' => $this->guardians[6]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[6]->phone_number,
                                    'type' => GuardianType::GUARDIAN,
                                ],
                                [
                                    'name' => $this->guardians[7]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[7]->phone_number,
                                    'type' => GuardianType::GUARDIAN,
                                ],
                            ]
                        ],
                        [
                            'photo' => null,
                            'student_number' => $this->students[1]->student_number,
                            'seat_number' => $this->student_classes[1]->seat_no,
                            'name' => $this->students[1]->getFormattedTranslations('name'),
                            'gender' => $this->students[1]->gender->value,
                            'nric' => $this->students[1]->nric,
                            'address' => $this->students[1]->address,
                            'guardians' => [
                                [
                                    'name' => $this->guardians[2]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[2]->phone_number,
                                    'type' => GuardianType::FATHER,
                                ]
                            ]
                        ]
                    ]
                ]);
            }
        );
});

test('getStudentContactsReportData() export pdf', function () {
    $filters = [
        'semester_setting_id' => $this->semester_settings[0]->id,
    ];

    $response = $this->semesterClassReportService->getStudentContactsReportData($filters);

    $report_data = ['data' => $response, 'title' => 'Student Contact Report' . " ({$response['semester_name']})"];
    $report_view_name = 'reports.semester-classes.student-contacts';
    $file_name = 'semester-classes-report-student-contacts';

    $export_type = ExportType::PDF;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    $expected_headers = ['Photo', 'Seat No.', "Student No.", 'Name', "Gender", 'I/C No.', 'Address', 'Guardian Type', 'Guardian Name', 'Guardian Phone No.'];

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    SnappyPdf::assertSee($report_data['title']);

    foreach ($report_data['data']['classes'] as $class) {
        SnappyPdf::assertSee('Class : ' . e($class['class_name']));
        SnappyPdf::assertSee('Homeroom Teacher : ' . e($class['homeroom_teacher_name']));

        foreach ($expected_headers as $header) {
            SnappyPdf::assertSee($header);
        }

        foreach ($class['students'] as $student) {
            if ($student['photo']) {
                SnappyPdf::assertSee($student['photo']);
            }

            SnappyPdf::assertSee($student['student_number']);
            SnappyPdf::assertSee($student['seat_number']);
            SnappyPdf::assertSee(e($student['name']));
            SnappyPdf::assertSee($student['gender']);
            SnappyPdf::assertSee($student['nric']);
            SnappyPdf::assertSee(e($student['address']));

            foreach ($student['guardians'] as $guardian) {
                SnappyPdf::assertSee($guardian['type']->value);
                SnappyPdf::assertSee(e($guardian['name']));
                SnappyPdf::assertSee($guardian['phone_number']);
            }
        }
    }
});


test('getStudentContactsReportData() export excel', function () {
    $filters = [
        'semester_setting_id' => $this->semester_settings[0]->id,
    ];

    $response = $this->semesterClassReportService->getStudentContactsReportData($filters);

    $report_data = ['data' => $response, 'title' => 'Student Contact Report' . " ({$response['semester_name']})"];
    $report_view_name = 'reports.semester-classes.student-contacts';
    $file_name = 'semester-classes-report-student-contacts';

    $export_type = ExportType::EXCEL;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    $expected_headers = ['Photo', 'Seat No.', "Student No.", 'Name', "Gender", 'I/C No.', 'Address', 'Guardian Type', 'Guardian Name', 'Guardian Phone No.'];

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $report_data, $expected_headers) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $expected_headers = array_map(function ($header) {
                return "<th>$header</th>";
            }, $expected_headers);

            $view->assertSee($report_data['title']);

            foreach ($report_data['data']['classes'] as $class) {
                $view->assertSee('Class : ' . $class['class_name']);
                $view->assertSee('Homeroom Teacher : ' . $class['homeroom_teacher_name']);

                $view->assertSeeInOrder($expected_headers, false);

                foreach ($class['students'] as $student) {
                    if ($student['photo']) {
                        $view->assertSee($student['photo']);
                    }

                    $view->assertSee($student['student_number']);
                    $view->assertSee($student['seat_number']);
                    $view->assertSee($student['name']);
                    $view->assertSee($student['gender']);
                    $view->assertSee($student['nric']);
                    $view->assertSee($student['address']);

                    foreach ($student['guardians'] as $guardian) {
                        $view->assertSee($guardian['type']->value);
                        $view->assertSee($guardian['name']);
                        $view->assertSee($guardian['phone_number']);
                    }
                }
            }

            return true;
        }
    );
});

test('getStudentDetailsReportData()', function () {
    $filters = [
        'semester_class_id' => $this->semester_classes[0]->id,
    ];

    $response = $this->semesterClassReportService->getStudentDetailsReportData($filters);

    expect($response)->sequence(
        function ($data) {
            $data->toMatchArray([
                'photo' => $this->students[0]->photo,
                'name' => $this->students[0]->getFormattedTranslations('name'),
                'student_number' => $this->students[0]->student_number,
                'address' => $this->students[0]->address,
                'class_name' => $this->classes[0]->name,
                'semester_name' => $this->semester_settings[0]->name,
                'primary_school_name' => $this->school->name,
                'date_of_birth' => $this->students[0]->date_of_birth,
                'birthplace' => $this->students[0]->birthplace,
                'religion' => $this->students[0]->religion->name,
                'guardians' => [
                    GuardianType::FATHER->value => [
                        'name' => $this->guardians[0]->getFormattedTranslations('name'),
                        'nationality' => $this->guardians[0]->country?->name,
                        'email' => $this->guardians[0]->email,
                        'education' => $this->guardians[0]->education?->name,
                        'religion' => $this->guardians[0]->religion->name,
                        'phone_number' => $this->guardians[0]->phone_number,
                        'occupation' => $this->guardians[0]->occupation,
                    ],
                    GuardianType::MOTHER->value => [
                        'name' => $this->guardians[1]->getFormattedTranslations('name'),
                        'nationality' => $this->guardians[1]->country?->name,
                        'email' => $this->guardians[1]->email,
                        'education' => $this->guardians[1]->education?->name,
                        'religion' => $this->guardians[1]->religion->name,
                        'phone_number' => $this->guardians[1]->phone_number,
                        'occupation' => $this->guardians[1]->occupation,
                    ],
                    GuardianType::GUARDIAN->value => [
                        [
                            'name' => $this->guardians[6]->getFormattedTranslations('name'),
                            'nationality' => $this->guardians[6]->country?->name,
                            'email' => $this->guardians[6]->email,
                            'education' => $this->guardians[6]->education?->name,
                            'religion' => $this->guardians[6]->religion->name,
                            'phone_number' => $this->guardians[6]->phone_number,
                            'occupation' => $this->guardians[6]->occupation,
                        ],
                        [
                            'name' => $this->guardians[7]->getFormattedTranslations('name'),
                            'nationality' => $this->guardians[7]->country?->name,
                            'email' => $this->guardians[7]->email,
                            'education' => $this->guardians[7]->education?->name,
                            'religion' => $this->guardians[7]->religion->name,
                            'phone_number' => $this->guardians[7]->phone_number,
                            'occupation' => $this->guardians[7]->occupation,
                        ]
                    ],
                ]
            ]);
        },
        function ($data) {
            $data->toMatchArray([
                'photo' => $this->students[1]->photo,
                'name' => $this->students[1]->getFormattedTranslations('name'),
                'student_number' => $this->students[1]->student_number,
                'address' => $this->students[1]->address,
                'class_name' => $this->classes[0]->name,
                'semester_name' => $this->semester_settings[0]->name,
                'primary_school_name' => null,
                'date_of_birth' => $this->students[1]->date_of_birth,
                'birthplace' => $this->students[1]->birthplace,
                'religion' => $this->students[1]->religion->name,
                'guardians' => [
                    GuardianType::FATHER->value => [
                        'name' => $this->guardians[2]->getFormattedTranslations('name'),
                        'nationality' => $this->guardians[2]->country?->name,
                        'email' => $this->guardians[2]->email,
                        'education' => $this->guardians[2]->education?->name,
                        'religion' => $this->guardians[2]->religion->name,
                        'phone_number' => $this->guardians[2]->phone_number,
                        'occupation' => $this->guardians[2]->occupation,
                    ],
                    GuardianType::MOTHER->value => [
                        'name' => null,
                        'nationality' => null,
                        'email' => null,
                        'education' => null,
                        'religion' => null,
                        'phone_number' => null,
                        'occupation' => null,
                    ],
                    GuardianType::GUARDIAN->value => [],
                ]
            ]);
        }
    );
});

test('getStudentDetailsReportData() export pdf', function () {
    $filters = [
        'semester_class_id' => $this->semester_classes[0]->id,
    ];

    $response = $this->semesterClassReportService->getStudentDetailsReportData($filters);

    $report_data = ['data' => $response];
    $report_view_name = 'reports.semester-classes.student-details';
    $file_name = 'semester-classes-report-student-details';

    $export_type = ExportType::PDF;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    $expected_headers = [
        'Student Name',
        'Semester/Class',
        'Student No.',
        'Student School From',
        'Student Address',
        'Student DOB',
        'Nativeplace',
        'Religion',

        'Father Name',
        'Father Religion',
        'Father Nationality',
        'Father Phone No.',
        'Father Email',
        'Father Work',
        'Father Education',

        'Mother Name',
        'Mother Religion',
        'Mother Nationality',
        'Mother Phone No.',
        'Mother Email',
        'Mother Work',
        'Mother Education',

        'Guardian Name',
        'Guardian Religion',
        'Guardian Nationality',
        'Guardian Phone No.',
        'Guardian Email',
        'Guardian Work',
        'Guardian Education',
    ];

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    SnappyPdf::assertSee('Student Details Report (' . $this->semester_settings[0]->name . ')');
    SnappyPdf::assertSee('Print Date : ' . now()->setTimezone(config('school.timezone'))->format('d M Y, h:i A'));

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    foreach ($report_data['data'] as $student) {
        SnappyPdf::assertSee(e($student['name']));
        SnappyPdf::assertSee($student['semester_name'] . '/' . $student['class_name']);

        if ($student['photo']) {
            SnappyPdf::assertSee($student['photo']);
        }

        SnappyPdf::assertSee($student['student_number']);
        SnappyPdf::assertSee(e($student['primary_school_name']));
        SnappyPdf::assertSee(e($student['address']));
        SnappyPdf::assertSee($student['date_of_birth']);
        SnappyPdf::assertSee(e($student['birthplace']));
        SnappyPdf::assertSee(e($student['religion']));


        foreach ($student['guardians'] as $type => $guardian) {
            if ($type == GuardianType::GUARDIAN->value) {
                foreach ($guardian as $data) {
                    $type = strtoupper($type);
                    SnappyPdf::assertSee('STUDENT (' . $type . ') INFORMATION');
                    SnappyPdf::assertSee(e($data['name']));
                    SnappyPdf::assertSee(e($data['religion']));
                    SnappyPdf::assertSee(e($data['nationality']));

                    if ($data['phone_number']) {
                        SnappyPdf::assertSee($data['phone_number']);
                    }

                    if ($data['email']) {
                        SnappyPdf::assertSee($data['email']);
                    }

                    if ($data['occupation']) {
                        SnappyPdf::assertSee($data['occupation']);
                    }
                }
            } else {
                $type = strtoupper($type);
                SnappyPdf::assertSee('STUDENT (' . $type . ') INFORMATION');
                SnappyPdf::assertSee(e($guardian['name']));
                SnappyPdf::assertSee(e($guardian['religion']));
                SnappyPdf::assertSee(e($guardian['nationality']));

                if ($guardian['phone_number']) {
                    SnappyPdf::assertSee($guardian['phone_number']);
                }

                if ($guardian['email']) {
                    SnappyPdf::assertSee($guardian['email']);
                }

                if ($guardian['occupation']) {
                    SnappyPdf::assertSee($guardian['occupation']);
                }
            }
        }
    }
});

test('getReportHomeroomTeachers() filter by semester setting id', function () {
    $filters = [
        'semester_setting_id' => $this->semester_settings[0]->id,
    ];

    $response = $this->semesterClassReportService->getReportHomeroomTeachers($filters);

    $this->semester_classes[0]->load('classModel.grade', 'homeroomTeacher');
    $this->semester_classes[1]->load('classModel.grade', 'homeroomTeacher');

    expect($response['homeroom_teachers'])->toMatchArray([
        $this->semester_classes[0]->toArray(),
        $this->semester_classes[1]->toArray(),
    ])->and($response['semester_name'])->toBe($this->semester_settings[0]->name);
});

test('getReportHomeroomTeachers() export pdf', function () {
    $filters = [
        'semester_setting_id' => $this->semester_settings[0]->id,
    ];

    $response = $this->semesterClassReportService->getReportHomeroomTeachers($filters);

    $report_data = [
        'data' => $response['homeroom_teachers'],
        'title' => 'Homeroom Teacher Report' . " ({$this->semester_settings[0]->name})"
    ];
    $report_view_name = 'reports.semester-classes.homeroom-teachers';
    $file_name = 'semester-classes-report-homeroom-teachers';

    $export_type = ExportType::from(ExportType::PDF->value);
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    $expected_headers = ['No.', "Class Code", 'Class', "Grade", 'Homeroom Teacher'];

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    SnappyPdf::assertSee($report_data['title']);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    $i = 1;

    foreach ($report_data['data'] as $class) {
        SnappyPdf::assertSee($i);
        SnappyPdf::assertSee(e($class->classModel->code));
        SnappyPdf::assertSee(e($class->classModel->name));
        SnappyPdf::assertSee(e($class->classModel->grade->name));
        SnappyPdf::assertSee(e($class->homeroomTeacher->getFormattedTranslations('name')));

        $i++;
    }
});

test('getReportHomeroomTeachers() export excel', function () {
    $filters = [
        'semester_setting_id' => $this->semester_settings[0]->id,
    ];

    $response = $this->semesterClassReportService->getReportHomeroomTeachers($filters);

    $report_data = [
        'data' => $response['homeroom_teachers'],
        'title' => 'Homeroom Teacher Report' . " ({$this->semester_settings[0]->name})"
    ];
    $report_view_name = 'reports.semester-classes.homeroom-teachers';
    $file_name = 'semester-classes-report-homeroom-teachers';

    $export_type = ExportType::from(ExportType::EXCEL->value);
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    $expected_headers = ['No.', "Class Code", 'Class', "Grade", 'Homeroom Teacher'];

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $report_data, $expected_headers) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $expected_headers = array_map(function ($header) {
                return "<th>$header</th>";
            }, $expected_headers);

            $view->assertSee($report_data['title']);

            $view->assertSeeInOrder($expected_headers, false);

            $i = 1;

            foreach ($report_data['data'] as $class) {
                $view->assertSee($i);
                $view->assertSee(e($class->classModel->code));
                $view->assertSee(e($class->classModel->name));
                $view->assertSee(e($class->classModel->grade->name));
                $view->assertSee(e($class->homeroomTeacher->getFormattedTranslations('name')));

                $i++;
            }

            return true;
        }
    );
});

test('getAllStudentsBySemesterClassReportData()', function () {
    $students = Student::factory(4)->state(new Sequence(
        [
            'name->en' => 'Jon',
        ],
        [
            'name->en' => 'Albert',
        ],
        [
            'name->en' => 'Karen',
        ],
        [
            'name->en' => 'Dee',
        ],
    ))->create();

    $teacher = Employee::factory()->create([
        'name->en' => 'Dr. Goopta',
    ]);

    $semester_class = SemesterClass::factory()->create([
        'class_id' => $this->classes[0]->id,
        'semester_setting_id' => $this->semester_settings[0]->id,
        'homeroom_teacher_id' => $teacher->id,
    ]);

    foreach ($students as $index => $student) {
        $seat_no = $students->count() - $index;

        StudentClass::factory()->create([
            'student_id' => $student->id,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $semester_class->id,
            'seat_no' => $seat_no,
        ]);
    }

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $filters = [
        'semester_class_id' => $semester_class->id,
    ];

    $response = $this->semesterClassReportService->getAllStudentsBySemesterClassReportData($filters);

    expect($response['students']->toArray())
        ->toHaveKey('0.id', $students[3]->id)
        ->toHaveKey('0.name.en', 'Dee')
        ->toHaveKey('0.latest_primary_class_by_semester_settings.0.semester_class_id', $semester_class->id)
        ->toHaveKey('0.latest_primary_class_by_semester_settings.0.semester_setting_id', $this->semester_settings[0]->id)
        ->toHaveKey('1.id', $students[2]->id)
        ->toHaveKey('1.name.en', 'Karen')
        ->toHaveKey('1.latest_primary_class_by_semester_settings.0.semester_class_id', $semester_class->id)
        ->toHaveKey('1.latest_primary_class_by_semester_settings.0.semester_setting_id', $this->semester_settings[0]->id)
        ->toHaveKey('2.id', $students[1]->id)
        ->toHaveKey('2.name.en', 'Albert')
        ->toHaveKey('2.latest_primary_class_by_semester_settings.0.semester_class_id', $semester_class->id)
        ->toHaveKey('2.latest_primary_class_by_semester_settings.0.semester_setting_id', $this->semester_settings[0]->id)
        ->toHaveKey('3.id', $students[0]->id)
        ->toHaveKey('3.name.en', 'Jon')
        ->toHaveKey('3.latest_primary_class_by_semester_settings.0.semester_class_id', $semester_class->id)
        ->toHaveKey('3.latest_primary_class_by_semester_settings.0.semester_setting_id', $this->semester_settings[0]->id);
});

test('getAllStudentsBySemesterClassReportData() export pdf', function () {
    $students = Student::factory(4)->state(new Sequence(
        [
            'name->en' => 'Jon',
        ],
        [
            'name->en' => 'Albert',
        ],
        [
            'name->en' => 'Karen',
        ],
        [
            'name->en' => 'Dee',
        ],
    ))->create();

    $teacher = Employee::factory()->create([
        'name->en' => 'Dr. Goopta',
    ]);

    $semester_class = SemesterClass::factory()->create([
        'class_id' => $this->classes[0]->id,
        'semester_setting_id' => $this->semester_settings[0]->id,
        'homeroom_teacher_id' => $teacher->id,
    ]);

    foreach ($students as $index => $student) {
        $seat_no = $students->count() - $index;

        StudentClass::factory()->create([
            'student_id' => $student->id,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $semester_class->id,
            'seat_no' => $seat_no,
        ]);
    }

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $filters = [
        'semester_class_id' => $semester_class->id,
    ];

    $response = $this->semesterClassReportService->getAllStudentsBySemesterClassReportData($filters);

    SnappyPdf::fake();

    $report_data = ['data' => $response];
    $report_view_name = 'reports.semester-classes.by-students-in-semester-class';
    $file_name = 'students-list';

    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor(ExportType::PDF);

    $adapter
        ->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    $expected_headers = ['No.', 'Student Number', "Name (zh)", 'Name (en)', 'Gender'];

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    SnappyPdf::assertSee($this->semester_settings[0]->name);
    SnappyPdf::assertSee(e($this->classes[0]->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($this->classes[0]->getTranslation('name', 'zh')));
    SnappyPdf::assertSee(e($teacher->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($teacher->getTranslation('name', 'zh')));
    SnappyPdf::assertSee(count($students));
    SnappyPdf::assertSee('* = 离校');

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    foreach ($students as $student) {
        if ($student->gender === Gender::FEMALE) {
            $gender = 'F';
        } elseif ($student->gender === Gender::MALE) {
            $gender = 'M';
        }

        SnappyPdf::assertSee($student->latestPrimaryClassBySemesterSettings->first()->seat_no);
        SnappyPdf::assertSee($student->student_number);
        SnappyPdf::assertSee(e($student->getTranslation('name', 'en')));
        SnappyPdf::assertSee(e($student->getTranslation('name', 'zh')));
        SnappyPdf::assertSee($gender);
    }
});

test('getAllStudentsBySemesterClassReportData() export excel', function () {
    $students = Student::factory(4)->state(new Sequence(
        [
            'name->en' => 'Jon',
        ],
        [
            'name->en' => 'Albert',
        ],
        [
            'name->en' => 'Karen',
        ],
        [
            'name->en' => 'Dee',
        ],
    ))->create();

    $teacher = Employee::factory()->create([
        'name->en' => 'Dr. Goopta',
    ]);

    $semester_class = SemesterClass::factory()->create([
        'class_id' => $this->classes[0]->id,
        'semester_setting_id' => $this->semester_settings[0]->id,
        'homeroom_teacher_id' => $teacher->id,
    ]);

    foreach ($students as $student) {
        StudentClass::factory()->create([
            'student_id' => $student->id,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $semester_class->id,
        ]);
    }

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $filters = [
        'semester_class_id' => $semester_class->id,
    ];

    $response = $this->semesterClassReportService->getAllStudentsBySemesterClassReportData($filters);


    Excel::fake();

    $report_data = ['data' => $response];
    $report_view_name = 'reports.semester-classes.by-students-in-semester-class';
    $file_name = 'students-list';

    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor(ExportType::EXCEL);

    $adapter
        ->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    $expected_headers = ['No.', 'Student Number', "Name (zh)", 'Name (en)', 'Gender'];

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $teacher, $expected_headers, $students) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($this->semester_settings[0]->name);
            $view->assertSee($this->classes[0]->getTranslation('name', 'en'));
            $view->assertSee($this->classes[0]->getTranslation('name', 'zh'));
            $view->assertSee($teacher->getTranslation('name', 'en'));
            $view->assertSee($teacher->getTranslation('name', 'zh'));
            $view->assertSee(count($students));
            $view->assertSee('* = 离校');

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            foreach ($students as $student) {
                if ($student->gender === Gender::FEMALE) {
                    $gender = 'F';
                } elseif ($student->gender === Gender::MALE) {
                    $gender = 'M';
                }

                $view->assertSee($student->latestPrimaryClassBySemesterSettings->first()->seat_no);
                $view->assertSee($student->student_number);
                $view->assertSee($student->getTranslation('name', 'en'));
                $view->assertSee($student->getTranslation('name', 'zh'));
                $view->assertSee($gender);
            }

            return true;
        }
    );
});
