<?php

use App\Models\Enrollment;
use App\Models\Student;

beforeEach(function () {
});

test('getFormattedTranslations()', function () {
    $student = Student::factory()->create([
        'name' => ['en' => 'one', 'zh' => '一'],
    ]);
    $enrollment = Enrollment::factory()->create([
        'student_name' => ['en' => 'two', 'zh' => '二'],
    ]);

    expect($student->getFormattedTranslations('name'))->tobe('one - 一');
    expect($enrollment->getFormattedTranslations('student_name'))->tobe('two - 二');

    // invalid key
    expect($student->getFormattedTranslations('test'))->tobe('-');

    // empty value
    $student = Student::factory()->create([
        'name' => [
            'en' => '',
            'zh' => '',
        ],
    ]);
    $enrollment = Enrollment::factory()->create([
        'student_name' => [
            'en' => '',
            'zh' => '',
        ],
    ]);
    expect($student->getFormattedTranslations('name'))->tobe('-');
    expect($enrollment->getFormattedTranslations('student_name'))->tobe('-');
});

