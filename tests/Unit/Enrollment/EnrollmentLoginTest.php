<?php

use App\Models\EnrollmentUser;
use App\Services\EnrollmentLoginOtpService;
use App\Services\EnrollmentLoginService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Mockery\MockInterface;

beforeEach(function () {
    Carbon::setTestNow('2024-01-01 00:00:00');
    $this->routePrefix = 'enrollments.';
});


test('request otp: by phone', function () {
    // Create user
    $user = EnrollmentUser::factory()->create([
        'phone_number' => '1111122222'
    ]);

    $payload = [
        'phone_number' => $user->phone_number,
    ];

    $this->partialMock(EnrollmentLoginService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('requestOtp')->with(array_merge($payload))->once();
    });

    // Request OTP
    $response = $this->postJson(route($this->routePrefix . 'request-otp'), $payload);

    $response->assertStatus(200);
    expect($response->json())->toMatchArray([
        'status' => 'OK',
        'code' => 200,
        'message' => 'You will receive SMS OTP at ' . $user->phone_number . ' if there is a valid account linked with this phone number.',
        'data' => [
            'expired_at' => now()
                ->addSeconds(EnrollmentLoginOtpService::OTP_EXPIRY_SECONDS)
                ->toISOString()
        ]
    ]);
});


test('request otp by phone: phone not found', function () {
    // Create user
    $user = EnrollmentUser::factory()->create([
        'phone_number' => '***********'
    ]);

    $payload = [
        'phone_number' => '************',
    ];

    // Request OTP
    $response = $this->postJson(route($this->routePrefix . 'request-otp'), $payload);

    $response->assertStatus(200);
    expect($response->json())->toMatchArray([
        'status' => 'OK',
        'code' => 200,
        'message' => 'You will receive SMS OTP at ' . $payload['phone_number'] . ' if there is a valid account linked with this phone number.',
        'data' => [
            'expired_at' => now()
                ->addSeconds(EnrollmentLoginOtpService::OTP_EXPIRY_SECONDS)
                ->toISOString()
        ]
    ]);
});

test('login: with phone_number and otp', function () {
    // Create user
    $user = EnrollmentUser::factory()->create([
        'phone_number' => '***********'
    ]);

    // Create OTP
    $otp = rand(100000, 999999);
    $user->otps()->create([
        'otp' => hash('sha256', $otp),
        'expired_at' => now()->addMinutes(5)
    ]);

    $payload = [
        'phone_number' => $user->phone_number,
        'otp' => $otp,
    ];

    $this->mock(EnrollmentLoginService::class, function (MockInterface $mock) use ($payload, $user) {
        $mock->shouldReceive('login')->with($payload)->once();
        $mock->shouldReceive('generateToken')->once()->andReturn('longlongstringtoken');
    });

    // Login
    $response = $this->postJson(route($this->routePrefix . 'login'), $payload);

    $response->assertStatus(200);

    expect($response->json())->toMatchArray([
        'status' => 'OK',
        'code' => 200,
        'message' => 'Success.',
        'data' => [
            'token' => 'longlongstringtoken',
        ]
    ]);
});

test('login: with phone_number invalid otp', function () {
    // Create user
    $user = EnrollmentUser::factory()->create([
        'phone_number' => '***********'
    ]);

    // Create OTP
    $otp = 123456;
    $user->otps()->create([
        'otp' => hash('sha256', $otp),
        'expired_at' => now()->addMinutes(5)
    ]);

    $payload = [
        'phone_number' => $user->phone_number,
        'otp' => 111111,
    ];

    // Login
    $response = $this->postJson(route($this->routePrefix . 'login'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse();
    expect($response)->toMatchArray([
        'status' => 'ERROR',
        'code' => 4004,
        'message' => null,
        'error' => 'Invalid OTP.',
        'data' => null
    ]);

    // Login with invalid phone number
    $payload = [
        'phone_number' => '2222222222',
        'otp' => 123456,
        'use_otp' => true
    ];

    // Login
    $response = $this->postJson(route($this->routePrefix . 'login'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse();
    expect($response)->toMatchArray([
        'status' => 'ERROR',
        'code' => 4006,
        'message' => null,
        'error' => 'The provided credentials does not match our records.',
        'data' => null
    ]);
});

test('logout', function () {
    $user = EnrollmentUser::factory()->create();

    // Create multiple token
    $user->createToken('test-token');
    $user->createToken('test-token');
    $token = $user->createToken('test-token')->accessToken;
    $user->withAccessToken($token);

    $this->assertDatabaseCount('personal_access_tokens', 3);

    $response = $this->actingAs($user, 'enrollment')->postJson(route($this->routePrefix . 'logout'))->json();
    //dd($response);
    // Only one gets deleted
    $this->assertDatabaseCount('personal_access_tokens', 2);

    expect($response)->toHaveSuccessGeneralResponse();
});
