<?php

use App\Helpers\SystemHelper;
use App\Models\Bank;
use App\Models\BillingDocument;
use App\Models\BillingDocumentAdvanceTransaction;
use App\Models\BillingDocumentLineItem;
use App\Models\DiscountSetting;
use App\Models\Employee;
use App\Models\GlAccount;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\Media;
use App\Models\PaymentMethod;
use App\Models\PaymentRequest;
use App\Models\Scholarship;
use App\Models\ScholarshipAward;
use App\Models\Student;
use App\Models\UnpaidItem;
use App\Repositories\UnpaidItemRepository;
use App\Services\Billing\AccountingService;
use Carbon\Carbon;
use Database\Seeders\BankSeeder;
use Database\Seeders\CurrencySeeder;
use Database\Seeders\EmployeeSeeder;
use Database\Seeders\GlAccountSeeder;
use Database\Seeders\LegalEntitySeeder;
use Database\Seeders\PaymentMethodSeeder;
use Database\Seeders\PaymentTermsSeeder;
use Database\Seeders\PermissionSeeder;
use Database\Seeders\ProductSeeder;
use Database\Seeders\TaxSeeder;
use Database\Seeders\UomSeeder;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Event;

beforeEach(function () {
    $this->accountingService = app(AccountingService::class);

    $this->seed([
        CurrencySeeder::class,
        ProductSeeder::class,
        TaxSeeder::class,
        UomSeeder::class,
        GlAccountSeeder::class,
        PaymentMethodSeeder::class,
        PaymentTermsSeeder::class,
        LegalEntitySeeder::class,
        PermissionSeeder::class,
        EmployeeSeeder::class,
        BankSeeder::class,
    ]);

    $this->admin = SystemHelper::getSystemEmployee();

    config(['media-library.disk_name' => 'local']);
});

test('createBillingDocumentFromUnpaidItem(), with amount after tax is more than 0', function () {
    $student = Student::factory()->create();
    $user = $student->user;

    $unpaid_items = UnpaidItem::factory(2)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100, // total for 2 unpaid items is 200
    ]);

    $this->assertDatabaseCount('billing_documents', 0);
    $this->assertDatabaseCount('billing_document_line_items', 0);

    $payload = [
        'unpaid_item_ids' => [
            $unpaid_items[0]->id,
            $unpaid_items[1]->id,
        ],
    ];

    $response = $this->accountingService
        ->setUser($user)
        ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload['unpaid_item_ids']))
        ->validateUserCreateBillingDocument()
        ->createBillingDocumentFromUnpaidItem();

    expect($response)->toBeInstanceOf(BillingDocument::class);

    $this->assertDatabaseCount('payment_gateway_logs', 0);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 2);
    $this->assertDatabaseCount('payments', 0);

    $created_billing_document = BillingDocument::where([
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ])->first();

    foreach ($unpaid_items as $unpaid_item) {
        $this->assertDatabaseHas('unpaid_items', [
            'id' => $unpaid_item->id,
            'billing_document_id' => $created_billing_document->id,
            'status' => UnpaidItem::STATUS_PENDING,
        ]);

        $this->assertDatabaseHas('billing_document_line_items', [
            'billing_document_id' => $created_billing_document->id,
            'gl_account_code' => $unpaid_item->gl_account_code,
            'description' => $unpaid_item->description,
            'amount_before_tax' => $unpaid_item->amount_before_tax,
            'product_id' => $unpaid_item->product_id,
            'currency_code' => $unpaid_item->currency_code,
            'quantity' => $unpaid_item->quantity,
            'unit_price' => $unpaid_item->unit_price,
            'billable_item_type' => get_class($unpaid_item),
            'billable_item_id' => $unpaid_item->id,
            'is_discount' => false,
            'offset_billing_document_id' => null,
        ]);
    }

    $this->assertDatabaseHas('billing_documents', [
        'id' => $created_billing_document->id,
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'currency_code' => config('school.currency_code'),
        'amount_before_tax' => 200,
        'amount_before_tax_after_less_advance' => 200,
        'amount_after_tax' => 200,
        'tax_amount' => 0,
        'legal_entity_id' => \App\Helpers\SystemHelper::getDefaultLegalEntity()->id,
        'tax_percentage' => 0,
        'remit_to_id' => null,
    ]);
});

test('createBillingDocumentFromUnpaidItem(), with amount after tax is 0', function () {
    $student = Student::factory()->create();
    $user = $student->user;

    $unpaid_items = UnpaidItem::factory(2)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 0, // total for 2 unpaid items is 0
    ]);

    $this->assertDatabaseCount('billing_documents', 0);
    $this->assertDatabaseCount('billing_document_line_items', 0);

    $payload = [
        'unpaid_item_ids' => [
            $unpaid_items[0]->id,
            $unpaid_items[1]->id,
        ],
    ];

    Event::fake();

    $response = $this->accountingService
        ->setUser($user)
        ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload['unpaid_item_ids']))
        ->validateUserCreateBillingDocument()
        ->createBillingDocumentFromUnpaidItem();

    expect($response)->toBeInstanceOf(BillingDocument::class);

    $this->assertDatabaseCount('payment_gateway_logs', 0);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 2);
    $this->assertDatabaseCount('payments', 1);

    Event::assertDispatched(\App\Events\InvoicePaidEvent::class);

    $this->assertDatabaseHas('payments', [
        'billing_document_id' => $response->id,
        'payment_method_id' => SystemHelper::getSystemPaymentMethod()->id,
        'payment_reference_no' => $response->reference_no . '-PAID',
        'amount_received' => $response->amount_after_tax,
        'paid_at' => now(),
        'created_by_employee_id' => SystemHelper::getSystemEmployee()->id,
    ]);

    $this->assertDatabaseHas('billing_documents', [
        'id' => $response->id,
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'currency_code' => config('school.currency_code'),
        'amount_before_tax' => 0,
        'amount_before_tax_after_less_advance' => 0,
        'amount_after_tax' => 0,
        'tax_amount' => 0,
        'legal_entity_id' => \App\Helpers\SystemHelper::getDefaultLegalEntity()->id,
        'tax_percentage' => 0,
        'remit_to_id' => null,
    ]);
});

test('createBillingDocumentFromUnpaidItem(), ensure the sequence is correct for discount + advance', function () {
    $student = Student::factory()->create();
    $user = $student->user;

    $unpaid_item_1 = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-02-01', // FEB
    ]);

    $unpaid_item_2 = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 80,
        'period' => '2024-01-01', // JAN
    ]);

    // ADVANCE
    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 10, // only 10
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_address' => $student->getBillToAddress(),
    ]);

    BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);


    // SCHOLARSHIP
    $scholarship = Scholarship::factory()->create([
        'description' => 'Pin Hwa High School High Achievers Scholarship',
    ]);

    $scholarship_award1 = ScholarshipAward::factory()->create([
        'scholarship_id' => $scholarship->id,
        'student_id' => $student->id,
    ]);

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'source_id' => $scholarship_award1->id,
        'source_type' => get_class($scholarship_award1),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 20 // 20 discount
    ]);

    // DISCOUNT without scholarship
    $setting2 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 40 // 40 discount
    ]);

    $payload = [
        'unpaid_item_ids' => [
            $unpaid_item_2->id,
            $unpaid_item_1->id,
        ],
    ];

    $response = $this->accountingService
        ->setUser($user)
        ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload['unpaid_item_ids']))
        ->validateUserCreateBillingDocument()
        ->createBillingDocumentFromUnpaidItem();

    $response->loadMissing('lineItems');

    expect($response->lineItems->toArray())
        ->toHaveKey('0.billable_item_id', $unpaid_item_2->id) // unpaid_item_2 is period JAN
        ->toHaveKey('0.billable_item_type', get_class($unpaid_item_2))
        ->toHaveKey('0.amount_before_tax', $unpaid_item_2->amount_before_tax)
        ->toHaveKey('1.billable_item_id', $unpaid_item_1->id) // unpaid_item_1 is period FEB
        ->toHaveKey('1.billable_item_type', get_class($unpaid_item_1))
        ->toHaveKey('1.amount_before_tax', $unpaid_item_1->amount_before_tax)
        ->toHaveKey('2.amount_before_tax', -20.00) // scholarship discount for unpaid_item_2
        ->toHaveKey('3.amount_before_tax', -40.00) // plain discount for unpaid_item_2

        ->toHaveKey('4.amount_before_tax', -20.00) // scholarship discount for unpaid_item_1
        ->toHaveKey('5.amount_before_tax', -40.00) // plain discount for unpaid_item_1

        ->toHaveKey('6.amount_before_tax', -10.00) // advance invoice
    ;
});

test('validateUserCreateBillingDocument payment failed because user cannot access the unpaid items', function () {
    $unrelated_guardian = Guardian::factory()->create();
    $unrelated_guardian_user = $unrelated_guardian->user;

    $guardian1 = Guardian::factory()->create();
    $guardian1_user = $guardian1->user;

    $guardian2 = Guardian::factory()->create();
    $guardian2_user = $guardian2->user;

    $student = Student::factory()->create();
    $student_user = $student->user;

    // $student is under $guardian1
    GuardianStudent::factory()->create([
        'guardian_id' => $guardian1->id,
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
    ]);

    // $student is under $guardian2
    GuardianStudent::factory()->create([
        'guardian_id' => $guardian2->id,
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
    ]);


    // unpaidItem for $student
    $unpaid_item_JAN = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-01-01',
    ]);

    // FAIL because $unrelated_guradian cannot access the unpaid_item
    $payload = collect([
        $unpaid_item_JAN,
    ]);

    expect(function () use ($payload, $unrelated_guardian_user) {
        $this->accountingService
            ->setUser($unrelated_guardian_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(403);
    }, __('system_error.403'));

    // PASS for $guardian1
    $payload = collect([
        $unpaid_item_JAN,
    ]);

    expect(function () use ($payload, $guardian1_user) {
        $this->accountingService
            ->setUser($guardian1_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->not->toThrow(Exception::class);

    // PASS for $guardian2
    $payload = collect([
        $unpaid_item_JAN,
    ]);

    expect(function () use ($payload, $guardian2_user) {
        $this->accountingService
            ->setUser($guardian2_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->not->toThrow(Exception::class);

    // PASS for $student
    $payload = collect([
        $unpaid_item_JAN,
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->not->toThrow(Exception::class);
});

test('FE : validateUserCreateBillingDocument payment failed because exist previous unpaid items', function () {
    $student = Student::factory()->create();
    $student_user = $student->user;

    $unpaid_item_JAN = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_PAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-01-01',
    ]);

    $unpaid_item_APRIL = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-04-01',
    ]);

    $unpaid_item_FEB = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-02-01',
    ]);

    $unpaid_item_MARCH = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-03-01',
    ]);

    $unpaid_item_others_JAN = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'amount_before_tax' => 100,
        'period' => '2024-01-01',
    ]);
    $unpaid_item_others_MARCH = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'amount_before_tax' => 100,
        'period' => '2024-03-01',
    ]);
    $unpaid_item_others_FEB = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'amount_before_tax' => 100,
        'period' => '2024-02-01',
    ]);

    // FAIL - skip a month of unpaid fee
    $payload = collect([
        $unpaid_item_MARCH,
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36001);
    }, __('system_error.36001'));


    // FAIL - fee already paid
    $payload = collect([
        $unpaid_item_JAN,
    ]);


    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36005);
    }, __('system_error.36005'));

    // PASS
    $payload = collect([
        $unpaid_item_FEB,
        $unpaid_item_MARCH,
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->not->toThrow(Exception::class);

    // PASS
    $payload = collect([
        $unpaid_item_others_JAN,
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->not->toThrow(Exception::class);

    // PASS
    $payload = collect([
        $unpaid_item_others_JAN,
        $unpaid_item_others_FEB,
        $unpaid_item_FEB,
        $unpaid_item_MARCH,
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->not->toThrow(Exception::class);
});

test('FE : validateUserCreateBillingDocument payment failed because dont pay all unpaid_items together in same period - multiple fees per period scenario', function () {
    $student = Student::factory()->create();
    $student_user = $student->user;

    $unpaid_item_JAN_SCHOOL1 = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-01-01',
    ]);

    $unpaid_item_JAN_SCHOOL2 = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 30,
        'period' => '2024-01-01',
    ]);

    $unpaid_item_JAN_HOSTEL1 = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-01-01',
    ]);

    $unpaid_item_JAN_HOSTEL2 = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
        'amount_before_tax' => 30,
        'period' => '2024-01-01',
    ]);

    $unpaid_item_JAN_other = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'amount_before_tax' => 100,
        'period' => '2024-01-01',
    ]);

    $unpaid_item_FEB_other = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_OTHERS,
        'amount_before_tax' => 100,
        'period' => '2024-02-01',
    ]);

    $payload = collect([
        $unpaid_item_JAN_HOSTEL1,
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36002);
    }, __('system_error.36002'));

    $payload = collect([
        $unpaid_item_JAN_HOSTEL1, // only pay hostel
        $unpaid_item_JAN_HOSTEL2, // only pay hostel
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36002);
    }, __('system_error.36002'));

    $payload = collect([
        $unpaid_item_JAN_SCHOOL1, // only pay school
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36002);
    }, __('system_error.36002'));

    $payload = collect([
        $unpaid_item_JAN_SCHOOL1, // only pay school
        $unpaid_item_JAN_SCHOOL2, // only pay school
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36002);
    }, __('system_error.36002'));

    $payload = collect([
        $unpaid_item_JAN_SCHOOL1, // only pay school
        $unpaid_item_JAN_SCHOOL2, // only pay school
        $unpaid_item_JAN_HOSTEL1, // only pay hostel1
        // left 2 more
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36002);
    }, __('system_error.36002'));

    $payload = collect([
        $unpaid_item_JAN_HOSTEL1, // only pay hostel
        $unpaid_item_JAN_HOSTEL2, // only pay hostel
        $unpaid_item_JAN_SCHOOL1, // only pay school1
        // left 2 more
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36002);
    }, __('system_error.36002'));


    // FAIL
    $payload = collect([
        $unpaid_item_JAN_HOSTEL1,
        $unpaid_item_JAN_HOSTEL2,
        $unpaid_item_JAN_SCHOOL1,
        $unpaid_item_JAN_SCHOOL2,
        // other is left out
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36002);
    }, __('system_error.36002'));


    // PASS
    $payload = collect([
        $unpaid_item_JAN_HOSTEL1,
        $unpaid_item_JAN_HOSTEL2,
        $unpaid_item_JAN_SCHOOL1,
        $unpaid_item_JAN_SCHOOL2,
        $unpaid_item_JAN_other,
        $unpaid_item_FEB_other,
    ]);

    expect(function () use ($payload, $student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateUserCreateBillingDocument();
    })->not->toThrow(Exception::class);
});

test('FE : validateUserCreateBillingDocument failed because user already have an existing unpaid item that is PENDING', function () {
    $student = Student::factory()->create();
    $student_user = $student->user;

    // unpaidItem for $student PENDING, in progress to be paid
    $pending_unpaid_item = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_PENDING,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-01-01',
    ]);

    $unpaid_item_JAN = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-01-01',
    ]);

    // FAIL because user already have an existing unpaid item that is PENDING
    $payload = collect([
        $unpaid_item_JAN,
    ]);

    $unpaid_items = (new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray());

    expect(function () use ($student_user, $unpaid_items) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems($unpaid_items)
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36009);
    }, __('system_error.36009'));


    // delete the pending unpaid item
    $pending_unpaid_item->delete();

    $payload = collect([
        $unpaid_item_JAN,
    ]);

    $unpaid_items = (new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray());

    // PASS
    expect(function () use ($student_user, $unpaid_items) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems($unpaid_items)
            ->validateUserCreateBillingDocument();
    })->not->toThrow(Exception::class);
});

test('FE : validateUserCreateBillingDocument failed because the amount_after_tax is between RM0.01 and RM1', function () {
    $student = Student::factory()->create();
    $student_user = $student->user;

    $unpaid_item_JAN = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 0.01,
        'period' => '2024-01-01',
    ]);

    // FAIL because the amount_after_tax is 0.01
    $payload = collect([
        $unpaid_item_JAN,
    ]);

    $unpaid_items = (new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray());

    expect(function () use ($student_user, $unpaid_items) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems($unpaid_items)
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36010);
    }, __('system_error.36010'));


    // amount after tax is 0.99
    $unpaid_item_JAN->update([
        'amount_before_tax' => 0.99,
    ]);

    // FAIL because the amount_after_tax is 0.99
    $payload = collect([
        $unpaid_item_JAN,
    ]);

    $unpaid_items = (new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray());

    expect(function () use ($student_user, $unpaid_items) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems($unpaid_items)
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36010);
    }, __('system_error.36010'));


    // amount after tax is 1.01 , expect to pass
    $unpaid_item_JAN->update([
        'amount_before_tax' => 1.01,
    ]);

    // PASS
    $payload = collect([
        $unpaid_item_JAN,
    ]);

    $unpaid_items = (new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray());

    expect(function () use ($student_user, $unpaid_items) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems($unpaid_items)
            ->validateUserCreateBillingDocument();
    })->not->toThrow(Exception::class);


    // amount after tax is 0.00 , expect to pass
    $unpaid_item_JAN->update([
        'amount_before_tax' => 0.00,
    ]);

    // PASS
    $payload = collect([
        $unpaid_item_JAN,
    ]);

    $unpaid_items = (new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray());

    expect(function () use ($student_user, $unpaid_items) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems($unpaid_items)
            ->validateUserCreateBillingDocument();
    })->not->toThrow(Exception::class);


    /**
     * multiple unpaid items with less than RM1
     */

    $unpaid_item_FEB = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 0.01,
        'period' => '2024-02-01',
    ]);

    $unpaid_item_MARCH = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 0.02,
        'period' => '2024-03-01',
    ]);

    // FAIL because the amount_after_tax is 0.00 + 0.02 + 0.01 = 0.03

    $payload = collect([
        $unpaid_item_JAN,
        $unpaid_item_FEB,
        $unpaid_item_MARCH,
    ]);

    $unpaid_items = (new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray());

    expect(function () use ($student_user, $unpaid_items) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems($unpaid_items)
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36010);
    }, __('system_error.36010'));
});

test('FE : validateUserCreateBillingDocument failed because the amount_after_tax is between RM0.01 and RM1, with discount + advances', function () {
    $student = Student::factory()->create();
    $student_user = $student->user;

    /**
     *
     * Unpaid Item with Discount -> amount_after_tax < 1
     *
     */

    $unpaid_item_JAN = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 1,
        'period' => '2024-01-01',
    ]);

    $unpaid_item_FEB = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 1,
        'period' => '2024-02-01',
    ]);

    $unpaid_item_MARCH = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 1,
        'period' => '2024-03-01',
    ]);

    $discount_RM07 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 0.7,
    ]);

    // FAIL because the amount_after_tax is RM3 - RM0.7 - RM0.7 - RM0.7 = RM0.9
    $payload = collect([
        // all fee have discount = RM0.7
        $unpaid_item_JAN,
        $unpaid_item_FEB,
        $unpaid_item_MARCH,
    ]);

    $unpaid_items = (new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray());

    expect(function () use ($student_user, $unpaid_items) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems($unpaid_items)
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36010);
    }, __('system_error.36010'));


    /**
     *
     * Unpaid Item with Advances -> amount_after_tax < 1
     *
     */

    // delete the discount
    $discount_RM07->delete();

    // ADVANCE
    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 2.3, // only 2.3
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_address' => $student->getBillToAddress(),
    ]);

    $billing_document_document_adavance_transaction = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    // FAIL because the amount_after_tax is RM3 - RM2.1 = RM0.9

    $payload = collect([
        $unpaid_item_JAN,
        $unpaid_item_FEB,
        $unpaid_item_MARCH,
    ]);

    $unpaid_items = (new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray());

    expect(function () use ($student_user, $unpaid_items) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems($unpaid_items)
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36010);
    }, __('system_error.36010'));


    $advance_invoice_1->delete();
    $billing_document_document_adavance_transaction->delete();

    /**
     *
     * Unpaid Item with Advance + discount -> amount_after_tax < 1
     *
     */


    $unpaid_item_JAN->update([
        'amount_before_tax' => 1.01,
    ]);
    $unpaid_item_FEB->update([
        'amount_before_tax' => 2.01,
    ]);
    $unpaid_item_MARCH->update([
        'amount_before_tax' => 3.01,
    ]);

    $discount_RM1 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 1,
    ]);

    $advance_invoice_2 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 2.04, // only 2.04
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_address' => $student->getBillToAddress(),
    ]);

    $billing_document_document_adavance_transaction = BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_2->id,
        'amount_before_tax' => $advance_invoice_2->amount_before_tax,
        'billable_type' => $advance_invoice_2->billTo->getBillToType(),
        'billable_id' => $advance_invoice_2->billTo->getBillToId(),
        'currency_code' => $advance_invoice_2->currency_code,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    // FAIL because the amount_after_tax is RM6.03 - RM1 - RM1 - RM1 - RM2.04  = RM0.99

    $payload = collect([
        $unpaid_item_JAN,
        $unpaid_item_FEB,
        $unpaid_item_MARCH,
    ]);

    $unpaid_items = (new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray());

    expect(function () use ($student_user, $unpaid_items) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems($unpaid_items)
            ->validateUserCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36010);
    }, __('system_error.36010'));

});

test('ADMIN : validateAdminCreateBillingDocument failed ADMIN validation logic', function () {
    $student = Student::factory()->create();
    $student_user = $student->user;


    $employee = Employee::factory()->create();
    $employee_user = $employee->user;


    $unpaid_item_JAN = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 1,
        'period' => '2024-01-01',
    ]);

    $unpaid_item_FEB = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 1,
        'period' => '2024-02-01',
    ]);

    /**
     *
     * no unpaid items
     *
     */
    expect(function () use ($student_user) {
        $this->accountingService
            ->setUser($student_user)
            ->setUnpaidItems(collect()) // not set
            ->validateAdminCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36004);
    }, __('system_error.36004'));


    /**
     *
     * not an employee
     *
     */

    $payload = collect([
        $unpaid_item_JAN,
    ]);

    expect(function () use ($student_user, $payload) {
        $this->accountingService
            ->setUser($student_user) // not an employee
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateAdminCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(403);
    }, __('system_error.403'));


    /**
     *
     * unpaid items is PAID
     *
     */
    $unpaid_item_JAN->update([
        'status' => UnpaidItem::STATUS_PAID,
    ]);

    $payload = collect([
        $unpaid_item_JAN,
    ]);

    expect(function () use ($employee_user, $payload) {
        $this->accountingService
            ->setUser($employee_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateAdminCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36005);
    }, __('system_error.36005'));


    /**
     * unpaid items not from same bill_to
     */

    $student_1_unpaid_item = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => Student::factory()->create()->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 1,
        'period' => '2024-01-01',
    ]);

    $student_2_unpaid_item = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => Student::factory()->create()->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 1,
        'period' => '2024-01-01',
    ]);

    $payload = collect([
        $student_1_unpaid_item,
        $student_2_unpaid_item,
    ]);

    expect(function () use ($employee_user, $payload) {
        $this->accountingService
            ->setUser($employee_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateAdminCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36003);
    }, __('system_error.36003'));


    /**
     *
     * unpaid items mock billing_document->amount_after_tax is between RM0.01 and RM1
     *
     */

    $student_1_unpaid_item->update([
        'amount_before_tax' => 0.99,
    ]);

    $payload = collect([
        $student_1_unpaid_item,
    ]);

    expect(function () use ($employee_user, $payload) {
        $this->accountingService
            ->setUser($employee_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateAdminCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36010);
    }, __('system_error.36010'));


    $student_1_unpaid_item->update([
        'amount_before_tax' => 0.01,
    ]);

    $payload = collect([
        $student_1_unpaid_item,
    ]);

    expect(function () use ($employee_user, $payload) {
        $this->accountingService
            ->setUser($employee_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateAdminCreateBillingDocument();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36010);
    }, __('system_error.36010'));


    /**
     *
     * PASS
     *
     */

    $unpaid_item_JAN->update([
        'status' => UnpaidItem::STATUS_UNPAID,
    ]);

    // PASS : pay all
    $payload = collect([
        $unpaid_item_JAN,
        $unpaid_item_FEB,
    ]);

    expect(function () use ($employee_user, $payload) {
        $this->accountingService
            ->setUser($employee_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateAdminCreateBillingDocument();
    })->not->toThrow(Exception::class);


    // PASS : only pay 1
    $payload = collect([
        $unpaid_item_JAN,
    ]);

    expect(function () use ($employee_user, $payload) {
        $this->accountingService
            ->setUser($employee_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateAdminCreateBillingDocument();
    })->not->toThrow(Exception::class);

    // PASS : only pay 1
    $payload = collect([
        $student_2_unpaid_item,
    ]);

    expect(function () use ($employee_user, $payload) {
        $this->accountingService
            ->setUser($employee_user)
            ->setUnpaidItems((new UnpaidItemRepository)->getUnpaidItemsByIds($payload->pluck('id')->toArray()))
            ->validateAdminCreateBillingDocument();
    })->not->toThrow(Exception::class);
});

test('validateUnpaidItemPaymentSequence', function () {

    $student = Student::factory()->create();

    $unpaid_item_JAN = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_PAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-01-01',
    ]);

    $unpaid_item_FEB = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-02-01',
    ]);

    $unpaid_item_MARCH = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-03-01',
    ]);

    $unpaid_item_APRIL = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-04-01',
    ]);

    $unpaid_item_MAY = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-05-01',
    ]);

    $unpaid_item_JUNE = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-06-01',
    ]);

    $unpaid_item_JULY = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100,
        'period' => '2024-07-01',
    ]);

    /**
     * [1] , [1,2,3,4,5] === TRUE
     * [1,2] , [1,2,3,4,5] === TRUE
     * [1,2,3] , [1,2,3,4,5] === TRUE
     * [1,2,3,4] , [1,2,3,4,5] === TRUE
     * [1,2,3,4,5] , [1,2,3,4,5] === TRUE
     *
     */
    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_JAN]),
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_APRIL, $unpaid_item_MARCH, $unpaid_item_MAY]),
    );

    expect($result)->toBeTruthy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_JAN, $unpaid_item_FEB]),
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_APRIL, $unpaid_item_MAY, $unpaid_item_MARCH]),
    );

    expect($result)->toBeTruthy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_MARCH]),
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_APRIL, $unpaid_item_MAY, $unpaid_item_MARCH]),
    );

    expect($result)->toBeTruthy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL]),
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_APRIL, $unpaid_item_MAY, $unpaid_item_MARCH]),
    );

    expect($result)->toBeTruthy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY]),
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_APRIL, $unpaid_item_MAY, $unpaid_item_MARCH]),
    );

    expect($result)->toBeTruthy();

    /**
     * [2] , [1,2,3,4,5] === FALSE
     * [2,3] , [1,2,3,4,5] === FALSE
     * [2,3,4] , [1,2,3,4,5] === FALSE
     * [2,3,4,5] , [1,2,3,4,5] === FALSE
     * [2,3,4,5,6] , [1,2,3,4,5] === FALSE
     * [2,3,4,5,6,7] , [1,2,3,4,5] === FALSE
     *
     */
    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_FEB]),
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY]),
    );

    expect($result)->toBeFalsy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_FEB, $unpaid_item_MARCH]),
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY]),
    );

    expect($result)->toBeFalsy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL]),
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY]),
    );

    expect($result)->toBeFalsy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY]),
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY]),
    );

    expect($result)->toBeFalsy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_JUNE, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY]),
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY]),
    );

    expect($result)->toBeFalsy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_JUNE, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY, $unpaid_item_JULY]),
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY]),
    );

    expect($result)->toBeFalsy();


    /**
     * NON SEQUENTIAL
     * [5] , [5,10,15] === TRUE
     * [5,10] , [5,10,15] === TRUE
     * [5,10,15] , [5,10,15] === TRUE
     *
     */

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_MARCH]),
        collect([$unpaid_item_MARCH, $unpaid_item_MAY, $unpaid_item_JULY]),
    );

    expect($result)->toBeTruthy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_MARCH, $unpaid_item_MAY]),
        collect([$unpaid_item_MARCH, $unpaid_item_MAY, $unpaid_item_JULY]),
    );

    expect($result)->toBeTruthy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_MARCH, $unpaid_item_MAY, $unpaid_item_JULY]),
        collect([$unpaid_item_MARCH, $unpaid_item_MAY, $unpaid_item_JULY]),
    );

    expect($result)->toBeTruthy();


    /**
     * NON SEQUENTIAL
     * [10] , [5,10,15] === FALSE
     * [10,15] , [5,10,15] === FALSE
     *
     */

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_MAY]),
        collect([$unpaid_item_MARCH, $unpaid_item_MAY, $unpaid_item_JULY]),
    );

    expect($result)->toBeFalsy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_MAY, $unpaid_item_JULY]),
        collect([$unpaid_item_MARCH, $unpaid_item_MAY, $unpaid_item_JULY]),
    );

    expect($result)->toBeFalsy();

    /**
     * [1,2,4,5] , [1,2,3,4,5] === FALSE  --------   SKIP 1 index
     * [1,5] , [1,2,3,4,5] === FALSE  ------   SKIP 3 index
     *
     */

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_APRIL, $unpaid_item_MAY]), // skip 3
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY]),
    );

    expect($result)->toBeFalsy();

    $result = $this->accountingService->validateUnpaidItemPaymentSequence(
        collect([$unpaid_item_JAN, $unpaid_item_MAY]), // skip 2,3,4
        collect([$unpaid_item_JAN, $unpaid_item_FEB, $unpaid_item_MARCH, $unpaid_item_APRIL, $unpaid_item_MAY]),
    );

    expect($result)->toBeFalsy();
});

test('processManualPayment() payment success, only cash', function () {
    Carbon::setTestNow('2025-01-20 00:00:00');
    $student = Student::factory()->create();

    $invoice1 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_reference_number' => $student->getBillToReferenceNumber(),
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => null,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 100,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'description' => 'School Fees Jan 2024',
    ]);

    expect($invoice1->payments->count())->toBe(0);

    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('payments', 0);
    $this->assertDatabaseCount('payment_requests', 0);

    $payment_method = PaymentMethod::where('code', PaymentMethod::CODE_CASH)->first();

    $payload = [
        'remarks' => 'Manual Payment',
        'payments' => [
            [
                'payment_method_code' => $payment_method->code,
                'amount_received' => 40.5,
            ],
            [
                'payment_method_code' => $payment_method->code,
                'amount_received' => 59.5,
            ],
        ],
    ];

    Event::fake();

    $response = $this->accountingService
        ->setUser($this->admin->user)
        ->setBillingDocument($invoice1)
        ->processManualPayment($payload);

    expect($response)->toBeTruthy();

    Event::assertDispatched(\App\Events\InvoicePaidEvent::class);
    Event::assertDispatched(\App\Events\PaymentCompletedEvent::class);

    $invoice1->refresh();

    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('payments', 2);
    $this->assertDatabaseCount('payment_requests', 2);

    $this->assertDatabaseHas('billing_documents', [
        'id' => $invoice1->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'paid_at' => now()->toDateTimeString(),
    ]);

    expect($invoice1->payments->count())->toBe(2);
    expect($invoice1->paymentRequests->count())->toBe(2);

    $this->assertDatabaseHas('payment_requests', [
        'userable_type' => $student->getBillToType(),
        'userable_id' => $student->getBillToId(),
        'billing_document_id' => $invoice1->id,
        'payment_method_id' => $payment_method->id,
        'status' => PaymentRequest::STATUS_APPROVED,
        'amount' => 40.5,
        'bank_id' => null,
        'payment_reference_no' => $invoice1->reference_no,
        'proof_of_payment_url' => null,
        'approved_at' => now()->toDateTimeString(),
    ]);

    $this->assertDatabaseHas('payment_requests', [
        'userable_type' => $student->getBillToType(),
        'userable_id' => $student->getBillToId(),
        'billing_document_id' => $invoice1->id,
        'payment_method_id' => $payment_method->id,
        'status' => PaymentRequest::STATUS_APPROVED,
        'amount' => 59.5,
        'bank_id' => null,
        'payment_reference_no' => $invoice1->reference_no,
        'proof_of_payment_url' => null,
        'approved_at' => now()->toDateTimeString(),
    ]);

    $this->assertDatabaseHas('payments', [
        'billing_document_id' => $invoice1->id,
        'payment_method_id' => $payment_method->id,
        'payment_reference_no' => $invoice1->reference_no,
        'payment_reference_no_2' => null,
        'amount_received' => 40.5,
        'paid_at' => now()->toDateTimeString(),
        'bank_id' => null,
        'remarks' => $payload['remarks'],
        'created_by_employee_id' => $this->admin->id,
    ]);

    $this->assertDatabaseHas('payments', [
        'billing_document_id' => $invoice1->id,
        'payment_method_id' => $payment_method->id,
        'payment_reference_no' => $invoice1->reference_no,
        'payment_reference_no_2' => null,
        'amount_received' => 59.5,
        'paid_at' => now()->toDateTimeString(),
        'bank_id' => null,
        'remarks' => $payload['remarks'],
        'payment_source_type' => PaymentRequest::class,
        'created_by_employee_id' => $this->admin->id,
    ]);
});

test('processManualPayment() payment success, multiple payment method with file attached', function () {
    Carbon::setTestNow('2025-01-20 00:00:00');
    $student = Student::factory()->create();

    $invoice1 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_reference_number' => $student->getBillToReferenceNumber(),
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => null,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 100,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'description' => 'School Fees Jan 2024',
    ]);

    expect($invoice1->payments->count())->toBe(0);

    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('payments', 0);
    $this->assertDatabaseCount('payment_requests', 0);
    $this->assertDatabaseCount('media', 0);

    $cash_payment_method = PaymentMethod::where('code', PaymentMethod::CODE_CASH)->first();

    $bank_transfer_payment_method = PaymentMethod::where('code', PaymentMethod::CODE_BANK_TRANSFER)->first();
    $maybank = Bank::where('swift_code', 'MBBEMYKLXXX')->first();
    $bank_transfer_receipt = UploadedFile::fake()->create('bank_transfer_receipt.png', 500);

    $cheque_payment_method = PaymentMethod::factory()->create([
        'code' => PaymentMethod::CODE_CHEQUE,
        'name' => 'Cheque',
        'is_active' => true,
    ]);
    $cheque_payment_receipt = UploadedFile::fake()->create('cheque_payment_receipt.png', 500);


    $payload = [
        'remarks' => 'Manual Payment',
        'payments' => [
            [
                'payment_method_code' => $cash_payment_method->code, // Cash payment
                'amount_received' => 20.00,
            ],
            [
                'payment_method_code' => $bank_transfer_payment_method->code, // Bank transfer payment
                'bank_id' => $maybank->id,
                'payment_reference_no' => 'BANK123456',
                'amount_received' => 40.50,
                'file' => $bank_transfer_receipt,
            ],
            [
                'payment_method_code' => $cheque_payment_method->code, // CHEQUE payment
                'payment_reference_no' => 'FPX987654',
                'amount_received' => 39.50,
                'file' => $cheque_payment_receipt,
            ],

        ],
    ];

    Event::fake();

    $response = $this->accountingService
        ->setUser($this->admin->user)
        ->setBillingDocument($invoice1)
        ->processManualPayment($payload);

    expect($response)->toBeTruthy();

    Event::assertDispatched(\App\Events\InvoicePaidEvent::class);
    Event::assertDispatched(\App\Events\PaymentCompletedEvent::class);

    $invoice1->refresh();

    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('payments', 3);
    $this->assertDatabaseCount('payment_requests', 3);
    $this->assertDatabaseCount('media', 2);

    $this->assertDatabaseHas('billing_documents', [
        'id' => $invoice1->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'paid_at' => now()->toDateTimeString(),
    ]);

    expect($invoice1->payments->count())->toBe(3);
    expect($invoice1->paymentRequests->count())->toBe(3);

    // CASH
    $this->assertDatabaseHas('payment_requests', [
        'userable_type' => $student->getBillToType(),
        'userable_id' => $student->getBillToId(),
        'billing_document_id' => $invoice1->id,
        'payment_method_id' => $cash_payment_method->id,
        'status' => PaymentRequest::STATUS_APPROVED,
        'amount' => 20.00, // CASH
        'bank_id' => null,
        'payment_reference_no' => $invoice1->reference_no,
        'proof_of_payment_url' => null,
        'approved_at' => now()->toDateTimeString(),
    ]);

    // BANK TRANSFER
    $created_bank_transfer_payment_request = PaymentRequest::where([
        'payment_method_id' => $bank_transfer_payment_method->id,
        'billing_document_id' => $invoice1->id,
    ])->first();

    $created_bank_transfer_payment_file = Media::where([
        'model_type' => PaymentRequest::class,
        'model_id' => $created_bank_transfer_payment_request->id,
    ])->first();

    $this->assertDatabaseHas('payment_requests', [
        'userable_type' => $student->getBillToType(),
        'userable_id' => $student->getBillToId(),
        'billing_document_id' => $invoice1->id,
        'payment_method_id' => $bank_transfer_payment_method->id,
        'status' => PaymentRequest::STATUS_APPROVED,
        'amount' => 40.50, // BANK TRANSFER
        'bank_id' => $maybank->id,
        'payment_reference_no' => $payload['payments'][1]['payment_reference_no'],
        'proof_of_payment_url' => getMediaFullUrl($created_bank_transfer_payment_file),
        'approved_at' => now()->toDateTimeString(),
    ]);


    // CHEQUE PAYMENT
    $created_cheque_payment_request = PaymentRequest::where([
        'payment_method_id' => $cheque_payment_method->id,
        'billing_document_id' => $invoice1->id,
    ])->first();

    $created_cheque_payment_file = Media::where([
        'model_type' => PaymentRequest::class,
        'model_id' => $created_cheque_payment_request->id,
    ])->first();

    $this->assertDatabaseHas('payment_requests', [
        'userable_type' => $student->getBillToType(),
        'userable_id' => $student->getBillToId(),
        'billing_document_id' => $invoice1->id,
        'payment_method_id' => $cheque_payment_method->id,
        'status' => PaymentRequest::STATUS_APPROVED,
        'amount' => 39.50, // CHEQUE PAYMENT
        'bank_id' => null,
        'payment_reference_no' => $payload['payments'][2]['payment_reference_no'],
        'proof_of_payment_url' => getMediaFullUrl($created_cheque_payment_file),
        'approved_at' => now()->toDateTimeString(),
    ]);


    // PAYMENTS
    $this->assertDatabaseHas('payments', [
        'billing_document_id' => $invoice1->id,
        'payment_method_id' => $cash_payment_method->id,
        'payment_reference_no' => $invoice1->reference_no,
        'payment_reference_no_2' => null,
        'amount_received' => 20.00,
        'paid_at' => now()->toDateTimeString(),
        'bank_id' => null,
        'remarks' => $payload['remarks'],
        'created_by_employee_id' => $this->admin->id,
    ]);

    $this->assertDatabaseHas('payments', [
        'billing_document_id' => $invoice1->id,
        'payment_method_id' => $bank_transfer_payment_method->id,
        'payment_reference_no' => $payload['payments'][1]['payment_reference_no'],
        'payment_reference_no_2' => null,
        'amount_received' => 40.50, // BANK TRANSFER
        'paid_at' => now()->toDateTimeString(),
        'bank_id' => $maybank->id,
        'remarks' => $payload['remarks'],
        'created_by_employee_id' => $this->admin->id,
    ]);

    $this->assertDatabaseHas('payments', [
        'billing_document_id' => $invoice1->id,
        'payment_method_id' => $cheque_payment_method->id,
        'payment_reference_no' => $payload['payments'][2]['payment_reference_no'],
        'payment_reference_no_2' => null,
        'amount_received' => 39.50, // CHEQUE PAYMENT
        'paid_at' => now()->toDateTimeString(),
        'bank_id' => null,
        'remarks' => $payload['remarks'],
        'created_by_employee_id' => $this->admin->id,
    ]);
});

test('validateManualPayment() payment failed because validation', function () {

    /**
     * not setting billing document
     */
    expect(function () {
        $this->accountingService
            ->setUser($this->admin->user)
            ->validateManualPayment([]);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(0);
    }, 'Billing document is not set');


    /**
     * invalid status for billing document to do manual payment
     */

    expect(function () {
        $billing_document = BillingDocument::factory()->create([
            'type' => BillingDocument::TYPE_INVOICE,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'amount_before_tax' => 100,
        ]);

        $this->accountingService
            ->setUser($this->admin->user)
            ->setBillingDocument($billing_document)
            ->validateManualPayment([]);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(37001);
    }, __('system_error.37001'));

    /**
     * incorrect total amount
     */
    $guardian1 = Guardian::factory()->create();

    $student = Student::factory()->create();

    $invoice1 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_reference_number' => $student->getBillToReferenceNumber(),
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => null,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1,
        'amount_before_tax' => 100,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'description' => 'School Fees Jan 2024',
    ]);

    expect($invoice1->payments->count())->toBe(0);

    $this->assertDatabaseCount('billing_documents', 2);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('payments', 0);
    $this->assertDatabaseCount('payment_requests', 0);

    $payment_method = PaymentMethod::where('code', PaymentMethod::CODE_CASH)->first();

    $payload = [
        'billing_document_id' => $invoice1->id,
        'remarks' => 'Manual Payment',
        'paid_by' => $guardian1->id,
        'payments' => [
            [
                'payment_method_code' => $payment_method->code,
                'amount_received' => 100.5, // incorrect total amount
            ],
        ],
    ];

    Event::fake();

    expect(function () use ($payload, $invoice1) {
        $this->accountingService
            ->setUser($this->admin->user)
            ->setBillingDocument($invoice1)
            ->processManualPayment($payload);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36008);
    }, __('system_error.36008'));

    Event::assertNotDispatched(\App\Events\InvoicePaidEvent::class);
    Event::assertNotDispatched(\App\Events\PaymentCompletedEvent::class);

    expect($invoice1->payments->count())->toBe(0);

    $this->assertDatabaseCount('billing_documents', 2);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('payments', 0);
    $this->assertDatabaseCount('payment_requests', 0);

    $this->assertDatabaseHas('billing_documents', [
        'id' => $invoice1->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'paid_at' => null,
    ]);
});
