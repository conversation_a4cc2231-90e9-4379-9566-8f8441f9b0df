<?php

use App\Enums\PaymentStatus;
use App\Models\Bank;
use App\Models\BankAccount;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Employee;
use App\Models\Payment;
use App\Models\PaymentGatewayLog;
use App\Models\PaymentTerm;
use App\Models\Product;
use App\Models\Student;
use \App\Models\LegalEntity;
use App\Models\Tax;
use App\Models\UnpaidItem;
use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function () {

    $this->legalEntity = LegalEntity::factory()->create();
    $this->student = Student::factory()->has(User::factory()->state([]))->create([]);

    $this->system = Employee::factory()->create([
        'employee_number' => 'SYSTEM',
    ]);
    $this->employee = Employee::factory()->create();

    $this->bank = Bank::factory()->create([
        'name' => 'MAYBANK',
    ]);
    $this->bankAccount = BankAccount::factory()->create([
        'bank_id' => $this->bank->id,
        'bankable_id' => $this->legalEntity->id,
        'bankable_type' => LegalEntity::class,
    ]);
    $this->tax = Tax::factory()->create([
        'percentage' => 0,
        'name' => 'Tax Exempt',
    ]);
    $this->paymentTerm = PaymentTerm::factory()->create([
        'due_date_days' => 10,
    ]);
    $this->product = Product::factory()->create([
        'gl_account_code' => 'PRO0000001'
    ]);

});

test('test multiple line items with unpaid item billable to be marked as paid', function() {

    $invoice1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
        'paid_at' => '2024-07-25 04:30:00'
    ]);

    $unpaid_item1 = \App\Models\UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice1->id,
        'amount_before_tax' => 60,
        'paid_at' => null,
    ]);
    $unpaid_item2 = \App\Models\UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice1->id,
        'amount_before_tax' => 40,
        'paid_at' => null,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1->id,
        'amount_before_tax' => 60,
        'currency_code' => 'MYR',
        'billable_item_type' => get_class($unpaid_item1),
        'billable_item_id' => $unpaid_item1->id,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1->id,
        'amount_before_tax' => 40,
        'currency_code' => 'MYR',
        'billable_item_type' => get_class($unpaid_item2),
        'billable_item_id' => $unpaid_item2->id,
    ]);

    Payment::factory()->create([
        'billing_document_id' => $invoice1->id,
        'paid_at' => '2024-07-25 04:29:00'
    ]);

    // trigger event listener
    $event = new \App\Events\InvoicePaidEvent($invoice1);
    $listener = new \App\Listeners\MarkBillableItemAsPaid();

    $listener->handle($event);

    // validate data
    $unpaid_item1->refresh();
    $unpaid_item2->refresh();

    expect($unpaid_item1->status)
        ->toBe(UnpaidItem::STATUS_PAID)
        ->and($unpaid_item1->paid_at)
        ->toBe('2024-07-25 04:29:00')
        ->and($unpaid_item2->status)
        ->toBe(UnpaidItem::STATUS_PAID)
        ->and($unpaid_item2->paid_at)
        ->toBe('2024-07-25 04:29:00');
});



test('test edge case where billing document missing paid_at, should use payment object date anyways', function() {

    \Carbon\Carbon::setTestNow('2024-08-01 14:00:01');

    $invoice1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
        'paid_at' => null
    ]);

    $unpaid_item1 = \App\Models\UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice1->id,
        'amount_before_tax' => 100,
        'paid_at' => null,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice1->id,
        'amount_before_tax' => 100,
        'currency_code' => 'MYR',
        'billable_item_type' => get_class($unpaid_item1),
        'billable_item_id' => $unpaid_item1->id,
    ]);

    Payment::factory()->create([
        'billing_document_id' => $invoice1->id,
        'paid_at' => '2024-07-25 04:28:00'
    ]);

    // trigger event listener
    $event = new \App\Events\InvoicePaidEvent($invoice1);
    $listener = new \App\Listeners\MarkBillableItemAsPaid();

    $listener->handle($event);

    // validate data
    $unpaid_item1->refresh();

    expect($unpaid_item1->status)
        ->toBe(UnpaidItem::STATUS_PAID)
        ->and($unpaid_item1->paid_at)
        ->toBe('2024-07-25 04:28:00');
});


test('test running mark billable item as paid on credit note, should not be processed', function() {

    \Carbon\Carbon::setTestNow('2024-08-01 14:00:01');

    $credit_note = BillingDocument::factory()->create([
        'document_date' => '2024-06-02',
        'type' => BillingDocument::TYPE_CREDIT_NOTE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
        'bill_to_name' => $this->student->getBillToName(),
        'bill_to_address' => $this->student->getBillToAddress(),
        'paid_at' => null
    ]);

    // check logs
    Log::shouldReceive('debug')
        ->once()
        ->with('[MarkBillableItemAsPaid] Billing Document ' . $credit_note->id . ' is not an invoice, ignore.');

    // trigger event listener
    $event = new \App\Events\InvoicePaidEvent($credit_note);
    $listener = new \App\Listeners\MarkBillableItemAsPaid();

    $listener->handle($event);

});
