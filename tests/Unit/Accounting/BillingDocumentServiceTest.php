<?php

use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\PaymentType;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Config;
use App\Models\Currency;
use App\Models\PaymentGatewayLog;
use App\Models\Student;
use App\Models\UnpaidItem;
use App\Services\Billing\BillingDocumentService;
use Database\Seeders\CurrencySeeder;
use Database\Seeders\EmployeeSeeder;
use Database\Seeders\GlAccountSeeder;
use Database\Seeders\LegalEntitySeeder;
use Database\Seeders\PaymentMethodSeeder;
use Database\Seeders\PaymentTermsSeeder;
use Database\Seeders\TaxSeeder;
use Database\Seeders\UomSeeder;
use Illuminate\Support\Facades\Http;

beforeEach(function () {
    $this->seed([
        CurrencySeeder::class,
        TaxSeeder::class,
        UomSeeder::class,
        GlAccountSeeder::class,
        PaymentMethodSeeder::class,
        PaymentTermsSeeder::class,
        LegalEntitySeeder::class,
        EmployeeSeeder::class,
    ]);

    $this->student = Student::factory()->create();

    $this->currency = Currency::where('code', config('school.currency_code'))->first();
});

test('requestPaymentLink - generate from Payex - success', function () {
    $user = $this->student->user;

    $billing_document = BillingDocument::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $this->student->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_after_tax' => 1,
    ]);

    $unpaid_item = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $this->student->id,
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $billing_document->id,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $billing_document->id,
        'billable_item_type' => get_class($unpaid_item),
        'billable_item_id' => $unpaid_item->id,
    ]);

    $payex_base_url = config('services.payment_gateway.payex.base_url');
    $payex_payment_url = $payex_base_url . '/' . config('services.payment_gateway.payex.payment_url');
    $payex_auth_url = $payex_base_url . '/' . config('services.payment_gateway.payex.auth_url');

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>'
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_SECRET,
        'value' => '123456'
    ]);

    Http::fake([
        $payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_payment_url => Http::response(
            [
                'request_id' => 'd9e48d61-df21-422c-82f3-19eaf66ee5f8',
                'status' => '00',
                'result' => [
                    [
                        'status' => '00',
                        'key' => '7e3afcc5957b4555909d085832550147',
                        'url' => 'VALID_PAYMENT_URL',
                        'error' => null
                    ]
                ],
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);

    $this->assertDatabaseCount('payment_gateway_logs', 0);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('unpaid_items', 1);


    /** @var BillingDocumentService $service */
    $service = app()->make(BillingDocumentService::class);

    $response = $service
        ->setUser($user)
        ->setBillingDocument($billing_document)
        ->requestPaymentLink(); // with no return url

    expect($response)->toBeInstanceOf(PaymentGatewayLog::class)
        ->toMatchArray([
            'requested_by_id' => $user->id,
            'type' => PaymentType::FEE_PAYMENT->value,
            'provider' => PaymentProvider::PAYEX->value,
            'transaction_loggable_type' => BillingDocument::class,
            'transaction_loggable_id' => $billing_document->id,
            'currency_id' => $this->currency->id,
            'currency_code' => $this->currency->code,
            'currency_name' => $this->currency->name,
            'amount' => $billing_document->amount_after_tax,
            'status' => PaymentStatus::PENDING->value,
            'description' => 'Pay Unpaid Item',
            'payment_url' => 'VALID_PAYMENT_URL',
        ]);

    $this->assertDatabaseCount('payment_gateway_logs', 1);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('unpaid_items', 1);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'requested_by_id' => $user->id,
        'type' => PaymentType::FEE_PAYMENT->value,
        'provider' => PaymentProvider::PAYEX->value,
        'transaction_loggable_type' => BillingDocument::class,
        'transaction_loggable_id' => $billing_document->id,
        'currency_id' => $this->currency->id,
        'currency_code' => $this->currency->code,
        'currency_name' => $this->currency->name,
        'amount' => $billing_document->amount_after_tax,
        'status' => PaymentStatus::PENDING,
        'description' => 'Pay Unpaid Item',
        'payment_url' => 'VALID_PAYMENT_URL'
    ]);

    $created_payment_gateway_log = PaymentGatewayLog::where([
        'requested_by_id' => $user->id,
        'type' => PaymentType::FEE_PAYMENT->value,
        'provider' => PaymentProvider::PAYEX->value,
        'transaction_loggable_type' => BillingDocument::class,
        'transaction_loggable_id' => $billing_document->id,
        'currency_id' => $this->currency->id,
    ])->first();

    // expect not to have string 'redirect' in the return url
    expect($created_payment_gateway_log->request_data['return_url'])->not->toContain('redirect');

    // billing document no effect
    $this->assertDatabaseHas('billing_documents', [
        'id' => $billing_document->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    // unpaid item no effect
    $this->assertDatabaseHas('unpaid_items', [
        'id' => $unpaid_item->id,
        'status' => UnpaidItem::STATUS_PENDING,
    ]);
});

test('requestPaymentLink - generate from Payex - success with return url', function () {
    $user = $this->student->user;

    $billing_document = BillingDocument::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $this->student->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_after_tax' => 1,
    ]);

    $unpaid_item = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $this->student->id,
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $billing_document->id,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $billing_document->id,
        'billable_item_type' => get_class($unpaid_item),
        'billable_item_id' => $unpaid_item->id,
    ]);

    $payex_return_url = url(config('services.payment_gateway.payex.return_url'));
    $payex_base_url = config('services.payment_gateway.payex.base_url');
    $payex_payment_url = $payex_base_url . '/' . config('services.payment_gateway.payex.payment_url');
    $payex_auth_url = $payex_base_url . '/' . config('services.payment_gateway.payex.auth_url');

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>'
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_SECRET,
        'value' => '123456'
    ]);

    Http::fake([
        $payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_payment_url => Http::response(
            [
                'request_id' => 'd9e48d61-df21-422c-82f3-19eaf66ee5f8',
                'status' => '00',
                'result' => [
                    [
                        'status' => '00',
                        'key' => '7e3afcc5957b4555909d085832550147',
                        'url' => 'VALID_PAYMENT_URL',
                        'error' => null
                    ]
                ],
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);

    $this->assertDatabaseCount('payment_gateway_logs', 0);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('unpaid_items', 1);


    /** @var BillingDocumentService $service */
    $service = app()->make(BillingDocumentService::class);

    $redirect_url = 'https://google.com';

    $payload = [
        'return_url' => $redirect_url,
    ];

    $response = $service
        ->setUser($user)
        ->setBillingDocument($billing_document)
        ->requestPaymentLink($payload);

    expect($response)->toBeInstanceOf(PaymentGatewayLog::class)
        ->and($response['request_data']['return_url'])->toBe($payex_return_url . '?' . http_build_query(['redirect_url' => $redirect_url]));

    $this->assertDatabaseCount('payment_gateway_logs', 1);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('unpaid_items', 1);
});

test('requestPaymentLink - generate from Payex - failed because of payment gateway', function () {
    $user = $this->student->user;

    $billing_document = BillingDocument::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $this->student->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_after_tax' => 1,
    ]);

    $unpaid_item = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $this->student->id,
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $billing_document->id,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $billing_document->id,
        'billable_item_type' => get_class($unpaid_item),
        'billable_item_id' => $unpaid_item->id,
    ]);


    $payex_base_url = config('services.payment_gateway.payex.base_url');
    $payex_payment_url = $payex_base_url . '/' . config('services.payment_gateway.payex.payment_url');
    $payex_auth_url = $payex_base_url . '/' . config('services.payment_gateway.payex.auth_url');

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>'
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_SECRET,
        'value' => '123456'
    ]);

    Http::fake([
        $payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_payment_url => Http::response(
            [
                'request_id' => 'd9e48d61-df21-422c-82f3-19eaf66ee5f8',
                'status' => '01',
                'result' => [
                    [
                        'status' => '01',
                        'key' => '7e3afcc5957b4555909d085832550147',
                        'url' => 'INVALID_PAYMENT_URL',
                        'error' => 'INVALID PAYMENT'
                    ]
                ],
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);

    $this->assertDatabaseCount('payment_gateway_logs', 0);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);
    $this->assertDatabaseCount('unpaid_items', 1);


    /** @var BillingDocumentService $service */
    $service = app()->make(BillingDocumentService::class);

    expect(function () use ($service, $user, $billing_document) {
        $service
            ->setUser($user)
            ->setBillingDocument($billing_document)
            ->requestPaymentLink();
    })->toThrow(function (Exception $e) {
        expect($e->getMessage())->toBe('INVALID PAYMENT');
    });

    $this->assertDatabaseCount('payment_gateway_logs', 1);

    $payment_gateway_log = PaymentGatewayLog::first();

    expect($payment_gateway_log)->toBeInstanceOf(PaymentGatewayLog::class)
        ->toMatchArray([
            'requested_by_id' => $user->id,
            'type' => PaymentType::FEE_PAYMENT->value,
            'provider' => PaymentProvider::PAYEX->value,
            'transaction_loggable_type' => BillingDocument::class,
            'currency_id' => $this->currency->id,
            'currency_code' => $this->currency->code,
            'currency_name' => $this->currency->name,
            'amount' => $billing_document->amount_after_tax,
            'status' => PaymentStatus::FAILED->value,
            'description' => 'Pay Unpaid Item',
            'payment_url' => null,
        ]);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'requested_by_id' => $user->id,
        'type' => PaymentType::FEE_PAYMENT->value,
        'provider' => PaymentProvider::PAYEX->value,
        'transaction_loggable_type' => BillingDocument::class,
        'currency_id' => $this->currency->id,
        'currency_code' => $this->currency->code,
        'currency_name' => $this->currency->name,
        'amount' => $billing_document->amount_after_tax,
        'status' => PaymentStatus::FAILED->value,
        'description' => 'Pay Unpaid Item',
        'payment_url' => null,
    ]);


    // billing document no effect
    $this->assertDatabaseHas('billing_documents', [
        'id' => $billing_document->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    // unpaid item no effect
    $this->assertDatabaseHas('unpaid_items', [
        'id' => $unpaid_item->id,
        'status' => UnpaidItem::STATUS_PENDING,
    ]);
});

test('validateRequestPaymentLink', function () {
    /**
     * Try to make payment with invalid status for $billing_document
     *
     */
    $user = $this->student->user;

    $billing_document = BillingDocument::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $this->student->id,
        'status' => BillingDocument::STATUS_DRAFT,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
    ]);

    $unpaid_item = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $this->student->id,
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $billing_document->id,
    ]);


    /** @var BillingDocumentService $service */
    $service = app()->make(BillingDocumentService::class);

    expect(function () use ($service, $user, $billing_document) {
        $service
            ->setUser($user)
            ->setBillingDocument($billing_document)
            ->validateRequestPaymentLink();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(37001);
    }, __('system_error.37001'));


    /**
     * PASS
     */

    $billing_document->update([
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    /** @var BillingDocumentService $service */
    $service = app()->make(BillingDocumentService::class);

    expect(function () use ($service, $user, $billing_document) {
        $service
            ->setUser($user)
            ->setBillingDocument($billing_document)
            ->validateRequestPaymentLink();
    })->not->toThrow(Exception::class);


    /**
     *
     * Try to make payment for billing document with amount_after_tax between 0.01 and 0.99
     */

    // amount_after_tax = 0.99
    $billing_document->update([
        'amount_after_tax' => 0.99,
    ]);

    expect(function () use ($user, $billing_document) {
        app()->make(BillingDocumentService::class)
            ->setUser($user)
            ->setBillingDocument($billing_document)
            ->validateRequestPaymentLink();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36010);
    }, __('system_error.36010'));


    // amount_after_tax = 0.01
    $billing_document->update([
        'amount_after_tax' => 0.01,
    ]);

    expect(function () use ($user, $billing_document) {
        app()->make(BillingDocumentService::class)
            ->setUser($user)
            ->setBillingDocument($billing_document)
            ->validateRequestPaymentLink();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36010);
    }, __('system_error.36010'));


    // amount after tax = 0
    $billing_document->update([
        'amount_after_tax' => 0,
    ]);

    expect(function () use ($user, $billing_document) {
        app()->make(BillingDocumentService::class)
            ->setUser($user)
            ->setBillingDocument($billing_document)
            ->validateRequestPaymentLink();
    })->not->toThrow(Exception::class);


    // amount after tax = 1
    $billing_document->update([
        'amount_after_tax' => 1,
    ]);

    expect(function () use ($user, $billing_document) {
        app()->make(BillingDocumentService::class)
            ->setUser($user)
            ->setBillingDocument($billing_document)
            ->validateRequestPaymentLink();
    })->not->toThrow(Exception::class);


    /**
     * Try to make payment but already exist payment gateway log for the billing document with status PENDING
     */

    $payment_gateway_log = PaymentGatewayLog::factory()->create([
        'requested_by_id' => $user->id,
        'type' => PaymentType::FEE_PAYMENT->value,
        'provider' => PaymentProvider::PAYEX->value,
        'billing_document_id' => $billing_document->id,
        'status' => PaymentStatus::PENDING->value,
    ]);

    expect(function () use ($user, $billing_document) {
        app()->make(BillingDocumentService::class)
            ->setUser($user)
            ->setBillingDocument($billing_document)
            ->validateRequestPaymentLink();

    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36011);
    }, __('system_error.36011'));


    // PASS because payment gateway log status is updated to FAILED
    $payment_gateway_log->update([
        'status' => PaymentStatus::FAILED->value,
    ]);

    expect(function () use ($user, $billing_document) {
        app()->make(BillingDocumentService::class)
            ->setUser($user)
            ->setBillingDocument($billing_document)
            ->validateRequestPaymentLink();
    })->not->toThrow(Exception::class);
});
