<?php

use App\Models\Exam;
use App\Repositories\ExamRepository;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->examRepository = app(ExamRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Exam::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->examRepository->getModelClass();

    expect($response)->toEqual(Exam::class);
});

test('getAll()', function (int $expected_count, array $filters, array $expected_model) {
    $test_date = Carbon::parse('2024-05-01')->format('Y-m-d');
    Carbon::setTestNow($test_date);

    $keys = [
        'first' => Exam::factory()->create([
            'code' => 'A001',
            'name->en' => 'English Exam 1',
            'name->zh' => '英语考试1',
            'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
            'description' => 'This is the last exam'
        ]),
        'second' => Exam::factory()->create([
            'code' => 'A002',
            'name->en' => 'English Exam 2',
            'name->zh' => '英语考试2',
            'start_date' => Carbon::parse('2024-10-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-11-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-10-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-11-15 15:59:59'),
            'description' => 'This is the last last exam'
        ]),
        'third' => Exam::factory()->create([
            'code' => 'B003',
            'name->en' => 'Math Exam 1',
            'name->zh' => '数学考试 1',
            'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
            'description' => 'For real this time'
        ]),
    ];

    $result = $this->examRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result[$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'get all data' => [3, [], ['first', 'second', 'third']],
    'filter by name = English Exam 1' => [1, ['name' => 'English Exam 1'], ['first']],
    'filter by name = Math Exam 1' => [1, ['name' => 'Math Exam 1'],  ['third']],
    'filter by name = English Exam' => [2, ['name' => 'English Exam'], ['first', 'second']],
    'filter by name = Non Existing' => [0, ['name' => 'Bla Bla'], []],
    'filter by code = A001' => [1, ['code' => 'A001'], ['first']],
    'filter by code = B001' => [1, ['code'=> 'B003'], ['third']],
    'filter by code = NONEXISTINGCODE' => [0, ['code' => 'Bla Bla'], []],
    'filter by result entry period (April to June)' => [2, ['results_entry_period_open' => true],['first', 'third']],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['first', 'second', 'third']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['third', 'second', 'first']],
    'sort by code asc' => [3, ['order_by' => ['code' => 'asc']], ['first', 'second', 'third']],
    'sort by code desc' => [3, ['order_by' => ['code' => 'desc']], ['third', 'second', 'first']],
    'sort by name asc' => [3, ['order_by' => ['name' => 'asc']], ['first', 'second', 'third']],
    'sort by name desc' => [3, ['order_by' => ['name' => 'desc']], ['third', 'second', 'first']],
]);

test('getAllPaginated()', function (int $expected_count, array $filters, array $expected_model) {
    $test_date = Carbon::parse('2024-05-01')->format('Y-m-d');
    Carbon::setTestNow($test_date);

    $keys = [
        'first' => Exam::factory()->create([
            'code' => 'A001',
            'name->en' => 'English Exam 1',
            'name->zh' => '英语考试1',
            'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
            'description' => 'This is the last exam'
        ]),
        'second' => Exam::factory()->create([
            'code' => 'A002',
            'name->en' => 'English Exam 2',
            'name->zh' => '英语考试2',
            'start_date' => Carbon::parse('2024-10-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-11-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-10-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-11-15 15:59:59'),
            'description' => 'This is the last last exam'
        ]),
        'third' => Exam::factory()->create([
            'code' => 'B003',
            'name->en' => 'Math Exam 1',
            'name->zh' => '数学考试 1',
            'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
            'description' => 'For real this time'
        ]),
    ];

    $result = $this->examRepository->getAllPaginated($filters)->toArray();
    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'filter by name = English Exam 1' => [1, ['name' => 'English Exam 1'], ['first']],
    'filter by name = Math Exam 1' => [1, ['name' => 'Math Exam 1'],  ['third']],
    'filter by name = English Exam' => [2, ['name' => 'English Exam'], ['first', 'second']],
    'filter by name = Non Existing' => [0, ['name' => 'Bla Bla'], []],
    'filter by code = A001' => [1, ['code' => 'A001'], ['first']],
    'filter by code = B001' => [1, ['code'=> 'B003'], ['third']],
    'filter by code = NONEXISTINGCODE' => [0, ['code' => 'Bla Bla'], []],
    'filter by result entry period (April to June)' => [
        2,
        [
            'results_entry_period_open' => true,
            'results_entry_period_from' => Carbon::parse('2024-04-01 00:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-06-01 00:00:00'),
        ],
        ['first', 'third']
    ],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['first', 'second', 'third']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['third', 'second', 'first']],
    'sort by code asc' => [3, ['order_by' => ['code' => 'asc']], ['first', 'second', 'third']],
    'sort by code desc' => [3, ['order_by' => ['code' => 'desc']], ['third', 'second', 'first']],
    'sort by name asc' => [3, ['order_by' => ['name' => 'asc']], ['first', 'second', 'third']],
    'sort by name desc' => [3, ['order_by' => ['name' => 'desc']], ['third', 'second', 'first']],
]);