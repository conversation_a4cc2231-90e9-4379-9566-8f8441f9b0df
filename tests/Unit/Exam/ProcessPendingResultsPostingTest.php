<?php


use App\Console\Commands\Exam\ProcessPendingResultsPosting;
use App\Enums\ClassType;
use App\Enums\ConductRecordStatus;
use App\Enums\EnglishLevel;
use App\Enums\PeriodAttendanceStatus;
use App\Enums\RewardPunishmentRecordStatus;
use App\Jobs\StudentResultPostingJob;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\Competition;
use App\Models\CompetitionRecord;
use App\Models\ConductRecord;
use App\Models\ConductSetting;
use App\Models\ConductSettingTeacher;
use App\Models\Course;
use App\Models\Exam;
use App\Models\Grade;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\LeadershipPosition;
use App\Models\LeadershipPositionRecord;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\LeaveApplicationType;
use App\Models\MeritDemeritRewardPunishment;
use App\Models\MeritDemeritSetting;
use App\Models\PeriodAttendance;
use App\Models\PromotionMark;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\RewardPunishment;
use App\Models\RewardPunishmentRecord;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentReportCard;
use App\Models\Subject;
use App\Services\Exam\StudentGradingFrameworkService;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Mockery\MockInterface;

beforeEach(function(){
    Cache::clear();
    $this->academic_year = '2024';
});


test('failed due to lock', function () {

    $students = Student::factory(5)->create();

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$students[0]->id, $students[1]->id, $students[2]->id],
    ]);

    $lock = Cache::lock('posting-lock-' . $header1->id, 2);
    $lock->get();

    $result = Artisan::call('results-posting:process', ['--actual' => 0, '--unit-test' => 1]);
    expect($result)->toBe(-1);

    sleep(2);

    $result = Artisan::call('results-posting:process', ['--actual' => 0, '--unit-test' => 1]);
    expect($result)->toBe(1);
});

test('success', function () {

    $students = Student::factory(5)->create();

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$students[0]->id, $students[1]->id, $students[2]->id],
    ]);

    $header2 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [$students[3]->id, $students[4]->id],
    ]);

    Queue::fake();

    $object = $this->partialMock(ProcessPendingResultsPosting::class, function (MockInterface $mock) {
        $mock->shouldReceive('allStudentsPostingCompleted')->times(2);  // so status will remain as in progress
    });
    $object->__construct();

    Artisan::call('results-posting:process', ['--actual' => 1, '--unit-test' => 1]);

    Queue::assertPushed(StudentResultPostingJob::class, function($job) use ($header1, $students) {
        return $job->postingHeader->id === $header1->id && in_array($job->student->id, [$students[0]->id, $students[1]->id, $students[2]->id]);
    });

    Queue::assertPushed(StudentResultPostingJob::class, 3);

    $header1->refresh();
    $header2->refresh();
    expect($header1->status)->toBe(ResultsPostingHeader::STATUS_IN_PROGRESS)
        ->and($header2->status)->toBe(ResultsPostingHeader::STATUS_PENDING);

    Queue::shouldReceive();

    Queue::fake();

    Artisan::call('results-posting:process', ['--actual' => 1, '--unit-test' => 1]);

    Queue::assertPushed(StudentResultPostingJob::class, function($job) use ($header2, $students) {
        return $job->postingHeader->id === $header2->id && in_array($job->student->id, [$students[3]->id, $students[4]->id]);
    });

    Queue::assertPushed(StudentResultPostingJob::class, 2);

    $header1->refresh();
    $header2->refresh();
    expect($header1->status)->toBe(ResultsPostingHeader::STATUS_IN_PROGRESS)
        ->and($header2->status)->toBe(ResultsPostingHeader::STATUS_IN_PROGRESS);


});


test('error behaviour', function(){
    $students = Student::factory(5)->create();
    $course = Course::factory()->uec()->create();
    $year = SemesterYearSetting::factory()->create();
    
    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'code' => '2025SEM1',
        'semester_year_setting_id' => $year->id,
        'is_current_semester' => true
    ]);

    $grade = Grade::factory()->create([
        'name->en' => 'J1',
    ]);

    $class = ClassModel::factory()->create([
        'grade_id' => $grade->id,
        'type' => ClassType::PRIMARY,
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
    ]);

    // Semester 1 Classes
    StudentClass::factory(5)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[0]->id,
            'class_type' => ClassType::PRIMARY,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[1]->id,
            'class_type' => ClassType::PRIMARY,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[2]->id,
            'class_type' => ClassType::PRIMARY,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[3]->id,
            'class_type' => ClassType::PRIMARY,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[4]->id,
            'class_type' => ClassType::PRIMARY,
        ]
    ))->create();

    $exam_grading_scheme = GradingScheme::factory()->create([
        'name' => 'Default',
        'code' => 'DEFAULT'
    ]);

    GradingSchemeItem::factory(6)->state(new Sequence(
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 0,
            'to' => 0,
            'name' => 'Not Applicable',
            'display_as_name' => 'N/A',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 1,
            'to' => 59.99,
            'name' => 'Fail',
            'display_as_name' => 'F',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 60.00,
            'to' => 69.99,
            'name' => 'C',
            'display_as_name' => 'C',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 70.00,
            'to' => 79.99,
            'name' => 'B',
            'display_as_name' => 'B',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 80.00,
            'to' => 89.99,
            'name' => 'A',
            'display_as_name' => 'A',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 90.00,
            'to' => 100,
            'name' => 'A+',
            'display_as_name' => 'A+',
            'extra_marks' => 0,            
        ]
    ))->create();
    
    $conduct_grading_scheme = GradingScheme::factory()->create([
        'name' => 'Conduct',
        'code' => 'CONDUCT'
    ]);

    GradingSchemeItem::factory(6)->state(new Sequence(
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 0,
            'to' => 39.99,
            'name' => 'Fail',
            'display_as_name' => 'Fail',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 40,
            'to' => 59.99,
            'name' => 'Pass',
            'display_as_name' => 'Pass',
            'extra_marks' => 0.2,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 60,
            'to' => 79.99,
            'name' => 'Average',
            'display_as_name' => 'Average',
            'extra_marks' => 0.4,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 80,
            'to' => 84.99,
            'name' => 'Good',
            'display_as_name' => 'Good',
            'extra_marks' => 0.5,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 85,
            'to' => 89.99,
            'name' => 'Great',
            'display_as_name' => 'Great',
            'extra_marks' => 0.6,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 90,
            'to' => 100,
            'name' => 'Amazing',
            'display_as_name' => 'Amazing',
            'extra_marks' => 0.8,            
        ]
    ))->create();  

    $subjects = Subject::factory(5)->state(new Sequence(
        [
            'code' => 'CHINESE', 
            'name' => [
                'en' => 'Chinese',
                'zh' => '中文'
            ]
        ],
        [
            'code' => 'BM', 
            'name' => [
                'en' => 'Bahase Melayu',
                'zh' => '马来文'
            ]
        ],
        [
            'code' => 'EN', 
            'name' => [
                'en' => 'English',
                'zh' => '英文'
            ]
        ],
        [
            'code' => 'MATH', 
            'name' => [
                'en' => 'Mathematics',
                'zh' => '数学'
            ]
        ],
        [
            'code' => 'EN_ORAL', 
            'name' => [
                'en' => 'English (Oral)',
                'zh' => '英文讲话'
            ]
        ],
    ))->create();

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $subjects[3]->id
        ],
        [
            'semester_class_id' =>$semester_class->id,
            'subject_id' => $subjects[4]->id
        ],
    ))->create();

    foreach($students as $student){
        // skip students[1] -> this student will take all subjects
        if ($student->id == $students[1]->id) {
            continue;
        }
        ClassSubjectStudent::factory(3)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ]
        ))->create();
    }

    ClassSubjectStudent::factory(5)->state(new Sequence(
        [
            'student_id' => $students[1]->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $students[1]->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $students[1]->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $students[1]->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
        [
            'student_id' => $students[1]->id,
            'class_subject_id' => $class_subjects[4]->id
        ]       
    ))->create();

    $exams = Exam::factory(3)->state(new Sequence(
        [
            'code' => 'SEM1EXAM', 
            'results_entry_period_from' => '2025-01-01 16:00:00', 
            'results_entry_period_to' => '2025-06-30 15:59:59'
        ],
        [
            'code' => 'SEM2EXAM', 
            'results_entry_period_from' => '2025-01-01 16:00:00', 
            'results_entry_period_to' => '2025-06-30 15:59:59'
        ],
        [
            'code' => 'FINALEXAM', 
            'results_entry_period_from' => '2025-01-01 16:00:00', 
            'results_entry_period_to' => '2025-06-30 15:59:59'
        ],
    ))->create();    

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-2.json')), true),
    ]);

    // setup student's grading framework
    $sgf0 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[0])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf1 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[1])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf2 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[2])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf3 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[3])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf4 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[4])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    // PROCESS SEM1RESULT
    $sem1_header = ResultsPostingHeader::factory()->create([
        'code' => 'SEM1EXAM',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [
            $students[0]->id,
            $students[1]->id,
            $students[2]->id,
            $students[3]->id,
            $students[4]->id
        ],
        'report_card_output_code' => 'SEM1RESULT',
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting->id,
    ]);

    Artisan::call('results-posting:process', ['--actual' => 1]);

    $this->assertDatabaseHas(ResultsPostingHeader::class, [
        'status' => ResultsPostingHeader::STATUS_ERROR,
        'id' => $sem1_header->id,
    ]);
});

test('behaviour when subject is in one semester but not the other', function (){
    $students = Student::factory(5)->create();
    $course = Course::factory()->uec()->create();
    $year = SemesterYearSetting::factory()->create();
    
    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'code' => '2025SEM1',
        'semester_year_setting_id' => $year->id,
        'is_current_semester' => false
    ]);

    $semester_setting_2 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'code' => '2025SEM2',
        'semester_year_setting_id' => $year->id,
        'is_current_semester' => true
    ]);

    $grade = Grade::factory()->create([
        'name->en' => 'J1',
    ]);

    $class = ClassModel::factory()->create([
        'grade_id' => $grade->id,
        'type' => ClassType::PRIMARY,
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
        'is_active' => false
    ]);

    $semester_class_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting_2->id,
        'class_id' => $class->id,  
    ]);

    $promotion_marks = PromotionMark::factory(2)->state(new Sequence(
        [
            'semester_class_id' => $semester_class_2->id,
            'conduct_mark_for_promotion' => 60.00,
            'net_average_for_promotion' => 56.60,
        ],
    ))->create();

    // Semester 1 Classes
    StudentClass::factory(5)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[0]->id,
            'class_type' => ClassType::PRIMARY,
            'is_active' => false
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[1]->id,
            'class_type' => ClassType::PRIMARY,
            'is_active' => false
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[2]->id,
            'class_type' => ClassType::PRIMARY,
            'is_active' => false
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[3]->id,
            'class_type' => ClassType::PRIMARY,
            'is_active' => false
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[4]->id,
            'class_type' => ClassType::PRIMARY,
            'is_active' => false
        ],
    ))->create();

    // Semester 2 Classes
    StudentClass::factory(5)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $semester_class_2->id,
            'student_id' => $students[0]->id,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $semester_class_2->id,
            'student_id' => $students[1]->id,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $semester_class_2->id,
            'student_id' => $students[2]->id,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $semester_class_2->id,
            'student_id' => $students[3]->id,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $semester_class_2->id,
            'student_id' => $students[4]->id,
            'class_type' => ClassType::PRIMARY
        ]
    ))->create();

    $exam_grading_scheme = GradingScheme::factory()->create([
        'name' => 'Default',
        'code' => 'DEFAULT'
    ]);

    GradingSchemeItem::factory(6)->state(new Sequence(
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 0,
            'to' => 0,
            'name' => 'Not Applicable',
            'display_as_name' => 'N/A',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 1,
            'to' => 59.99,
            'name' => 'Fail',
            'display_as_name' => 'F',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 60.00,
            'to' => 69.99,
            'name' => 'C',
            'display_as_name' => 'C',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 70.00,
            'to' => 79.99,
            'name' => 'B',
            'display_as_name' => 'B',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 80.00,
            'to' => 89.99,
            'name' => 'A',
            'display_as_name' => 'A',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 90.00,
            'to' => 100,
            'name' => 'A+',
            'display_as_name' => 'A+',
            'extra_marks' => 0,            
        ]
    ))->create();
    
    $conduct_grading_scheme = GradingScheme::factory()->create([
        'name' => 'Conduct',
        'code' => 'CONDUCT'
    ]);

    GradingSchemeItem::factory(6)->state(new Sequence(
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 0,
            'to' => 39.99,
            'name' => 'Fail',
            'display_as_name' => 'Fail',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 40,
            'to' => 59.99,
            'name' => 'Pass',
            'display_as_name' => 'Pass',
            'extra_marks' => 0.2,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 60,
            'to' => 79.99,
            'name' => 'Average',
            'display_as_name' => 'Average',
            'extra_marks' => 0.4,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 80,
            'to' => 84.99,
            'name' => 'Good',
            'display_as_name' => 'Good',
            'extra_marks' => 0.5,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 85,
            'to' => 89.99,
            'name' => 'Great',
            'display_as_name' => 'Great',
            'extra_marks' => 0.6,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 90,
            'to' => 100,
            'name' => 'Amazing',
            'display_as_name' => 'Amazing',
            'extra_marks' => 0.8,            
        ]
    ))->create();  

    $subjects = Subject::factory(3)->state(new Sequence(
        [
            'code' => 'CHINESE', 
            'name' => [
                'en' => 'Chinese',
                'zh' => '中文'
            ]
        ],
        [
            'code' => 'BM', 
            'name' => [
                'en' => 'Bahase Melayu',
                'zh' => '马来文'
            ]
        ],
        [
            'code' => 'EN', 
            'name' => [
                'en' => 'English',
                'zh' => '英文'
            ]
        ]
    ))->create();

    $class_subjects = ClassSubject::factory(3)->state(new Sequence(
        [
            'semester_class_id' => $semester_class_2->id,
            'subject_id' => $subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class_2->id,
            'subject_id' => $subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class_2->id,
            'subject_id' => $subjects[2]->id
        ]
    ))->create();

    foreach($students as $student){
        ClassSubjectStudent::factory(3)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ]
        ))->create();
    }

    $exams = Exam::factory(2)->state(new Sequence(
        [
            'code' => 'SEM1EXAM', 
            'results_entry_period_from' => '2025-01-01 16:00:00', 
            'results_entry_period_to' => '2025-06-30 15:59:59'
        ],
        [
            'code' => 'SEM2EXAM', 
            'results_entry_period_from' => '2025-01-01 16:00:00', 
            'results_entry_period_to' => '2025-06-30 15:59:59'
        ]
    ))->create();    

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-semester-test.json')), true),
    ]);

    // setup student's grading framework
    $sgf0 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[0])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf1 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[1])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf2 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[2])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf3 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[3])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf4 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[4])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $result_source_subject_components = ResultSourceSubjectComponent::all();
    foreach ($result_source_subject_components as $component){
        $component->update(['actual_score' => rand(1, 100)]);
    }

    $sem1_header = ResultsPostingHeader::factory()->create([
        'code' => '1',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [
            $students[0]->id,
            $students[1]->id,
            $students[2]->id,
            $students[3]->id,
            $students[4]->id
        ],
        'report_card_output_code' => 'SEM1RESULT',
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting->id,
    ]);
    Artisan::call('results-posting:process', ['--actual' => 1]);

    $sem2_header = ResultsPostingHeader::factory()->create([
        'code' => '2',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [
            $students[0]->id,
            $students[1]->id,
            $students[2]->id,
            $students[3]->id,
            $students[4]->id
        ],
        'report_card_output_code' => 'SEM2RESULT',
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting_2->id,
    ]);
    Artisan::call('results-posting:process', ['--actual' => 1]);

    $final_header = ResultsPostingHeader::factory()->create([
        'code' => '3',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [
            $students[0]->id,
            $students[1]->id,
            $students[2]->id,
            $students[3]->id,
            $students[4]->id
        ],
        'report_card_output_code' => 'FINALRESULT',
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting_2->id,
    ]);
    Artisan::call('results-posting:process', ['--actual' => 1]);
    print_r('One Semester Subject Behavior Report Cards: ');
    print_r(StudentReportCard::all()->sortBy('created_at')->pluck('file_url')->toArray());

    $rco_final_student0 = $sgf0->outputs()->where('code', 'SEM2RESULT')->first();
    $en_component_sem2 = $rco_final_student0->components->where('code', 'EN')->first();

    $rco_final_student0 = $sgf0->outputs()->where('code', 'FINALRESULT')->first();
    $en_component_final = $rco_final_student0->components->where('code', 'EN')->first();


    // Confirm that the line item exist
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $en_component_final->id,
        'report_card_output_component_code' => $en_component_final->code,
        'student_id' => $students[0]->id,
        'subject_id' => $en_component_final->subject_id,
        'grading_scheme_id' => $en_component_final->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
    ]);

    // Confirm that sem 2 and final value is equal (since only sem 2 has english)
    expect($en_component_final->total)->toEqual($en_component_sem2->total);
});



test('exemption and electives behaviour', function(){
    $students = Student::factory(5)->create();
    $course = Course::factory()->uec()->create();
    $year = SemesterYearSetting::factory()->create();
    
    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'code' => '2025SEM1',
        'semester_year_setting_id' => $year->id,
        'is_current_semester' => true
    ]);

    $grade = Grade::factory()->create([
        'name->en' => 'J1',
    ]);

    $class = ClassModel::factory()->create([
        'grade_id' => $grade->id,
        'type' => ClassType::PRIMARY,
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);

    $class = ClassModel::factory()->create([
        'grade_id' => $grade->id,
        'type' => ClassType::ENGLISH,
        'name->en' => 'J111',
        'name->zh' => '初一11',
        'english_level' => EnglishLevel::ELEMENTARY->value
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
    ]);

    $english_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,  
    ]);

    $promotion_marks = PromotionMark::factory(2)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'conduct_mark_for_promotion' => 60.00,
            'net_average_for_promotion' => 56.60,
        ],
    ))->create();

    // Semester 1 Classes
    StudentClass::factory(6)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[0]->id,
            'class_type' => ClassType::PRIMARY,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[1]->id,
            'class_type' => ClassType::PRIMARY,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[2]->id,
            'class_type' => ClassType::PRIMARY,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[3]->id,
            'class_type' => ClassType::PRIMARY,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[4]->id,
            'class_type' => ClassType::PRIMARY,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $english_semester_class->id,
            'student_id' => $students[0]->id,
            'class_type' => ClassType::ENGLISH,
        ]
    ))->create();

    $exam_grading_scheme = GradingScheme::factory()->create([
        'name' => 'Default',
        'code' => 'DEFAULT'
    ]);

    GradingSchemeItem::factory(6)->state(new Sequence(
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 0,
            'to' => 0,
            'name' => 'Not Applicable',
            'display_as_name' => 'N/A',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 1,
            'to' => 59.99,
            'name' => 'Fail',
            'display_as_name' => 'F',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 60.00,
            'to' => 69.99,
            'name' => 'C',
            'display_as_name' => 'C',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 70.00,
            'to' => 79.99,
            'name' => 'B',
            'display_as_name' => 'B',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 80.00,
            'to' => 89.99,
            'name' => 'A',
            'display_as_name' => 'A',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 90.00,
            'to' => 100,
            'name' => 'A+',
            'display_as_name' => 'A+',
            'extra_marks' => 0,            
        ]
    ))->create();
    
    $conduct_grading_scheme = GradingScheme::factory()->create([
        'name' => 'Conduct',
        'code' => 'CONDUCT'
    ]);

    GradingSchemeItem::factory(6)->state(new Sequence(
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 0,
            'to' => 39.99,
            'name' => 'Fail',
            'display_as_name' => 'Fail',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 40,
            'to' => 59.99,
            'name' => 'Pass',
            'display_as_name' => 'Pass',
            'extra_marks' => 0.2,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 60,
            'to' => 79.99,
            'name' => 'Average',
            'display_as_name' => 'Average',
            'extra_marks' => 0.4,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 80,
            'to' => 84.99,
            'name' => 'Good',
            'display_as_name' => 'Good',
            'extra_marks' => 0.5,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 85,
            'to' => 89.99,
            'name' => 'Great',
            'display_as_name' => 'Great',
            'extra_marks' => 0.6,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 90,
            'to' => 100,
            'name' => 'Amazing',
            'display_as_name' => 'Amazing',
            'extra_marks' => 0.8,            
        ]
    ))->create();  

    $subjects = Subject::factory(5)->state(new Sequence(
        [
            'code' => 'CHINESE', 
            'name' => [
                'en' => 'Chinese',
                'zh' => '中文'
            ]
        ],
        [
            'code' => 'BM', 
            'name' => [
                'en' => 'Bahase Melayu',
                'zh' => '马来文'
            ]
        ],
        [
            'code' => 'EN', 
            'name' => [
                'en' => 'English',
                'zh' => '英文'
            ]
        ],
        [
            'code' => 'MATH', 
            'name' => [
                'en' => 'Mathematics',
                'zh' => '数学'
            ]
        ],
        [
            'code' => 'EN_ORAL', 
            'name' => [
                'en' => 'English (Oral)',
                'zh' => '英文讲话'
            ]
        ],
    ))->create();

    $class_subjects = ClassSubject::factory(6)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $subjects[3]->id
        ],
        [
            'semester_class_id' =>$semester_class->id,
            'subject_id' => $subjects[4]->id
        ],
        [
            'semester_class_id' =>$english_semester_class->id,
            'subject_id' => $subjects[2]->id
        ]
    ))->create();

    foreach($students as $student){
        // skip students[1] -> this student will take all subjects
        if ($student->id == $students[1]->id) {
            continue;
        }
        ClassSubjectStudent::factory(3)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ]
        ))->create();
    }

    ClassSubjectStudent::factory(5)->state(new Sequence(
        [
            'student_id' => $students[1]->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $students[1]->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $students[1]->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $students[1]->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
        [
            'student_id' => $students[1]->id,
            'class_subject_id' => $class_subjects[4]->id
        ]       
    ))->create();

    $exams = Exam::factory(3)->state(new Sequence(
        [
            'code' => 'SEM1EXAM', 
            'results_entry_period_from' => '2025-01-01 16:00:00', 
            'results_entry_period_to' => '2025-06-30 15:59:59'
        ],
        [
            'code' => 'SEM2EXAM', 
            'results_entry_period_from' => '2025-01-01 16:00:00', 
            'results_entry_period_to' => '2025-06-30 15:59:59'
        ],
        [
            'code' => 'FINALEXAM', 
            'results_entry_period_from' => '2025-01-01 16:00:00', 
            'results_entry_period_to' => '2025-06-30 15:59:59'
        ],
    ))->create();    

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-2.json')), true),
    ]);

    // setup student's grading framework
    $sgf0 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[0])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf1 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[1])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf2 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[2])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf3 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[3])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf4 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[4])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    // SEM1EXAM STUDENT 0 
    $rs_sem1_student0 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf0->id)->first();

    $rs_sub1_sem1_student0 = ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem1_student0 = ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem1_student0 = ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();

    // Student 0 is exempted from Subject 1 (Chinese)
    ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->update(['is_exempted' => true]);

    $rs_sub1_sem1_student0->components()->update([
        'actual_score' => 85
    ]);
    $rs_sub2_sem1_student0->components()->update([
        'actual_score' => 80
    ]);
    $rs_sub3_sem1_student0->components()->update([
        'actual_score' => 83,
    ]);
    
    // SEM1EXAM STUDENT 1
    $rs_sem1_student1 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf1->id)->first();

    $rs_sub1_sem1_student1 = ResultSourceSubject::where('result_source_id', $rs_sem1_student1->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem1_student1 = ResultSourceSubject::where('result_source_id', $rs_sem1_student1->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem1_student1 = ResultSourceSubject::where('result_source_id', $rs_sem1_student1->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_sem1_student1 = ResultSourceSubject::where('result_source_id', $rs_sem1_student1->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_sem1_student1 = ResultSourceSubject::where('result_source_id', $rs_sem1_student1->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_sem1_student1->components()->update([
        'actual_score' => 70
    ]);
    $rs_sub2_sem1_student1->components()->update([
        'actual_score' => 70
    ]);
    $rs_sub3_sem1_student1->components()->update([
        'actual_score' => 65,
    ]);
    $rs_sub4_sem1_student1->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 80
    ]);
    $rs_sub4_sem1_student1->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 75
    ]);
    $rs_sub5_sem1_student1->update([
        'actual_score_grade' => 'B'
    ]);

    // SEM1EXAM STUDENT 2
    $rs_sem1_student2 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf2->id)->first();

    $rs_sub1_sem1_student2 = ResultSourceSubject::where('result_source_id', $rs_sem1_student2->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem1_student2 = ResultSourceSubject::where('result_source_id', $rs_sem1_student2->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem1_student2 = ResultSourceSubject::where('result_source_id', $rs_sem1_student2->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();

    $rs_sub1_sem1_student2->components()->update([
        'actual_score' => 60
    ]);
    $rs_sub2_sem1_student2->components()->update([
        'actual_score' => 65
    ]);
    $rs_sub3_sem1_student2->components()->update([
        'actual_score' => 64,
    ]);

    // SEM1EXAM STUDENT 3
    $rs_sem1_student3 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf3->id)->first();

    $rs_sub1_sem1_student3 = ResultSourceSubject::where('result_source_id', $rs_sem1_student3->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem1_student3 = ResultSourceSubject::where('result_source_id', $rs_sem1_student3->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem1_student3 = ResultSourceSubject::where('result_source_id', $rs_sem1_student3->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();

    $rs_sub1_sem1_student3->components()->update([
        'actual_score' => 90
    ]);
    $rs_sub2_sem1_student3->components()->update([
        'actual_score' => 95
    ]);
    $rs_sub3_sem1_student3->components()->update([
        'actual_score' => 92,
    ]);

    // SEM1EXAM STUDENT 4
    $rs_sem1_student4 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf4->id)->first();

    $rs_sub1_sem1_student4 = ResultSourceSubject::where('result_source_id', $rs_sem1_student4->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem1_student4 = ResultSourceSubject::where('result_source_id', $rs_sem1_student4->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem1_student4 = ResultSourceSubject::where('result_source_id', $rs_sem1_student4->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();


    $rs_sub1_sem1_student4->components()->update([
        'actual_score' => 50
    ]);
    $rs_sub2_sem1_student4->components()->update([
        'actual_score' => 45
    ]);
    $rs_sub3_sem1_student4->components()->update([
        'actual_score' => 60,
    ]);

    // PROCESS SEM1RESULT
    $sem1_header = ResultsPostingHeader::factory()->create([
        'code' => 'SEM1EXAM',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [
            $students[0]->id,
            $students[1]->id,
            $students[2]->id,
            $students[3]->id,
            $students[4]->id
        ],
        'report_card_output_code' => 'SEM1RESULT',
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting->id,
    ]);
    Artisan::call('results-posting:process', ['--actual' => 1]);

    $this->assertDatabaseHas(ResultsPostingHeader::class, [
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'id' => $sem1_header->id,
        'errors' => null
    ]);

    $rco_sem1_student0 = $sgf0->outputs()->where('code', 'SEM1RESULT')->first();
    $chinese_component = $rco_sem1_student0->components->where('code', 'CHINESE')->first();
    $bm_component = $rco_sem1_student0->components->where('code', 'BM')->first();
    $en_component = $rco_sem1_student0->components->where('code', 'EN')->first();
    $gt_component = $rco_sem1_student0->components->where('code', 'GT')->first();
    $gw_component = $rco_sem1_student0->components->where('code', 'GW')->first();
    $ga_component = $rco_sem1_student0->components->where('code', 'GA')->first();
    $na_component = $rco_sem1_student0->components->where('code', 'SYS_NET_AVG')->first();

    // Chinese is exempted, so rank is not calculated
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $chinese_component->id,
        'report_card_output_component_code' => $chinese_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $chinese_component->subject_id,
        'grading_scheme_id' => $chinese_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'calculate_rank' => false,
        'total' => 85,
        'label' => 'A',
        'total_grade->display_as_name' => 'A',
        'total_grade->name' => 'A',
        'total_grade->extra_marks' => 0,
        'weightage_multiplier' => 0,
        'class_rank' => null,
        'grade_rank' => null,
        'grade_percentile_rank' => null
    ]);

    //Eventho chinese is exempted, result source and output component MUST RETAIN ORIGINAL WEIGHTAGE MULTIPLIER (2)
    $rs_sem1_student0 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf0->id)->first();

    $chinese_rss = ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    
    expect($chinese_component->weightage_multiplier)->toEqual(2);
    expect($chinese_rss->weightage_multiplier)->toEqual(2);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $bm_component->id,
        'report_card_output_component_code' => $bm_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $bm_component->subject_id,
        'grading_scheme_id' => $bm_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 80,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $en_component->id,
        'report_card_output_component_code' => $en_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $en_component->subject_id,
        'grading_scheme_id' => $en_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 83,
        'label' => 'A',
    ]);

    // Not taking math and en oral, so expect to be null
    $math_component = $rco_sem1_student0->components->where('code', 'MATH')->first();
    $en_oral_component = $rco_sem1_student0->components->where('code', 'EN_ORAL')->first();

    expect($math_component)->toBe(null);
    expect($en_oral_component)->toBe(null);

    // Student exempted from chinese, so total marks is 80*2 (malay) + 83*1 (english)
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $gt_component->id,
        'report_card_output_component_code' => $gt_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $gt_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 243.00
    ]);

    // Student exempted from chinese, so total weightage is 2 (malay) + 1 (english)
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $gw_component->id,
        'report_card_output_component_code' => $gw_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $gw_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 3.00
    ]);

    // GA is calculated as 243/3 = 81.00
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $ga_component->id,
        'report_card_output_component_code' => $ga_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $ga_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 81.00
    ]);

    // NA same as GA since no marks added/subtracted
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $na_component->id,
        'report_card_output_component_code' => $na_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $na_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 81.00
    ]);


    $rco_sem1_student1 = $sgf1->outputs()->where('code', 'SEM1RESULT')->first();
    $chinese_component = $rco_sem1_student1->components->where('code', 'CHINESE')->first();
    $bm_component = $rco_sem1_student1->components->where('code', 'BM')->first();
    $en_component = $rco_sem1_student1->components->where('code', 'EN')->first();
    $math_component = $rco_sem1_student1->components->where('code', 'MATH')->first();
    $en_oral_component = $rco_sem1_student1->components->where('code', 'EN_ORAL')->first();
    $gt_component = $rco_sem1_student1->components->where('code', 'GT')->first();
    $gw_component = $rco_sem1_student1->components->where('code', 'GW')->first();

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $chinese_component->id,
        'report_card_output_component_code' => $chinese_component->code,
        'student_id' => $students[1]->id,
        'subject_id' => $chinese_component->subject_id,
        'grading_scheme_id' => $chinese_component->grading_scheme_id,
        'grading_framework_id' => $sgf1->grading_framework_id,
        'calculate_rank' => true,
        'total' => 70.00,
        'label' => 'B',
        'total_grade->display_as_name' => 'B',
        'total_grade->name' => 'B',
        'total_grade->extra_marks' => 0,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $bm_component->id,
        'report_card_output_component_code' => $bm_component->code,
        'student_id' => $students[1]->id,
        'subject_id' => $bm_component->subject_id,
        'grading_scheme_id' => $bm_component->grading_scheme_id,
        'grading_framework_id' => $sgf1->grading_framework_id,
        'total' => 70,
        'label' => 'B',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $en_component->id,
        'report_card_output_component_code' => $en_component->code,
        'student_id' => $students[1]->id,
        'subject_id' => $en_component->subject_id,
        'grading_scheme_id' => $en_component->grading_scheme_id,
        'grading_framework_id' => $sgf1->grading_framework_id,
        'total' => 65,
        'label' => 'C',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $math_component->id,
        'report_card_output_component_code' => $math_component->code,
        'student_id' => $students[1]->id,
        'subject_id' => $math_component->subject_id,
        'grading_scheme_id' => $math_component->grading_scheme_id,
        'grading_framework_id' => $sgf1->grading_framework_id,
        'total' => 76.5,
        'label' => 'B',
    ]);

    $rs_sub5_sem1_student1 = ResultSourceSubject::where('result_source_id', $rs_sem1_student1->id)
    ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
    ->first();

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $en_oral_component->id,
        'report_card_output_component_code' => $en_oral_component->code,
        'student_id' => $students[1]->id,
        'subject_id' => $en_oral_component->subject_id,
        'grading_scheme_id' => $en_oral_component->grading_scheme_id,
        'grading_framework_id' => $sgf1->grading_framework_id,
        'label' => 'B',
    ]);

    // GT is 421.5 (sum of all subjects as student 1 takes all subjects)
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $gt_component->id,
        'report_card_output_component_code' => $gt_component->code,
        'student_id' => $students[1]->id,
        'subject_id' => $gt_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 421.50
    ]);

    // Weight multiplier is 6 (student 1 takes all subjects)
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $gw_component->id,
        'report_card_output_component_code' => $gw_component->code,
        'student_id' => $students[1]->id,
        'subject_id' => $gw_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 6.00
    ]);

    /* Student 0 has Chinese Exempted, so when checking report card:
        - Chinese line item should not be present
        - Chinese Grade Percentile should be 25, 50, 75, 100 (only 4 students since student 0 is exempted)
    */
    print_r('Exemption Behaviour Report Card:');
    print_r(StudentReportCard::all()->pluck('file_url')->toArray());

});

test('success end-to-end', function(){

    $students = Student::factory(5)->create();
    $course = Course::factory()->uec()->create();
    $year = SemesterYearSetting::factory()->create();
    
    $semester_setting_1 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'code' => '2025SEM1',
        'semester_year_setting_id' => $year->id,
    ]);
    $semester_setting_2 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'code'=> '2025SEM2',
        'semester_year_setting_id' => $year->id,
        'is_current_semester' => true
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'J1',
    ]);

    $class1 = ClassModel::factory()->create([
        'grade_id' => $grade1->id,
        'type' => ClassType::PRIMARY,
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);

    $semester_class_sem1class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting_1->id,
        'class_id' => $class1->id,
        'is_active' => false
    ]);
    
    $semester_class_sem2class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting_2->id,
        'class_id' => $class1->id
    ]);

    $scout_society = ClassModel::factory()->create([
        'name->en' => 'SCOUT',
        'type' => ClassType::SOCIETY,
    ]);

    $badminton_society = ClassModel::factory()->create([
        'name->en' => 'BADMINTON',
        'type' => ClassType::SOCIETY,
    ]);

    $scout_sem1 = SemesterClass::factory() ->create([
        'semester_setting_id' => $semester_setting_1->id,
        'class_id' => $scout_society->id
    ]);

    $scout_sem2 = SemesterClass::factory() ->create([
        'semester_setting_id' => $semester_setting_2->id,
        'class_id' => $scout_society->id
    ]);

    $badminton_sem1 = SemesterClass::factory() ->create([
        'semester_setting_id' => $semester_setting_1->id,
        'class_id' => $badminton_society->id
    ]);

    $badminton_sem2 = SemesterClass::factory() ->create([
        'semester_setting_id' => $semester_setting_2->id,
        'class_id' => $badminton_society->id
    ]);

    // Sem 1 Societies
    StudentClass::factory(5)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $scout_sem1->id,
            'student_id' => $students[0]->id
        ],
        [
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $scout_sem1->id,
            'student_id' => $students[1]->id
        ],
        [
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $scout_sem1->id,
            'student_id' => $students[2]->id
        ],
        [
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $badminton_sem1->id,
            'student_id' => $students[3]->id
        ],
        [
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $badminton_sem1->id,
            'student_id' => $students[4]->id
        ]
    ))->create();

    StudentClass::factory(5)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $scout_sem2->id,
            'student_id' => $students[0]->id
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $scout_sem2->id,
            'student_id' => $students[1]->id
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $scout_sem2->id,
            'student_id' => $students[2]->id
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $badminton_sem2->id,
            'student_id' => $students[3]->id
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $badminton_sem2->id,
            'student_id' => $students[4]->id
        ]
    ))->create();

    $promotion_marks = PromotionMark::factory(2)->state(new Sequence(
        [
            'semester_class_id' => $semester_class_sem1class1->id,
            'conduct_mark_for_promotion' => 60.00,
            'net_average_for_promotion' => 56.60,
        ],
        [
            'semester_class_id' => $semester_class_sem2class1->id,
            'conduct_mark_for_promotion' => 60.00,
            'net_average_for_promotion' => 56.60,
        ]
    ))->create();

    // Semester 1 Classes
    StudentClass::factory(5)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $semester_class_sem1class1->id,
            'student_id' => $students[0]->id,
            'class_type' => ClassType::PRIMARY,
            'is_active' => false
        ],
        [
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $semester_class_sem1class1->id,
            'student_id' => $students[1]->id,
            'class_type' => ClassType::PRIMARY,
            'is_active' => false
        ],
        [
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $semester_class_sem1class1->id,
            'student_id' => $students[2]->id,
            'class_type' => ClassType::PRIMARY,
            'is_active' => false
        ],
        [
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $semester_class_sem1class1->id,
            'student_id' => $students[3]->id,
            'class_type' => ClassType::PRIMARY,
            'is_active' => false
        ],
        [
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $semester_class_sem1class1->id,
            'student_id' => $students[4]->id,
            'class_type' => ClassType::PRIMARY,
            'is_active' => false
        ]
    ))->create();

    // Semester 2 Classes
    StudentClass::factory(5)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $semester_class_sem2class1->id,
            'student_id' => $students[0]->id,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $semester_class_sem2class1->id,
            'student_id' => $students[1]->id,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $semester_class_sem2class1->id,
            'student_id' => $students[2]->id,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $semester_class_sem2class1->id,
            'student_id' => $students[3]->id,
            'class_type' => ClassType::PRIMARY
        ],
        [
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $semester_class_sem2class1->id,
            'student_id' => $students[4]->id,
            'class_type' => ClassType::PRIMARY
        ]
    ))->create();

    $exam_grading_scheme = GradingScheme::factory()->create([
        'name' => 'Default',
        'code' => 'DEFAULT'
    ]);

    GradingSchemeItem::factory(6)->state(new Sequence(
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 0,
            'to' => 0,
            'name' => 'Not Applicable',
            'display_as_name' => 'N/A',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 1,
            'to' => 59.99,
            'name' => 'Fail',
            'display_as_name' => 'F',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 60.00,
            'to' => 69.99,
            'name' => 'C',
            'display_as_name' => 'C',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 70.00,
            'to' => 79.99,
            'name' => 'B',
            'display_as_name' => 'B',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 80.00,
            'to' => 89.99,
            'name' => 'A',
            'display_as_name' => 'A',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $exam_grading_scheme->id,
            'from' => 90.00,
            'to' => 100,
            'name' => 'A+',
            'display_as_name' => 'A+',
            'extra_marks' => 0,            
        ]
    ))->create();
    
    $conduct_grading_scheme = GradingScheme::factory()->create([
        'name' => 'Conduct',
        'code' => 'CONDUCT'
    ]);

    GradingSchemeItem::factory(6)->state(new Sequence(
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 0,
            'to' => 39.99,
            'name' => 'Fail',
            'display_as_name' => 'Fail',
            'extra_marks' => 0,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 40,
            'to' => 59.99,
            'name' => 'Pass',
            'display_as_name' => 'Pass',
            'extra_marks' => 0.2,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 60,
            'to' => 79.99,
            'name' => 'Average',
            'display_as_name' => 'Average',
            'extra_marks' => 0.4,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 80,
            'to' => 84.99,
            'name' => 'Good',
            'display_as_name' => 'Good',
            'extra_marks' => 0.5,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 85,
            'to' => 89.99,
            'name' => 'Great',
            'display_as_name' => 'Great',
            'extra_marks' => 0.6,
        ],
        [
            'grading_scheme_id' => $conduct_grading_scheme->id,
            'from' => 90,
            'to' => 100,
            'name' => 'Amazing',
            'display_as_name' => 'Amazing',
            'extra_marks' => 0.8,            
        ]
    ))->create();    

    $subjects = Subject::factory(5)->state(new Sequence(
        [
            'code' => 'CHINESE', 
            'name' => [
                'en' => 'Chinese',
                'zh' => '中文'
            ]
        ],
        [
            'code' => 'BM', 
            'name' => [
                'en' => 'Bahase Melayu',
                'zh' => '马来文'
            ]
        ],
        [
            'code' => 'EN', 
            'name' => [
                'en' => 'English',
                'zh' => '英文'
            ]
        ],
        [
            'code' => 'MATH', 
            'name' => [
                'en' => 'Mathematics',
                'zh' => '数学'
            ]
        ],
        [
            'code' => 'EN_ORAL', 
            'name' => [
                'en' => 'English (Oral)',
                'zh' => '英文讲话'
            ]
        ],
    ))->create();

    $class_subjects = ClassSubject::factory(5)->state(new Sequence(
        [
            'semester_class_id' => $semester_class_sem2class1->id,
            'subject_id' => $subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class_sem2class1->id,
            'subject_id' => $subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class_sem2class1->id,
            'subject_id' => $subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class_sem2class1->id,
            'subject_id' => $subjects[3]->id
        ],
        [
            'semester_class_id' =>$semester_class_sem2class1->id,
            'subject_id' => $subjects[4]->id
        ],
    ))->create();

    foreach($students as $student){
        ClassSubjectStudent::factory(5)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[4]->id
            ],
        ))->create();
    }

    $exams = Exam::factory(3)->state(new Sequence(
        [
            'code' => 'SEM1EXAM', 
            'results_entry_period_from' => '2025-01-01 16:00:00', 
            'results_entry_period_to' => '2025-06-30 15:59:59'
        ],
        [
            'code' => 'SEM2EXAM', 
            'results_entry_period_from' => '2025-01-01 16:00:00', 
            'results_entry_period_to' => '2025-06-30 15:59:59'
        ],
        [
            'code' => 'FINALEXAM', 
            'results_entry_period_from' => '2025-01-01 16:00:00', 
            'results_entry_period_to' => '2025-06-30 15:59:59'
        ],
    ))->create();

    $competitions = Competition::factory(4)->state(new Sequence(
        [ 
            'name' => 'Football Match',
            'date' => '2025-03-01' 
        ],
        [ 
            'name' => 'Tennis Open',
            'date' => '2025-05-01' 
        ],
        [ 
            'name' => 'Swimming competition',
            'date' => '2025-10-01' 
        ],      
        [ 
            'name' => 'Tennis Open',
            'date' => '2025-10-01' 
        ],
    ))->create();

    // Sem 1 Competition Records
    CompetitionRecord::factory(2)->state(new Sequence(
        [
            'competition_id' => $competitions[0]->id,
            'student_id' => $students[0]->id,
            'semester_class_id' => $semester_class_sem1class1,
            'mark' => 1,
        ],
        [
            'competition_id' => $competitions[0]->id,
            'student_id' => $students[0]->id,
            'semester_class_id' => $semester_class_sem1class1,
            'mark' => 1,
        ]
    ))->create();

    // Sem 2 Competition Records
    CompetitionRecord::factory(2)->state(new Sequence(
        [
            'competition_id' => $competitions[2]->id,
            'student_id' => $students[0]->id,
            'semester_class_id' => $semester_class_sem2class1,
            'mark' => 1,
        ],
        [
            'competition_id' => $competitions[2]->id,
            'student_id' => $students[1]->id,
            'semester_class_id' => $semester_class_sem2class1,
            'mark' => 1,
        ]
    ))->create();

    // Conduct Settings
    $conduct_setting_sem1 = ConductSetting::factory()->create([
        'semester_class_id' => $semester_setting_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'grading_scheme_id' => $conduct_grading_scheme->id
    ]);

    $conduct_setting_sem2 = ConductSetting::factory()->create([
        'semester_class_id' => $semester_setting_2->id,
        'semester_setting_id' => $semester_setting_2->id,
        'grading_scheme_id' => $conduct_grading_scheme->id
    ]);

    // Conduct Setting Teacher Sem 1
    $cst_homeroom_sem1 = ConductSettingTeacher::factory()->create([
        'conduct_setting_id' => $conduct_setting_sem1->id,
        'is_homeroom_teacher' => true,
        'is_active' => true,   
    ]);

    $cst_sem1 = ConductSettingTeacher::factory(5)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'is_homeroom_teacher' => false,
            'is_active' => true,
        ]
    ))->create();


    $cst_homeroom_sem2 = ConductSettingTeacher::factory()->create([
        'conduct_setting_id' => $conduct_setting_sem2->id,
        'is_homeroom_teacher' => true,
        'is_active' => true,   
    ]);

    $cst_sem2 = ConductSettingTeacher::factory(5)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'is_homeroom_teacher' => false,
            'is_active' => true,
        ]
    ))->create();
    
    // Sem 1 Conduct Records
    $cr_sem1_student0 = ConductRecord::factory(6)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_homeroom_sem1->id,
            'student_id' => $students[0]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[0]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[1]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[2]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[3]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[4]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();

    $cr_sem1_student1 = ConductRecord::factory(6)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_homeroom_sem1->id,
            'student_id' => $students[1]->id,
            'marks' => 75,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[0]->id,
            'student_id' => $students[1]->id,
            'marks' => 70,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[1]->id,
            'student_id' => $students[1]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[2]->id,
            'student_id' => $students[1]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[3]->id,
            'student_id' => $students[1]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[4]->id,
            'student_id' => $students[1]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();
    
    $cr_sem1_student2 = ConductRecord::factory(6)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_homeroom_sem1->id,
            'student_id' => $students[2]->id,
            'marks' => 50,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[0]->id,
            'student_id' => $students[2]->id,
            'marks' => 40,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[1]->id,
            'student_id' => $students[2]->id,
            'marks' => 60,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[2]->id,
            'student_id' => $students[2]->id,
            'marks' => 40,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[3]->id,
            'student_id' => $students[2]->id,
            'marks' => 60,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[4]->id,
            'student_id' => $students[2]->id,
            'marks' => 40,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();

    $cr_sem1_student3 = ConductRecord::factory(6)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_homeroom_sem1->id,
            'student_id' => $students[3]->id,
            'marks' => 83,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[0]->id,
            'student_id' => $students[3]->id,
            'marks' => 81,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[1]->id,
            'student_id' => $students[3]->id,
            'marks' => 85,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[2]->id,
            'student_id' => $students[3]->id,
            'marks' => 81,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[3]->id,
            'student_id' => $students[3]->id,
            'marks' => 85,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[4]->id,
            'student_id' => $students[3]->id,
            'marks' => 83,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();    
   
    $cr_sem1_student4 = ConductRecord::factory(6)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_homeroom_sem1->id,
            'student_id' => $students[4]->id,
            'marks' => 20,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[0]->id,
            'student_id' => $students[4]->id,
            'marks' => 40,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[1]->id,
            'student_id' => $students[4]->id,
            'marks' => 20,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[2]->id,
            'student_id' => $students[4]->id,
            'marks' => 40,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[3]->id,
            'student_id' => $students[4]->id,
            'marks' => 20,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem1[4]->id,
            'student_id' => $students[4]->id,
            'marks' => 30,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();    

    // Sem 2 Conduct Records
    $cr_sem2_student0 = ConductRecord::factory(6)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_homeroom_sem2->id,
            'student_id' => $students[0]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[0]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[1]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem1->id,
            'conduct_setting_teacher_id' => $cst_sem2[2]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[3]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[4]->id,
            'student_id' => $students[0]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();

    $cr_sem2_student1 = ConductRecord::factory(6)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_homeroom_sem2->id,
            'student_id' => $students[1]->id,
            'marks' => 75,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[0]->id,
            'student_id' => $students[1]->id,
            'marks' => 70,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[1]->id,
            'student_id' => $students[1]->id,
            'marks' => 90,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[2]->id,
            'student_id' => $students[1]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[3]->id,
            'student_id' => $students[1]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[4]->id,
            'student_id' => $students[1]->id,
            'marks' => 80,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();
    
    $cr_sem2_student2 = ConductRecord::factory(6)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_homeroom_sem2->id,
            'student_id' => $students[2]->id,
            'marks' => 50,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[0]->id,
            'student_id' => $students[2]->id,
            'marks' => 40,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[1]->id,
            'student_id' => $students[2]->id,
            'marks' => 60,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[2]->id,
            'student_id' => $students[2]->id,
            'marks' => 40,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[3]->id,
            'student_id' => $students[2]->id,
            'marks' => 60,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[4]->id,
            'student_id' => $students[2]->id,
            'marks' => 40,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();

    $cr_sem2_student3 = ConductRecord::factory(6)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_homeroom_sem2->id,
            'student_id' => $students[3]->id,
            'marks' => 83,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[0]->id,
            'student_id' => $students[3]->id,
            'marks' => 81,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[1]->id,
            'student_id' => $students[3]->id,
            'marks' => 85,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[2]->id,
            'student_id' => $students[3]->id,
            'marks' => 81,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[3]->id,
            'student_id' => $students[3]->id,
            'marks' => 85,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[4]->id,
            'student_id' => $students[3]->id,
            'marks' => 83,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();    
   
    $cr_sem2_student4 = ConductRecord::factory(6)->state(new Sequence(
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_homeroom_sem2->id,
            'student_id' => $students[4]->id,
            'marks' => 20,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[0]->id,
            'student_id' => $students[4]->id,
            'marks' => 40,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[1]->id,
            'student_id' => $students[4]->id,
            'marks' => 20,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[2]->id,
            'student_id' => $students[4]->id,
            'marks' => 40,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[3]->id,
            'student_id' => $students[4]->id,
            'marks' => 20,
            'status' => ConductRecordStatus::POSTED->value,
        ],
        [
            'conduct_setting_id' => $conduct_setting_sem2->id,
            'conduct_setting_teacher_id' => $cst_sem2[4]->id,
            'student_id' => $students[4]->id,
            'marks' => 30,
            'status' => ConductRecordStatus::POSTED->value,
        ],
    ))->create();    

    // Flushing all auto generated grading schemes from conduct record factory
    GradingScheme::whereNotIn('id', [$exam_grading_scheme->id, $conduct_grading_scheme->id])->delete();

    // Reward Punishment
    $reward_punishments = RewardPunishment::factory(3)->state(new Sequence(
        ['name->en' => 'Cleaning'],
        ['name->en' => 'Charity'],
        ['name->en' => 'Stealing'],
    ))->create();

    $merit_demerit = MeritDemeritSetting::factory(3)->state(new Sequence(
        ['name->en' => '1 Merit'],
        ['name->en' => '2 Merit'],
        ['name->en' => '1 Demerit']
    ))->create();

    MeritDemeritRewardPunishment::factory(4)->state(new Sequence(
        [
            'merit_demerit_id' => $merit_demerit[0]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
        ],
        [
            'merit_demerit_id' => $merit_demerit[1]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
        ],
        [
            'merit_demerit_id' => $merit_demerit[1]->id,
            'reward_punishment_id' => $reward_punishments[1]->id,
        ],
        [
            'merit_demerit_id' => $merit_demerit[2]->id,
            'reward_punishment_id' => $reward_punishments[2]->id,
        ]
    ))->create();

    // Reward Punishment Sem 1 Records
    $reward_punishment_records = RewardPunishmentRecord::factory(4)->state(new Sequence(
        [
            'date' => '2025-03-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'average_exam_marks' => 0.2,
            'conduct_marks' => 0.5,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
            'display_in_report_card' => true
        ],
        [
            'date' => '2025-04-01',
            'student_id' => $students[0]->id,
            'average_exam_marks' => 0.2,
            'conduct_marks' => 0.5,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
            'display_in_report_card' => true
        ],
        [
            'date' => '2025-04-05',
            'student_id' => $students[0]->id,
            'average_exam_marks' => 0.2,
            'conduct_marks' => 0.25,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
            'display_in_report_card' => true
        ],
        [
            'date' => '2025-04-01',
            'student_id' => $students[4]->id,
            'reward_punishment_id' => $reward_punishments[2]->id,
            'average_exam_marks' => -0.5,
            'conduct_marks' => -2,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
            'display_in_report_card' => true
        ]
    ))->create();

    // Reward Punishment Sem 2 Records
    $reward_punishment_records = RewardPunishmentRecord::factory(4)->state(new Sequence(
        [
            'date' => '2025-10-01',
            'student_id' => $students[0]->id,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'average_exam_marks' => 0.2,
            'conduct_marks' => 0.5,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
            'display_in_report_card' => true
        ],
        [
            'date' => '2025-10-01',
            'student_id' => $students[0]->id,
            'average_exam_marks' => 0.2,
            'conduct_marks' => 0.25,
            'reward_punishment_id' => $reward_punishments[0]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
            'display_in_report_card' => true
        ],
        [
            'date' => '2025-11-05',
            'student_id' => $students[0]->id,
            'average_exam_marks' => 0.2,
            'conduct_marks' => 0.25,
            'reward_punishment_id' => $reward_punishments[1]->id,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
            'display_in_report_card' => true
        ],
        [
            'date' => '2025-10-01',
            'student_id' => $students[4]->id,
            'reward_punishment_id' => $reward_punishments[2]->id,
            'average_exam_marks' => -0.5,
            'conduct_marks' => -1,
            'status' => RewardPunishmentRecordStatus::POSTED->value,
            'display_in_report_card' => true
        ]
    ))->create();    


    $leadership_position_1 = LeadershipPosition::factory()->create([
        'name->en' => 'Class Leader',
        'is_active' => true,
    ]);

    $leadership_position_2 = LeadershipPosition::factory()->create([
        'name->en' => 'Prefect',
        'is_active' => true,
    ]);

    LeadershipPositionRecord::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class_sem1class1->id,
            'semester_setting_id' => $semester_setting_1->id,
            'leadership_position_id' => $leadership_position_1->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_class_id' => $semester_class_sem2class1->id,
            'semester_setting_id' => $semester_setting_2->id,
            'leadership_position_id' => $leadership_position_1->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_class_id' => $semester_class_sem1class1->id,
            'semester_setting_id' => $semester_setting_1->id,
            'leadership_position_id' => $leadership_position_2->id,
            'student_id' => $students[1]->id,
        ],
        [
            'semester_class_id' => $semester_class_sem2class1->id,
            'semester_setting_id' => $semester_setting_2->id,
            'leadership_position_id' => $leadership_position_2->id,
            'student_id' => $students[1]->id,
        ]
    ))->create();

    $leave_application_type_1 = LeaveApplicationType::factory()->create(['name->en' => 'MC',]);
    $leave_application_type_2 = LeaveApplicationType::factory()->create(['name->en' => 'Public Holiday']);

    $leave_applications = LeaveApplication::factory(2)->state(new Sequence(      
        [
            'leave_applicable_id' => $students[0],
            'leave_application_type_id' => $leave_application_type_2->id,
            'average_point_deduction' => 0.00
        ],
        [
            'leave_applicable_id' => $students[4],
            'leave_application_type_id' => $leave_application_type_1->id,
            'average_point_deduction' => 0.01
        ]
    ))->create();

    $leave_application_period = LeaveApplicationPeriod::factory(8)->state(new Sequence(
        [
            'leave_application_id' => $leave_applications[0]->id,
            'date' => '2025-03-01',
        ],
    ))->create();

    // Creating Public Holiday leave for Student 0
    PeriodAttendance::factory(8)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'date' => '2025-03-03',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => $leave_applications[0]->id,
        ],
    ))->create();

    // Creating ABSENT/LATE data for student 4
    PeriodAttendance::factory(8)->state(new Sequence(
        [
            'student_id' => $students[4]->id,
            'date' => '2025-02-01',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => null,
        ],
    ))->create();

    PeriodAttendance::factory(4)->state(new Sequence(
        [
            'student_id' => $students[4]->id,
            'date' => '2025-03-01',
            'status' => PeriodAttendanceStatus::LATE->value,
            'leave_application_id' => null,
        ],
    ))->create();

    PeriodAttendance::factory(8)->state(new Sequence(
        [
            'student_id' => $students[4]->id,
            'date' => '2025-03-03',
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => $leave_applications[1]->id,
        ],
    ))->create();

   // Flushing auto created subjects from timeslot factory
   Subject::whereNotIn('id', [
        $subjects[0]->id,
        $subjects[1]->id,
        $subjects[2]->id,
        $subjects[3]->id,
        $subjects[4]->id,
    ])
   ->delete();
    
    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-2.json')), true),
    ]);

    // setup student's grading framework
    $sgf0 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[0])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf1 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[1])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf2 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[2])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf3 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[3])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    $sgf4 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($students[4])
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), $this->academic_year)
        ->getStudentGradingFramework();

    // SEM1EXAM STUDENT 0 
    $rs_sem1_student0 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf0->id)->first();

    $rs_sub1_sem1_student0 = ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem1_student0 = ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem1_student0 = ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_sem1_student0 = ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_sem1_student0 = ResultSourceSubject::where('result_source_id', $rs_sem1_student0->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_sem1_student0->components()->update([
        'actual_score' => 85
    ]);
    $rs_sub2_sem1_student0->components()->update([
        'actual_score' => 80
    ]);
    $rs_sub3_sem1_student0->components()->update([
        'actual_score' => 83,
    ]);
    $rs_sub4_sem1_student0->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 100
    ]);
    $rs_sub4_sem1_student0->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 80
    ]);
    $rs_sub5_sem1_student0->update([
        'actual_score_grade' => 'A'
    ]);
    
    // SEM1EXAM STUDENT 1
    $rs_sem1_student1 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf1->id)->first();

    $rs_sub1_sem1_student1 = ResultSourceSubject::where('result_source_id', $rs_sem1_student1->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem1_student1 = ResultSourceSubject::where('result_source_id', $rs_sem1_student1->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem1_student1 = ResultSourceSubject::where('result_source_id', $rs_sem1_student1->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_sem1_student1 = ResultSourceSubject::where('result_source_id', $rs_sem1_student1->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_sem1_student1 = ResultSourceSubject::where('result_source_id', $rs_sem1_student1->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_sem1_student1->components()->update([
        'actual_score' => 70
    ]);
    $rs_sub2_sem1_student1->components()->update([
        'actual_score' => 70
    ]);
    $rs_sub3_sem1_student1->components()->update([
        'actual_score' => 65,
    ]);
    $rs_sub4_sem1_student1->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 80
    ]);
    $rs_sub4_sem1_student1->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 75
    ]);
    $rs_sub5_sem1_student1->update([
        'actual_score_grade' => 'B'
    ]);

    // SEM1EXAM STUDENT 2
    $rs_sem1_student2 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf2->id)->first();

    $rs_sub1_sem1_student2 = ResultSourceSubject::where('result_source_id', $rs_sem1_student2->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem1_student2 = ResultSourceSubject::where('result_source_id', $rs_sem1_student2->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem1_student2 = ResultSourceSubject::where('result_source_id', $rs_sem1_student2->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_sem1_student2 = ResultSourceSubject::where('result_source_id', $rs_sem1_student2->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_sem1_student2 = ResultSourceSubject::where('result_source_id', $rs_sem1_student2->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_sem1_student2->components()->update([
        'actual_score' => 60
    ]);
    $rs_sub2_sem1_student2->components()->update([
        'actual_score' => 65
    ]);
    $rs_sub3_sem1_student2->components()->update([
        'actual_score' => 64,
    ]);
    $rs_sub4_sem1_student2->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 60
    ]);
    $rs_sub4_sem1_student2->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 68
    ]);
    $rs_sub5_sem1_student2->update([
        'actual_score_grade' => 'C'
    ]);

    // SEM1EXAM STUDENT 3
    $rs_sem1_student3 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf3->id)->first();

    $rs_sub1_sem1_student3 = ResultSourceSubject::where('result_source_id', $rs_sem1_student3->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem1_student3 = ResultSourceSubject::where('result_source_id', $rs_sem1_student3->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem1_student3 = ResultSourceSubject::where('result_source_id', $rs_sem1_student3->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_sem1_student3 = ResultSourceSubject::where('result_source_id', $rs_sem1_student3->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_sem1_student3 = ResultSourceSubject::where('result_source_id', $rs_sem1_student3->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_sem1_student3->components()->update([
        'actual_score' => 90
    ]);
    $rs_sub2_sem1_student3->components()->update([
        'actual_score' => 95
    ]);
    $rs_sub3_sem1_student3->components()->update([
        'actual_score' => 92,
    ]);
    $rs_sub4_sem1_student3->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 100,
    ]);
    $rs_sub4_sem1_student3->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 98
    ]);
    $rs_sub5_sem1_student3->update([
        'actual_score_grade' => 'A'
    ]);

    // SEM1EXAM STUDENT 4
    $rs_sem1_student4 = ResultSource::where('code', 'SEM1EXAM')->where('student_grading_framework_id', $sgf4->id)->first();

    $rs_sub1_sem1_student4 = ResultSourceSubject::where('result_source_id', $rs_sem1_student4->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem1_student4 = ResultSourceSubject::where('result_source_id', $rs_sem1_student4->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem1_student4 = ResultSourceSubject::where('result_source_id', $rs_sem1_student4->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_sem1_student4 = ResultSourceSubject::where('result_source_id', $rs_sem1_student4->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_sem1_student4 = ResultSourceSubject::where('result_source_id', $rs_sem1_student4->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_sem1_student4->components()->update([
        'actual_score' => 50
    ]);
    $rs_sub2_sem1_student4->components()->update([
        'actual_score' => 45
    ]);
    $rs_sub3_sem1_student4->components()->update([
        'actual_score' => 60,
    ]);
    $rs_sub4_sem1_student4->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 80,
    ]);
    $rs_sub4_sem1_student4->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 45
    ]);
    $rs_sub5_sem1_student4->update([
        'actual_score_grade' => 'C'
    ]);

    // PROCESS SEM1RESULT
    $sem1_header = ResultsPostingHeader::factory()->create([
        'code' => 'SEM1EXAM',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [
            $students[0]->id,
            $students[1]->id,
            $students[2]->id,
            $students[3]->id,
            $students[4]->id
        ],
        'report_card_output_code' => 'SEM1RESULT',
        'grade_id' => $grade1->id,
        'semester_setting_id' => $semester_setting_1->id,
    ]);

    Artisan::call('results-posting:process', ['--actual' => 1]);

    $this->assertDatabaseHas(ResultsPostingHeader::class, [
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'id' => $sem1_header->id,
        'errors' => null
    ]);

    $rco_sem1_student0 = $sgf0->outputs()->where('code', 'SEM1RESULT')->first();

    $chinese_component = $rco_sem1_student0->components->where('code', 'CHINESE')->first();
    $bm_component = $rco_sem1_student0->components->where('code', 'BM')->first();
    $en_component = $rco_sem1_student0->components->where('code', 'EN')->first();
    $math_component = $rco_sem1_student0->components->where('code', 'MATH')->first();
    $en_oral_component = $rco_sem1_student0->components->where('code', 'EN_ORAL')->first();
    $gt_component = $rco_sem1_student0->components->where('code', 'GT')->first();
    $ga_component = $rco_sem1_student0->components->where('code', 'GA')->first();
    $ma_component = $rco_sem1_student0->components->where('code', 'MA')->first();
    $ms_component = $rco_sem1_student0->components->where('code', 'MS')->first();
    $sys_net_avg_component = $rco_sem1_student0->components->where('code', 'SYS_NET_AVG')->first();
    $conduct_component = $rco_sem1_student0->components->where('code', 'CONDUCT')->first();
    $schooldays_component = $rco_sem1_student0->components->where('code', 'SCHOOLDAYS')->first();
    $leadership_component = $rco_sem1_student0->components->where('code', 'LEADERSHIP_POSITION')->first();
    $society_component = $rco_sem1_student0->components->where('code', 'SOCIETY')->first();
    $merit_component = $rco_sem1_student0->components->where('code', 'MERIT')->first();
    $exceptional_component = $rco_sem1_student0->components->where('code', 'EXCEPTIONAL')->first();

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $chinese_component->id,
        'report_card_output_component_code' => $chinese_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $chinese_component->subject_id,
        'grading_scheme_id' => $chinese_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'calculate_rank' => true,
        'total' => 85,
        'label' => 'A',
        'total_grade->display_as_name' => 'A',
        'total_grade->name' => 'A',
        'total_grade->extra_marks' => 0,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $bm_component->id,
        'report_card_output_component_code' => $bm_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $bm_component->subject_id,
        'grading_scheme_id' => $bm_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 80,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $en_component->id,
        'report_card_output_component_code' => $en_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $en_component->subject_id,
        'grading_scheme_id' => $en_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 83,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $math_component->id,
        'report_card_output_component_code' => $math_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $math_component->subject_id,
        'grading_scheme_id' => $math_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 86,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $en_oral_component->id,
        'report_card_output_component_code' => $en_oral_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $en_oral_component->subject_id,
        'grading_scheme_id' => $en_oral_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => null,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $gt_component->id,
        'report_card_output_component_code' => $gt_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $gt_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 499.00,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $ga_component->id,
        'report_card_output_component_code' => $ga_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $ga_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'grading_scheme_id' => $ga_component->grading_scheme_id,
        'total' => 83.17,
        'label' => 'A',
    ]);

    // Conduct Record = 0.6, Avg Marks = 0.2*3 = 0.6 , Competition = 2
    // Total = 0.6 + 0.6 + 2 = 3.2
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $ma_component->id,
        'report_card_output_component_code' => $ma_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $ma_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 3.20,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $ms_component->id,
        'report_card_output_component_code' => $ms_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $ms_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 0,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $sys_net_avg_component->id,
        'report_card_output_component_code' => $sys_net_avg_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $sys_net_avg_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 86.37,
        'label' => 'A',
        'class_rank' => 2,
        'class_population' => 5,
    ]);

    // Homeroom = 80
    // Average of other teachers = 90
    // (0.33 * 80) + (0.67 * 90) = 86.7
    // reward_punishments = 1.25
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $conduct_component->id,
        'report_card_output_component_code' => $conduct_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $conduct_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 87.95,
        'label' => 'Great',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $schooldays_component->id,
        'report_card_output_component_code' => $schooldays_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $schooldays_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 60,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $leadership_component->id,
        'report_card_output_component_code' => $leadership_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $leadership_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => 'Class Leader',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $society_component->id,
        'report_card_output_component_code' => $society_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $society_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => 'SCOUT',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $merit_component->id,
        'report_card_output_component_code' => $merit_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $merit_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => "2 Merit (Charity); 1 Merit, 2 Merit (Cleaning); 1 Merit, 2 Merit (Cleaning)",
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem1_header->id,
        'report_card_output_component_id' => $exceptional_component->id,
        'report_card_output_component_code' => $exceptional_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $exceptional_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => 'Football Match; Football Match',
    ]);

    // SEM2EXAM RESULTS
    // SEM2EXAM STUDENT 0
    $rs_sem2_student0 = ResultSource::where('code', 'SEM2EXAM')->where('student_grading_framework_id', $sgf0->id)->first();

    $rs_sub1_sem2_student0 = ResultSourceSubject::where('result_source_id', $rs_sem2_student0->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem2_student0 = ResultSourceSubject::where('result_source_id', $rs_sem2_student0->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem2_student0 = ResultSourceSubject::where('result_source_id', $rs_sem2_student0->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_sem2_student0 = ResultSourceSubject::where('result_source_id', $rs_sem2_student0->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_sem2_student0 = ResultSourceSubject::where('result_source_id', $rs_sem2_student0->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_sem2_student0->components()->update([
        'actual_score' => 84
    ]);
    $rs_sub2_sem2_student0->components()->update([
        'actual_score' => 82
    ]);
    $rs_sub3_sem2_student0->components()->update([
        'actual_score' => 88,
    ]);
    $rs_sub4_sem2_student0->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 100,
    ]);
    $rs_sub4_sem2_student0->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 88
    ]);
    $rs_sub5_sem2_student0->update([
        'actual_score_grade' => 'A'
    ]);

    // SEM2EXAM STUDENT 1
    $rs_sem2_student1 = ResultSource::where('code', 'SEM2EXAM')->where('student_grading_framework_id', $sgf1->id)->first();

    $rs_sub1_sem2_student1 = ResultSourceSubject::where('result_source_id', $rs_sem2_student1->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem2_student1 = ResultSourceSubject::where('result_source_id', $rs_sem2_student1->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem2_student1 = ResultSourceSubject::where('result_source_id', $rs_sem2_student1->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_sem2_student1 = ResultSourceSubject::where('result_source_id', $rs_sem2_student1->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_sem2_student1 = ResultSourceSubject::where('result_source_id', $rs_sem2_student1->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_sem2_student1->components()->update([
        'actual_score' => 72
    ]);
    $rs_sub2_sem2_student1->components()->update([
        'actual_score' => 72
    ]);
    $rs_sub3_sem2_student1->components()->update([
        'actual_score' => 68,
    ]);
    $rs_sub4_sem2_student1->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 73
    ]);
    $rs_sub4_sem2_student1->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 74
    ]);
    $rs_sub5_sem2_student1->update([
        'actual_score_grade' => 'B'
    ]);

    // SEM2EXAM STUDENT 2
    $rs_sem2_student2 = ResultSource::where('code', 'SEM2EXAM')->where('student_grading_framework_id', $sgf2->id)->first();

    $rs_sub1_sem2_student2 = ResultSourceSubject::where('result_source_id', $rs_sem2_student2->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem2_student2 = ResultSourceSubject::where('result_source_id', $rs_sem2_student2->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem2_student2 = ResultSourceSubject::where('result_source_id', $rs_sem2_student2->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_sem2_student2 = ResultSourceSubject::where('result_source_id', $rs_sem2_student2->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_sem2_student2 = ResultSourceSubject::where('result_source_id', $rs_sem2_student2->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_sem2_student2->components()->update([
        'actual_score' => 63
    ]);
    $rs_sub2_sem2_student2->components()->update([
        'actual_score' => 68
    ]);
    $rs_sub3_sem2_student2->components()->update([
        'actual_score' => 67,
    ]);
    $rs_sub4_sem2_student2->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 65
    ]);
    $rs_sub4_sem2_student2->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 69
    ]);
    $rs_sub5_sem2_student2->update([
        'actual_score_grade' => 'C'
    ]);

    // SEM2EXAM STUDENT 3
    $rs_sem2_student3 = ResultSource::where('code', 'SEM2EXAM')->where('student_grading_framework_id', $sgf3->id)->first();

    $rs_sub1_sem2_student3 = ResultSourceSubject::where('result_source_id', $rs_sem2_student3->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem2_student3 = ResultSourceSubject::where('result_source_id', $rs_sem2_student3->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem2_student3 = ResultSourceSubject::where('result_source_id', $rs_sem2_student3->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_sem2_student3 = ResultSourceSubject::where('result_source_id', $rs_sem2_student3->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_sem2_student3 = ResultSourceSubject::where('result_source_id', $rs_sem2_student3->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_sem2_student3->components()->update([
        'actual_score' => 90
    ]);
    $rs_sub2_sem2_student3->components()->update([
        'actual_score' => 92
    ]);
    $rs_sub3_sem2_student3->components()->update([
        'actual_score' => 94,
    ]);
    $rs_sub4_sem2_student3->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 100,
    ]);
    $rs_sub4_sem2_student3->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 96
    ]);
    $rs_sub5_sem2_student3->update([
        'actual_score_grade' => 'A'
    ]);

    // SEM2EXAM STUDENT 4
    $rs_sem2_student4 = ResultSource::where('code', 'SEM2EXAM')->where('student_grading_framework_id', $sgf4->id)->first();

    $rs_sub1_sem2_student4 = ResultSourceSubject::where('result_source_id', $rs_sem2_student4->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_sem2_student4 = ResultSourceSubject::where('result_source_id', $rs_sem2_student4->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_sem2_student4 = ResultSourceSubject::where('result_source_id', $rs_sem2_student4->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_sem2_student4 = ResultSourceSubject::where('result_source_id', $rs_sem2_student4->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_sem2_student4 = ResultSourceSubject::where('result_source_id', $rs_sem2_student4->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_sem2_student4->components()->update([
        'actual_score' => 41
    ]);
    $rs_sub2_sem2_student4->components()->update([
        'actual_score' => 46
    ]);
    $rs_sub3_sem2_student4->components()->update([
        'actual_score' => 55,
    ]);
    $rs_sub4_sem2_student4->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 60,
    ]);
    $rs_sub4_sem2_student4->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 55
    ]);
    $rs_sub5_sem2_student4->update([
        'actual_score_grade' => 'C'
    ]);

    $sem2_header = ResultsPostingHeader::factory()->create([
        'code' => 'SEM2EXAM',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [
            $students[0]->id,
            $students[1]->id,
            $students[2]->id,
            $students[3]->id,
            $students[4]->id
        ],
        'report_card_output_code' => 'SEM2RESULT',
        'grade_id' => $grade1->id,
        'semester_setting_id' => $semester_setting_2->id,
    ]);

    $d = Artisan::call('results-posting:process', ['--actual' => 1]);

    $this->assertDatabaseHas(ResultsPostingHeader::class, [
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'id' => $sem2_header->id,
        'errors' => null
    ]);

    $rco_sem2_student0 = $sgf0->outputs()->where('code', 'SEM2RESULT')->first();

    $chinese_component = $rco_sem2_student0->components->where('code', 'CHINESE')->first();
    $bm_component = $rco_sem2_student0->components->where('code', 'BM')->first();
    $en_component = $rco_sem2_student0->components->where('code', 'EN')->first();
    $math_component = $rco_sem2_student0->components->where('code', 'MATH')->first();
    $en_oral_component = $rco_sem2_student0->components->where('code', 'EN_ORAL')->first();
    $gt_component = $rco_sem2_student0->components->where('code', 'GT')->first();
    $ga_component = $rco_sem2_student0->components->where('code', 'GA')->first();
    $ma_component = $rco_sem2_student0->components->where('code', 'MA')->first();
    $ms_component = $rco_sem2_student0->components->where('code', 'MS')->first();
    $sys_net_avg_component = $rco_sem2_student0->components->where('code', 'SYS_NET_AVG')->first();
    $conduct_component = $rco_sem2_student0->components->where('code', 'CONDUCT')->first();
    $schooldays_component = $rco_sem2_student0->components->where('code', 'SCHOOLDAYS')->first();
    $leadership_component = $rco_sem2_student0->components->where('code', 'LEADERSHIP_POSITION')->first();
    $society_component = $rco_sem2_student0->components->where('code', 'SOCIETY')->first();
    $merit_component = $rco_sem2_student0->components->where('code', 'MERIT')->first();
    $exceptional_component = $rco_sem2_student0->components->where('code', 'EXCEPTIONAL')->first();

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $chinese_component->id,
        'report_card_output_component_code' => $chinese_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $chinese_component->subject_id,
        'grading_scheme_id' => $chinese_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'calculate_rank' => true,
        'total' => 84,
        'label' => 'A',
        'total_grade->display_as_name' => 'A',
        'total_grade->name' => 'A',
        'total_grade->extra_marks' => 0,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $bm_component->id,
        'report_card_output_component_code' => $bm_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $bm_component->subject_id,
        'grading_scheme_id' => $bm_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 82,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $en_component->id,
        'report_card_output_component_code' => $en_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $en_component->subject_id,
        'grading_scheme_id' => $en_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 88,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $math_component->id,
        'report_card_output_component_code' => $math_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $math_component->subject_id,
        'grading_scheme_id' => $math_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 91.6,
        'label' => 'A+',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $en_oral_component->id,
        'report_card_output_component_code' => $en_oral_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $en_oral_component->subject_id,
        'grading_scheme_id' => $en_oral_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => null,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $gt_component->id,
        'report_card_output_component_code' => $gt_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $gt_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 511.60,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $ga_component->id,
        'report_card_output_component_code' => $ga_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $ga_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'grading_scheme_id' => $ga_component->grading_scheme_id,
        'total' => 85.27,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $ma_component->id,
        'report_card_output_component_code' => $ma_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $ma_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 2.20,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $ms_component->id,
        'report_card_output_component_code' => $ms_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $ms_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 0,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $sys_net_avg_component->id,
        'report_card_output_component_code' => $sys_net_avg_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $sys_net_avg_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 87.47,
        'label' => 'A',
        'class_rank' => 2,
        'class_population' => 5,
    ]);

    // Homeroom = 80
    // Average of other teachers = 90
    // (0.33 * 80) + (0.67 * 90) = 86.7
    // reward punishment conduct mark = +1
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $conduct_component->id,
        'report_card_output_component_code' => $conduct_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $conduct_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 87.7,
        'label' => 'Great',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $schooldays_component->id,
        'report_card_output_component_code' => $schooldays_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $schooldays_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 70,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $leadership_component->id,
        'report_card_output_component_code' => $leadership_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $leadership_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => 'Class Leader',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $society_component->id,
        'report_card_output_component_code' => $society_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $society_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => 'SCOUT',
    ]);


    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $merit_component->id,
        'report_card_output_component_code' => $merit_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $merit_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => "2 Merit (Charity); 1 Merit, 2 Merit (Cleaning); 1 Merit, 2 Merit (Cleaning)",
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $sem2_header->id,
        'report_card_output_component_id' => $exceptional_component->id,
        'report_card_output_component_code' => $exceptional_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $exceptional_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => 'Swimming competition',
    ]);

    // FINALEXAM RESULTS - ALL ZERO
    // FINALEXAM STUDENT 0
    $rs_final_student0 = ResultSource::where('code', 'FINALEXAM')->where('student_grading_framework_id', $sgf0->id)->first();

    $rs_sub1_final_student0 = ResultSourceSubject::where('result_source_id', $rs_final_student0->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_final_student0 = ResultSourceSubject::where('result_source_id', $rs_final_student0->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_final_student0 = ResultSourceSubject::where('result_source_id', $rs_final_student0->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_final_student0 = ResultSourceSubject::where('result_source_id', $rs_final_student0->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_final_student0 = ResultSourceSubject::where('result_source_id', $rs_final_student0->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_final_student0->components()->update([
        'actual_score' => 0
    ]);
    $rs_sub2_final_student0->components()->update([
        'actual_score' => 0
    ]);
    $rs_sub3_final_student0->components()->update([
        'actual_score' => 0,
    ]);
    $rs_sub4_final_student0->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 0,
    ]);
    $rs_sub4_final_student0->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 0
    ]);
    $rs_sub5_final_student0->update([
        'actual_score_grade' => '-'
    ]);

    // FINALEXAM STUDENT 1
    $rs_final_student1 = ResultSource::where('code', 'FINALEXAM')->where('student_grading_framework_id', $sgf1->id)->first();

    $rs_sub1_final_student1 = ResultSourceSubject::where('result_source_id', $rs_final_student1->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_final_student1 = ResultSourceSubject::where('result_source_id', $rs_final_student1->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_final_student1 = ResultSourceSubject::where('result_source_id', $rs_final_student1->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_final_student1 = ResultSourceSubject::where('result_source_id', $rs_final_student1->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_final_student1 = ResultSourceSubject::where('result_source_id', $rs_final_student1->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_final_student1->components()->update([
        'actual_score' => 0
    ]);
    $rs_sub2_final_student1->components()->update([
        'actual_score' => 0
    ]);
    $rs_sub3_final_student1->components()->update([
        'actual_score' => 0,
    ]);
    $rs_sub4_final_student1->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 0,
    ]);
    $rs_sub4_final_student1->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 0
    ]);
    $rs_sub5_final_student1->update([
        'actual_score_grade' => '-'
    ]);

    // FINALEXAM STUDENT 2
    $rs_final_student2 = ResultSource::where('code', 'FINALEXAM')->where('student_grading_framework_id', $sgf2->id)->first();

    $rs_sub1_final_student2 = ResultSourceSubject::where('result_source_id', $rs_final_student2->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_final_student2 = ResultSourceSubject::where('result_source_id', $rs_final_student2->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_final_student2 = ResultSourceSubject::where('result_source_id', $rs_final_student2->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_final_student2 = ResultSourceSubject::where('result_source_id', $rs_final_student2->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_final_student2 = ResultSourceSubject::where('result_source_id', $rs_final_student2->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_final_student2->components()->update([
        'actual_score' => 0
    ]);
    $rs_sub2_final_student2->components()->update([
        'actual_score' => 0
    ]);
    $rs_sub3_final_student2->components()->update([
        'actual_score' => 0,
    ]);
    $rs_sub4_final_student2->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 0,
    ]);
    $rs_sub4_final_student2->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 0
    ]);
    $rs_sub5_final_student2->update([
        'actual_score_grade' => '-'
    ]);

    // FINALEXAM STUDENT 3
    $rs_final_student3 = ResultSource::where('code', 'FINALEXAM')->where('student_grading_framework_id', $sgf3->id)->first();

    $rs_sub1_final_student3 = ResultSourceSubject::where('result_source_id', $rs_final_student3->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_final_student3 = ResultSourceSubject::where('result_source_id', $rs_final_student3->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_final_student3 = ResultSourceSubject::where('result_source_id', $rs_final_student3->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_final_student3 = ResultSourceSubject::where('result_source_id', $rs_final_student3->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_final_student3 = ResultSourceSubject::where('result_source_id', $rs_final_student3->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_final_student3->components()->update([
        'actual_score' => 0
    ]);
    $rs_sub2_final_student3->components()->update([
        'actual_score' => 0
    ]);
    $rs_sub3_final_student3->components()->update([
        'actual_score' => 0,
    ]);
    $rs_sub4_final_student3->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 0,
    ]);
    $rs_sub4_final_student3->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 0
    ]);
    $rs_sub5_final_student3->update([
        'actual_score_grade' => '-'
    ]);

    // FINALEXAM STUDENT 4
    $rs_final_student4 = ResultSource::where('code', 'FINALEXAM')->where('student_grading_framework_id', $sgf4->id)->first();

    $rs_sub1_final_student4 = ResultSourceSubject::where('result_source_id', $rs_final_student4->id)
        ->where('subject_id', $subjects->where('code', 'CHINESE')->first()->id)
        ->first();
    $rs_sub2_final_student4 = ResultSourceSubject::where('result_source_id', $rs_final_student4->id)
        ->where('subject_id', $subjects->where('code', 'BM')->first()->id)
        ->first();
    $rs_sub3_final_student4 = ResultSourceSubject::where('result_source_id', $rs_final_student4->id)
        ->where('subject_id', $subjects->where('code', 'EN')->first()->id)
        ->first();
    $rs_sub4_final_student4 = ResultSourceSubject::where('result_source_id', $rs_final_student4->id)
        ->where('subject_id', $subjects->where('code', 'MATH')->first()->id)
        ->first();
    $rs_sub5_final_student4 = ResultSourceSubject::where('result_source_id', $rs_final_student4->id)
        ->where('subject_id', $subjects->where('code', 'EN_ORAL')->first()->id)
        ->first();

    $rs_sub1_final_student4->components()->update([
        'actual_score' => 0
    ]);
    $rs_sub2_final_student4->components()->update([
        'actual_score' => 0
    ]);
    $rs_sub3_final_student4->components()->update([
        'actual_score' => 0,
    ]);
    $rs_sub4_final_student4->components()->where('code', 'HOMEWORK')->first()->update([
        'actual_score' => 0,
    ]);
    $rs_sub4_final_student4->components()->where('code', 'FINAL')->first()->update([
        'actual_score' => 0
    ]);
    $rs_sub5_final_student4->update([
        'actual_score_grade' => '-'
    ]);

    $final_header = ResultsPostingHeader::factory()->create([
        'code' => 'FINALEXAM',
        'status' => ResultsPostingHeader::STATUS_PENDING,
        'student_ids' => [
            $students[0]->id,
            $students[1]->id,
            $students[2]->id,
            $students[3]->id,
            $students[4]->id
        ],
        'report_card_output_code' => 'FINALRESULT',
        'grade_id' => $grade1->id,
        'semester_setting_id' => $semester_setting_2->id,
    ]);

    $d = Artisan::call('results-posting:process', ['--actual' => 1]);

    $this->assertDatabaseHas(ResultsPostingHeader::class, [
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'id' => $final_header->id,
        'errors' => null
    ]);

    $rco_final_student0 = $sgf0->outputs()->where('code', 'FINALRESULT')->first();

    $chinese_component = $rco_final_student0->components->where('code', 'CHINESE')->first();
    $bm_component = $rco_final_student0->components->where('code', 'BM')->first();
    $en_component = $rco_final_student0->components->where('code', 'EN')->first();
    $math_component = $rco_final_student0->components->where('code', 'MATH')->first();
    $en_oral_component = $rco_final_student0->components->where('code', 'EN_ORAL')->first();
    $gt_component = $rco_final_student0->components->where('code', 'GT')->first();
    $ga_component = $rco_final_student0->components->where('code', 'GA')->first();
    $ma_component = $rco_final_student0->components->where('code', 'MA')->first();
    $ms_component = $rco_final_student0->components->where('code', 'MS')->first();
    $sys_net_avg_component = $rco_final_student0->components->where('code', 'SYS_NET_AVG')->first();
    $conduct_component = $rco_final_student0->components->where('code', 'CONDUCT')->first();
    $schooldays_component = $rco_final_student0->components->where('code', 'SCHOOLDAYS')->first();
    $merit_component = $rco_final_student0->components->where('code', 'MERIT')->first();
    $exceptional_component = $rco_final_student0->components->where('code', 'EXCEPTIONAL')->first();
    $promotion_component = $rco_final_student0->components->where('code', 'PROMOTION')->first();
    $leave_component = $rco_final_student0->components->where('code', 'LEAVE')->first();

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $chinese_component->id,
        'report_card_output_component_code' => $chinese_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $chinese_component->subject_id,
        'grading_scheme_id' => $chinese_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'calculate_rank' => true,
        'total' => 84.40,
        'label' => 'A',
        'total_grade->display_as_name' => 'A',
        'total_grade->name' => 'A',
        'total_grade->extra_marks' => 0,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $bm_component->id,
        'report_card_output_component_code' => $bm_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $bm_component->subject_id,
        'grading_scheme_id' => $bm_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 81.20,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $en_component->id,
        'report_card_output_component_code' => $en_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $en_component->subject_id,
        'grading_scheme_id' => $en_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 85.50,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $math_component->id,
        'report_card_output_component_code' => $math_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $math_component->subject_id,
        'grading_scheme_id' => $math_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 88.80,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $en_oral_component->id,
        'report_card_output_component_code' => $en_oral_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $en_oral_component->subject_id,
        'grading_scheme_id' => $en_oral_component->grading_scheme_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => null,
        'label' => '-',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $gt_component->id,
        'report_card_output_component_code' => $gt_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $gt_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 505.30,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $ga_component->id,
        'report_card_output_component_code' => $ga_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $ga_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'grading_scheme_id' => $ga_component->grading_scheme_id,
        'total' => 84.22,
        'label' => 'A',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $ma_component->id,
        'report_card_output_component_code' => $ma_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $ma_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 2.70,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $ms_component->id,
        'report_card_output_component_code' => $ms_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $ms_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 0,
    ]);


    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $sys_net_avg_component->id,
        'report_card_output_component_code' => $sys_net_avg_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $sys_net_avg_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 86.92,
        'label' => 'A',
        'class_rank' => 2,
        'class_population' => 5,
    ]);

    // Average of Sem 1 and Sem 2 Conduct = 87.95, 87.7
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $conduct_component->id,
        'report_card_output_component_code' => $conduct_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $conduct_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 87.83,
        'label' => 'Great',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $schooldays_component->id,
        'report_card_output_component_code' => $schooldays_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $schooldays_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'total' => 130,
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $merit_component->id,
        'report_card_output_component_code' => $merit_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $merit_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => "2 Merit (Charity); 1 Merit, 2 Merit (Cleaning); 1 Merit, 2 Merit (Cleaning)",
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $exceptional_component->id,
        'report_card_output_component_code' => $exceptional_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $exceptional_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => 'Swimming competition; Football Match; Football Match',
    ]);

    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $promotion_component->id,
        'report_card_output_component_code' => $promotion_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $promotion_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => '升',
    ]);

    /*
    $this->assertDatabaseHas(ResultsPostingLineItem::class, [
        'header_id' => $final_header->id,
        'report_card_output_component_id' => $leave_component->id,
        'report_card_output_component_code' => $leave_component->code,
        'student_id' => $students[0]->id,
        'subject_id' => $leave_component->subject_id,
        'grading_framework_id' => $sgf0->grading_framework_id,
        'label' => 'Public Holiday:8节',
    ]);*/

    $student0_report_card = StudentReportCard::where('student_id', $students[0]->id)
        ->orderBy('created_at', 'DESC')
        ->first();

    $bucket_name = config('filesystems.disks.s3-downloads.bucket');
    $filename = 'report-card-' . $final_header->code . '-' . $students[0]->student_number;

    expect($student0_report_card->file_url)->not()->toBeNull()
        ->and(preg_match("/^https\:\/\/$bucket_name\.s3\.ap-southeast-1\.amazonaws\.com\/download\/report-card\/".$filename."\.pdf$/",
            $student0_report_card->file_url))->toBe(1);

    // Report Card URLs for all students, can remove once confident in report card printing
    print_r(StudentReportCard::all()->pluck('file_url')->toArray());

});
