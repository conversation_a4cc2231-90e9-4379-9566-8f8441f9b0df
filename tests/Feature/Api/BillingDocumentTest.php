<?php

use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\PaymentType;
use App\Events\InvoiceVoidedEvent;
use App\Http\Resources\BillToResource;
use App\Interfaces\Userable;
use App\Models\Bank;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Config;
use App\Models\Currency;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\Payment;
use App\Models\PaymentGatewayLog;
use App\Models\PaymentMethod;
use App\Models\Student;
use App\Models\UnpaidItem;
use App\Models\User;
use App\Services\Billing\AccountingService;
use App\Services\Billing\BillingDocumentService;
use Carbon\Carbon;
use Database\Seeders\BankSeeder;
use Database\Seeders\CurrencySeeder;
use Database\Seeders\EmployeeSeeder;
use Database\Seeders\GlAccountSeeder;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\LegalEntitySeeder;
use Database\Seeders\PaymentMethodSeeder;
use Database\Seeders\PaymentTermsSeeder;
use Database\Seeders\PermissionSeeder;
use Database\Seeders\TaxSeeder;
use Database\Seeders\UomSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
        CurrencySeeder::class,
        TaxSeeder::class,
        UomSeeder::class,
        GlAccountSeeder::class,
        PaymentMethodSeeder::class,
        BankSeeder::class,
        PaymentTermsSeeder::class,
        LegalEntitySeeder::class,
        EmployeeSeeder::class,
    ]);

    $this->user = User::factory()->create();
    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->currency = Currency::where('code', config('school.currency_code'))->first();
});

test('index admin', function () {
    BillingDocument::factory(2)->student()->create();

    $billing_document = BillingDocument::factory()->student()->create([
        'document_date' => '2024-12-31',
        'paid_at' => '2024-12-30 10:00:00',
    ]);

    $payment = Payment::factory()->create([
        'billing_document_id' => $billing_document->id,
        'payment_method_id' => PaymentMethod::factory()->create()->id,
        'payment_reference_no' => 'PAYEX-123456',
    ]);

    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[$billing_document->bill_to_type],
        'bill_to_id' => $billing_document->bill_to_id,
        'id' => $billing_document->id,
        'status' => $billing_document->status,
        'payment_status' => $billing_document->payment_status,
        'type' => $billing_document->type,
        'sub_type' => $billing_document->sub_type,
        'paid_at_from' => '2024-12-30 09:00:00',
        'paid_at_to' => '2024-12-30 11:00:00',
        'reference_no' => $billing_document->reference_no,
        'payment_reference_no' => 'PAYEX-123456',
        'document_date_from' => '2024-12-30',
        'document_date_to' => '2024-12-30',
        'includes' => [
            'billTo',
        ],
    ];

    $this->mock(BillingDocumentService::class, function (MockInterface $mock) use ($filters, $billing_document) {
        $mock->shouldReceive('getAllPaginatedBillingDocuments')
            ->with(array_merge($filters, ['bill_to_type' => Userable::USERABLE_MAPPING[$filters['bill_to_type']]]))
            ->once()
            ->andReturn(new LengthAwarePaginator([$billing_document->loadMissing([
                'billTo',
            ])], 1, 1))
        ;
    });

    $response = $this->getJson(
        route('admin.billing-document.index-admin', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $billing_document->id,
            'reference_number' => $billing_document->reference_no,
            'document_date' => $billing_document->document_date,
            'paid_at' => $billing_document->paid_at->tz(config('school.timezone'))->toIso8601String(),
            'type' => $billing_document->type,
            'sub_type' => $billing_document->sub_type,
            'status' => $billing_document->status,
            'payment_status' => $billing_document->payment_status,
            'legal_entity_name' => $billing_document->legal_entity_name,
            'legal_entity_address' => $billing_document->legal_entity_address,
            'bill_to_name' => $billing_document->bill_to_name,
            'bill_to_address' => $billing_document->bill_to_address,
            'bill_to_reference_number' => $billing_document->bill_to_reference_number,
            'bill_to' => resourceToArray(new BillToResource($billing_document->billTo)),
            'tax_code' => $billing_document->tax_code,
            'tax_description' => $billing_document->tax_description,
            'tax_percentage' => $billing_document->tax_percentage,
            'payment_due_date' => $billing_document->payment_due_date,
            'remit_to_account_number' => $billing_document->remit_to_account_number,
            'remit_to_account_name' => $billing_document->remit_to_account_name,
            'remit_to_bank_name' => $billing_document->remit_to_bank_name,
            'remit_to_bank_address' => $billing_document->remit_to_bank_address,
            'remit_to_swift_code' => $billing_document->remit_to_swift_code,
            'currency_code' => $billing_document->currency_code,
            'amount_before_tax' => $billing_document->amount_before_tax,
            'amount_before_tax_after_less_advance' => $billing_document->amount_before_tax_after_less_advance,
            'tax_amount' => $billing_document->tax_amount,
            'amount_after_tax' => $billing_document->amount_after_tax,
            'receipt_url' => $billing_document->receipt_url,
        ]);
});

test('index admin determine getPaginated per_page is not -1', function () {
    $this->mock(BillingDocumentService::class, function (MockInterface $mock) {
        $billing_document = BillingDocument::factory()->create();

        $mock->shouldReceive('getAllPaginatedBillingDocuments')
            ->once()
            ->andReturn(new LengthAwarePaginator([$billing_document], 1, 1));
    });

    $response = $this->getJson(route('admin.billing-document.index-admin', [
        'per_page' => 10,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});

test('index admin determine getAll per_page is -1', function () {
    $this->mock(BillingDocumentService::class, function (MockInterface $mock) {
        $billing_documents = BillingDocument::factory(2)->create();

        $mock->shouldReceive('getAllBillingDocuments')->once()->andReturn($billing_documents);
    });

    $response = $this->getJson(route('admin.billing-document.index-admin', [
        'per_page' => -1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('index FE only', function () {
    $student = Student::factory()->create();
    $student_user = $student->user;

    $student_user->syncPermissions(['billing-document-view']);

    Sanctum::actingAs($student_user);


    $billing_documents = BillingDocument::factory(2)->student()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'document_date' => '2024-12-30',
    ]);

    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[Student::class],
        'bill_to_id' => $student->id,
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'document_date_from' => '2024-12-30',
        'document_date_to' => '2024-12-30',
        'reference_no' => $billing_documents[0]->reference_no,
        'includes' => [
            'billTo',
        ],
    ];

    $this->mock(BillingDocumentService::class, function (MockInterface $mock) use ($filters, $billing_documents) {
        $mock->shouldReceive('getAllPaginatedBillingDocuments')
            ->with(array_merge($filters, [
                'bill_to_type' => Userable::USERABLE_MAPPING[$filters['bill_to_type']],
                'order_by' => ['created_at' => 'desc'],
            ]))
            ->once()
            ->andReturn(new LengthAwarePaginator([$billing_documents[0]->loadMissing([
                'billTo',
            ])], 1, 1))
        ;
    });

    $response = $this->getJson(
        route('billing-document.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $billing_documents[0]->id,
            'reference_number' => $billing_documents[0]->reference_no,
            'document_date' => $billing_documents[0]->document_date,
            'paid_at' => $billing_documents[0]->paid_at,
            'type' => $billing_documents[0]->type,
            'sub_type' => $billing_documents[0]->sub_type,
            'status' => $billing_documents[0]->status,
            'payment_status' => $billing_documents[0]->payment_status,
            'legal_entity_name' => $billing_documents[0]->legal_entity_name,
            'legal_entity_address' => $billing_documents[0]->legal_entity_address,
            'bill_to_name' => $billing_documents[0]->bill_to_name,
            'bill_to_address' => $billing_documents[0]->bill_to_address,
            'bill_to_reference_number' => $billing_documents[0]->bill_to_reference_number,
            'bill_to' => resourceToArray(new BillToResource($billing_documents[0]->billTo)),
            'tax_code' => $billing_documents[0]->tax_code,
            'tax_description' => $billing_documents[0]->tax_description,
            'tax_percentage' => $billing_documents[0]->tax_percentage,
            'payment_due_date' => $billing_documents[0]->payment_due_date,
            'remit_to_account_number' => $billing_documents[0]->remit_to_account_number,
            'remit_to_account_name' => $billing_documents[0]->remit_to_account_name,
            'remit_to_bank_name' => $billing_documents[0]->remit_to_bank_name,
            'remit_to_bank_address' => $billing_documents[0]->remit_to_bank_address,
            'remit_to_swift_code' => $billing_documents[0]->remit_to_swift_code,
            'currency_code' => $billing_documents[0]->currency_code,
            'amount_before_tax' => $billing_documents[0]->amount_before_tax,
            'amount_before_tax_after_less_advance' => $billing_documents[0]->amount_before_tax_after_less_advance,
            'tax_amount' => $billing_documents[0]->tax_amount,
            'amount_after_tax' => $billing_documents[0]->amount_after_tax,
            'receipt_url' => $billing_documents[0]->receipt_url,
        ]);
});

test('index FE sorted correctly by created_at desc', function () {
    $student = Student::factory()->create();
    $student_user = $student->user;

    $billing_documents = BillingDocument::factory(3)->state(new Sequence(
        [
            'bill_to_type' => Student::class,
            'bill_to_id' => $student->id,
            'created_at' => '2023-12-31 10:00:00', // last
        ],
        [
            'bill_to_type' => Student::class,
            'bill_to_id' => $student->id,
            'created_at' => '2024-12-31 10:00:00', // second
        ],
        [
            'bill_to_type' => Student::class,
            'bill_to_id' => $student->id,
            'created_at' => '2025-12-31 10:00:00', // first
        ]
    ))->create();

    $student_user->syncPermissions(['billing-document-view']);

    Sanctum::actingAs($student_user);

    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[Student::class],
        'bill_to_id' => $student->id,
    ];

    $response = $this->getJson(route('billing-document.index', $filters))->json();

    expect($response['data'])->toHaveCount(3)
        ->and($response['data'][0]['id'])->toBe($billing_documents[2]->id)
        ->and($response['data'][1]['id'])->toBe($billing_documents[1]->id)
        ->and($response['data'][2]['id'])->toBe($billing_documents[0]->id);
});

test('index FE only : validation failed', function () {
    /**
     * Forbidden because $student is not under $this->user
     */
    $student = Student::factory()->create();

    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[Student::class],
        'bill_to_id' => $student->id,
    ];

    $response = $this->getJson(
        route('billing-document.index', $filters)
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => 'Forbidden',
        ]);

    /**
     * Empty payload
     */

    $response = $this->getJson(
        route('billing-document.index', [])
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'bill_to_type' => [
                    'The bill to type field is required.',
                ],
                'bill_to_id' => [
                    'The bill to id field is required.',
                ],
            ],
        ]);
});


test('show FE only', function () {

    $user = User::factory()->create();
    $user2 = User::factory()->create();
    $parent_user = User::factory()->create();
    $parent_user2 = User::factory()->create();

    $user->syncPermissions(['billing-document-view']);
    $user2->syncPermissions(['billing-document-view']);
    $parent_user->syncPermissions(['billing-document-view']);
    $parent_user2->syncPermissions(['billing-document-view']);

    $student = Student::factory()->create([
        'user_id' => $user->id,
    ]);
    $student2 = Student::factory()->create([
        'user_id' => $user2->id,
    ]);
    $guardian = Guardian::factory()->create([
        'user_id' => $parent_user->id,
    ]);
    $guardian2 = Guardian::factory()->create([
        'user_id' => $parent_user2->id,
    ]);

    GuardianStudent::factory()->create([
        'studenable_type' => Student::class,
        'studenable_id' => $student,
        'guardian_id' => $guardian->id,
    ]);
    GuardianStudent::factory()->create([
        'studenable_type' => Student::class,
        'studenable_id' => $student2,
        'guardian_id' => $guardian2->id,
    ]);

    $invoice = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
    ]);
    $invoice2 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'bill_to_type' => $guardian->getBillToType(),
        'bill_to_id' => $guardian->getBillToId(),
        'bill_to_name' => $guardian->getBillToName(),
    ]);

    // For invoice1 - only user and parent_user can access
    // login as student
    Sanctum::actingAs($user);

    $response = $this->getJson(
        route('billing-document.show', ['billing_document' => $invoice->id])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse();

    // cannot access parent_user billing document
    $response = $this->getJson(
        route('billing-document.show', ['billing_document' => $invoice2->id])
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => 'Forbidden',
        ]);

    // login as student's guardian
    Sanctum::actingAs($parent_user);

    $response = $this->getJson(
        route('billing-document.show', ['billing_document' => $invoice->id])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse();

    // invoice2 only guardian parent_user can access
    $response = $this->getJson(
        route('billing-document.show', ['billing_document' => $invoice2->id])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse();

    // login as student 2
    Sanctum::actingAs($user2);

    $response = $this->getJson(
        route('billing-document.show', ['billing_document' => $invoice->id])
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => 'Forbidden',
        ]);

    $response = $this->getJson(
        route('billing-document.show', ['billing_document' => $invoice2->id])
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => 'Forbidden',
        ]);


    // login as student 2 guardian
    Sanctum::actingAs($parent_user2);

    $response = $this->getJson(
        route('billing-document.show', ['billing_document' => $invoice->id])
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => 'Forbidden',
        ]);

    $response = $this->getJson(
        route('billing-document.show', ['billing_document' => $invoice2->id])
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => 'Forbidden',
        ]);

});

test('show admin', function () {
    $billing_document = BillingDocument::factory()->create();

    $response = $this->getJson(route('admin.billing-document.show', $billing_document->id));

    $response->assertStatus(200);

    expect($response->json()['data'])->toEqual([
        'id' => $billing_document->id,
        'reference_number' => $billing_document->reference_no,
        'document_date' => $billing_document->document_date,
        'paid_at' => $billing_document->paid_at,
        'type' => $billing_document->type,
        'sub_type' => $billing_document->sub_type,
        'status' => $billing_document->status,
        'payment_status' => $billing_document->payment_status,
        'legal_entity_name' => $billing_document->legal_entity_name,
        'legal_entity_address' => $billing_document->legal_entity_address,
        'bill_to_name' => $billing_document->bill_to_name,
        'bill_to_address' => $billing_document->bill_to_address,
        'bill_to_reference_number' => $billing_document->bill_to_reference_number,
        'tax_code' => $billing_document->tax_code,
        'tax_description' => $billing_document->tax_description,
        'tax_percentage' => $billing_document->tax_percentage,
        'payment_due_date' => $billing_document->payment_due_date,
        'remit_to_account_number' => $billing_document->remit_to_account_number,
        'remit_to_account_name' => $billing_document->remit_to_account_name,
        'remit_to_bank_name' => $billing_document->remit_to_bank_name,
        'remit_to_bank_address' => $billing_document->remit_to_bank_address,
        'remit_to_swift_code' => $billing_document->remit_to_swift_code,
        'currency_code' => $billing_document->currency_code,
        'amount_before_tax' => $billing_document->amount_before_tax,
        'amount_before_tax_after_less_advance' => $billing_document->amount_before_tax_after_less_advance,
        'tax_amount' => $billing_document->tax_amount,
        'amount_after_tax' => $billing_document->amount_after_tax,
        'receipt_url' => $billing_document->receipt_url,
        'line_items' => [],
        'payments' => [],
    ]);
});

test('change status', function () {
    $billing_document = BillingDocument::factory()->create([
        'status' => BillingDocument::STATUS_DRAFT,
        'type' => BillingDocument::TYPE_INVOICE,
    ]);

    $payload = [
        'billing_document_id' => $billing_document->id,
        'new_status' => BillingDocument::STATUS_CONFIRMED,
    ];

    $response = $this->putJson(route('admin.billing-document.change-status'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas('billing_documents', [
        'id' => $billing_document->id,
        'status' => BillingDocument::STATUS_CONFIRMED
    ]);
});

test('non-admin void billing document : only accessable user can void own billing document', function () {
    $guardian = Guardian::factory()->create();
    $guardian_user = $guardian->user;

    $student = Student::factory()->create();
    $student_user = $student->user;

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian->id,
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
    ]);

    $billing_documents = BillingDocument::factory(3)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $billing_documents[0]->id,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $billing_documents[0]->id,
        'billable_item_type' => get_class($unpaid_item1),
        'billable_item_id' => $unpaid_item1->id,
    ]);

    $unpaid_item2 = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $billing_documents[1]->id,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $billing_documents[1]->id,
        'billable_item_type' => get_class($unpaid_item2),
        'billable_item_id' => $unpaid_item2->id,
    ]);

    $unpaid_item3 = UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $billing_documents[2]->id,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $billing_documents[2]->id,
        'billable_item_type' => get_class($unpaid_item3),
        'billable_item_id' => $unpaid_item3->id,
    ]);


    // $student void the billing_documents[0]
    Event::fake();

    $student_user->syncPermissions(['billing-document-void']);

    Sanctum::actingAs($student_user);



    $response = $this->postJson(route('billing-document.void', $billing_documents[0]->id))->json();


    Event::assertDispatchedTimes(InvoiceVoidedEvent::class, 1);

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas('billing_documents', [
        'id' => $billing_documents[0]->id,
        'status' => BillingDocument::STATUS_VOIDED,
    ]);



    // $guardian void the billing_documents[1]
    Event::fake();

    $guardian_user->syncPermissions(['billing-document-void']);

    Sanctum::actingAs($guardian_user);



    $response = $this->postJson(route('billing-document.void', $billing_documents[1]->id))->json();


    Event::assertDispatchedTimes(InvoiceVoidedEvent::class, 1);

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas('billing_documents', [
        'id' => $billing_documents[1]->id,
        'status' => BillingDocument::STATUS_VOIDED,
    ]);




    /**
     * UNRELATED guardian try to void billing_documents[2]
     */
    Event::fake();

    $unrelated_guardian = Guardian::factory()->create();
    $unrelated_guardian_user = $unrelated_guardian->user;

    $unrelated_guardian_user->syncPermissions(['billing-document-void']);

    Sanctum::actingAs($unrelated_guardian_user);


    $response = $this->postJson(route('billing-document.void', $billing_documents[1]->id))->json();


    Event::assertNotDispatched(InvoiceVoidedEvent::class);

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => 'Forbidden',
        ]);

    // unaffected
    $this->assertDatabaseHas('billing_documents', [
        'id' => $billing_documents[2]->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
    ]);
});

test('manualPayment failed because of validation', function () {
    /**
     * no payments
     */
    $billing_document = BillingDocument::factory()->create();

    $payload = [
        'remarks' => 'Paid by guardian',
        'payments' => [],
    ];

    $response = $this->postJson(route('admin.billing-document.manual-payment', ['billing_document' => $billing_document->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'payments' => [
                'The payments field is required.',
            ],
        ]);


    /**
     * invalid payment load
     */

    $payload = [
        'remarks' => 'Paid by guardian',
        'payments' => [
            [
                'payment_method_code' => 'INVALID',
                'bank_id' => 111111,
                'payment_reference_no' => '123',
                'amount_received' => '100.00',
                'file' => UploadedFile::fake()->create('proof_file.png', 500)
            ]
        ],
    ];

    $response = $this->postJson(route('admin.billing-document.manual-payment', ['billing_document' => $billing_document->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'payments.0.payment_method_code' => [
                'The selected payment method code is invalid.',
            ],
            'payments.0.bank_id' => [
                'The selected bank is invalid.',
            ],
        ]);


    /**
     * bank_id is required when payment_method_code is BANK_TRANSFER
     * payment_reference_no is required when payment_method_code is not BANK_TRANSFER or FPX
     */

    $payload = [
        'remarks' => 'Paid by guardian',
        'payments' => [
            [
                'payment_method_code' => PaymentMethod::CODE_BANK_TRANSFER,
                'bank_id' => null, // no bank
                'payment_reference_no' => '123',
                'amount_received' => '100.00',
                'file' => UploadedFile::fake()->create('proof_file.png', 500)
            ],
            [
                'payment_method_code' => PaymentMethod::CODE_CHEQUE,
                'bank_id' => null,
                'payment_reference_no' => null, // no payment_reference_no
                'amount_received' => '100.00',
                'file' => UploadedFile::fake()->create('proof_file.png', 500)
            ],
        ],
    ];

    $response = $this->postJson(route('admin.billing-document.manual-payment', ['billing_document' => $billing_document->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'payments.0.bank_id' => [
                'The bank field is required when payment method code is BANK.',
            ],
            'payments.1.payment_reference_no' => [
                'The payment reference no field is required when payment method code is CHEQUE.',
            ],
        ]);
});

test('manualPayment success', function () {
    $student = Student::factory()->create();

    $invoice = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_before_tax' => 100,
        'amount_after_tax' => 100,
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_reference_number' => $student->getBillToReferenceNumber(),
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => null,
    ]);

    $bank = Bank::first();

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice,
        'amount_before_tax' => 100,
        'gl_account_code' => 'SCH00000001',
        'currency_code' => 'MYR',
        'description' => 'School Fees Jan 2024',
    ]);

    $payload = [
        'remarks' => 'Paid invoice for 3 payment',
        'payments' => [
            [
                'payment_method_code' => PaymentMethod::CODE_BANK_TRANSFER,
                'bank_id' => $bank->id,
                'payment_reference_no' => 'INV1113',
                'amount_received' => '40.50',
                'file' => UploadedFile::fake()->create('proof_file.png', 500)
            ],
            [
                'payment_method_code' => PaymentMethod::CODE_CASH,
                'amount_received' => '59.50',
            ],
        ],
    ];

    $this->mock(AccountingService::class, function (MockInterface $mock) use ($payload, $invoice) {
        $mock->shouldReceive('setUser')->once()->with($this->user)->andReturnSelf();
        $mock->shouldReceive('setBillingDocument')->once()->withArgs(function ($arg) use ($invoice) {
            return $arg instanceof BillingDocument && $arg->id === $invoice->id;
        })->andReturnSelf();
        $mock->shouldReceive('processManualPayment')->once()->with($payload)->andReturnTrue();
    });

    $response = $this->postJson(route('admin.billing-document.manual-payment', ['billing_document' => $invoice->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('makePayment - generate from Payex - : only accessable user can make payment again', function () {
    $guardian = Guardian::factory()->create();
    $guardian_user = $guardian->user;

    $student = Student::factory()->create();
    $student_user = $student->user;

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian->id,
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
    ]);

    $billing_documents = BillingDocument::factory(3)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_after_tax' => 1,
    ]);

    createPayexConfig();
    mockPayex();

    $this->assertDatabaseCount('payment_gateway_logs', 0);

    /**
     *
     * $student_user make payment for billing_documents[0]->id
     *
     */
    $student_user->syncPermissions(['billing-document-make-payment']);

    Sanctum::actingAs($student_user);

    $response = $this->postJson(route('billing-document.make-payment', $billing_documents[0]->id))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'currency' => $this->currency->code,
            'billing_document_id' => $billing_documents[0]->id,
            'amount' => $billing_documents[0]->amount_after_tax,
            'status' => PaymentStatus::PENDING->value,
            'payment_url' => 'VALID_PAYMENT_URL',
            'payment_required' => true,
        ])->toHaveKey('order_id');

    $this->assertDatabaseCount('payment_gateway_logs', 1);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'requested_by_id' => $student_user->id,
        'type' => PaymentType::FEE_PAYMENT->value,
        'provider' => PaymentProvider::PAYEX->value,
        'transaction_loggable_type' => BillingDocument::class,
        'transaction_loggable_id' => $billing_documents[0]->id,
        'currency_id' => $this->currency->id,
        'currency_code' => $this->currency->code,
        'currency_name' => $this->currency->name,
        'amount' => $billing_documents[0]->amount_after_tax,
        'status' => PaymentStatus::PENDING,
        'description' => 'Pay Unpaid Item',
        'payment_url' => 'VALID_PAYMENT_URL'
    ]);

    /**
     *
     * $guardian_user make payment for billing_documents[1]->id
     *
     */

    $guardian_user->syncPermissions(['billing-document-make-payment']);

    Sanctum::actingAs($guardian_user);

    $response = $this->postJson(route('billing-document.make-payment', $billing_documents[1]->id))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'currency' => $this->currency->code,
            'billing_document_id' => $billing_documents[1]->id,
            'amount' => $billing_documents[1]->amount_after_tax,
            'status' => PaymentStatus::PENDING->value,
            'payment_url' => 'VALID_PAYMENT_URL',
            'payment_required' => true,
        ])->toHaveKey('order_id');

    $this->assertDatabaseCount('payment_gateway_logs', 2);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'requested_by_id' => $guardian_user->id,
        'type' => PaymentType::FEE_PAYMENT->value,
        'provider' => PaymentProvider::PAYEX->value,
        'transaction_loggable_type' => BillingDocument::class,
        'transaction_loggable_id' => $billing_documents[1]->id,
        'currency_id' => $this->currency->id,
        'currency_code' => $this->currency->code,
        'currency_name' => $this->currency->name,
        'amount' => $billing_documents[1]->amount_after_tax,
        'status' => PaymentStatus::PENDING,
        'description' => 'Pay Unpaid Item',
        'payment_url' => 'VALID_PAYMENT_URL'
    ]);


    /**
     *
     * $unrelated_guardian make payment for billing_documents[2]->id
     *
     */

    $unrelated_guardian = Guardian::factory()->create();
    $unrelated_guardian_user = $unrelated_guardian->user;

    $unrelated_guardian_user->syncPermissions(['billing-document-make-payment']);

    Sanctum::actingAs($unrelated_guardian_user);

    $response = $this->postJson(route('billing-document.make-payment', $billing_documents[2]->id))->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => 'Forbidden',
        ]);

    $this->assertDatabaseCount('payment_gateway_logs', 2);
});

test('makePayment - generate from Payex - : with return_url', function () {
    $student = Student::factory()->create();
    $student_user = $student->user;

    $billing_documents = BillingDocument::factory(3)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'amount_after_tax' => 1,
    ]);

    $student_user->syncPermissions(['billing-document-make-payment']);

    Sanctum::actingAs($student_user);

    createPayexConfig();
    mockPayex();

    /**
     *
     * NO RETURN URL
     */
    $payload = [
        // 'return_url' => 'https://google.com',
    ];

    $response = $this->postJson(route('billing-document.make-payment', $billing_documents[0]->id), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $first_payment_gateway_log = PaymentGatewayLog::where('billing_document_id', $billing_documents[0]->id)->first();

    expect($first_payment_gateway_log->request_data['return_url'])->not->toContain('redirect');



    /**
     *
     * WITH RETURN URL
     */

    $payex_return_url = url(config('services.payment_gateway.payex.return_url'));
    $redirect_url = 'https://google.com';

    $payload = [
        'return_url' => $redirect_url,
    ];

    $response = $this->postJson(route('billing-document.make-payment', $billing_documents[1]->id), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $second_payment_gateway_log = PaymentGatewayLog::where('billing_document_id', $billing_documents[1]->id)->first();

    expect($second_payment_gateway_log->request_data['return_url'])->toBe($payex_return_url . '?' . http_build_query(['redirect_url' => $redirect_url]));
});

function mockPayex()
{
    $payex_base_url = config('services.payment_gateway.payex.base_url');
    $payex_payment_url = $payex_base_url . '/' . config('services.payment_gateway.payex.payment_url');
    $payex_auth_url = $payex_base_url . '/' . config('services.payment_gateway.payex.auth_url');

    Http::fake([
        $payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_payment_url => Http::response(
            [
                'request_id' => 'd9e48d61-df21-422c-82f3-19eaf66ee5f8',
                'status' => '00',
                'result' => [
                    [
                        'status' => '00',
                        'key' => '7e3afcc5957b4555909d085832550147',
                        'url' => 'VALID_PAYMENT_URL',
                        'error' => null
                    ]
                ],
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_payment_url => Http::response(
            [
                'request_id' => 'd9e48d61-df21-422c-82f3-19eaf66ee5f8',
                'status' => '00',
                'result' => [
                    [
                        'status' => '00',
                        'key' => '7e3afcc5957b4555909d085832550147',
                        'url' => 'VALID_PAYMENT_URL',
                        'error' => null
                    ]
                ],
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
    ]);
}

function createPayexConfig()
{
    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>'
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_SECRET,
        'value' => '123456'
    ]);
}
