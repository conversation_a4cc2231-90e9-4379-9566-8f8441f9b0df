<?php

use App\Enums\EnrollmentStatus;
use App\Enums\PaymentStatus;
use App\Enums\WalletTransactionStatus;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Config;
use App\Models\Currency;
use App\Models\Enrollment;
use App\Models\Guardian;
use App\Models\Payment;
use App\Models\PaymentGatewayLog;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use Database\Seeders\EmployeeSeeder;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PaymentMethodSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Events\CallQueuedListener;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
        PaymentMethodSeeder::class,
        EmployeeSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->user->assignRole('Super Admin');

    $this->callbackRoute = 'payment-gateway.callback';
    $this->returnRoute = 'payment-gateway.router';

    $this->walletTable = resolve(Wallet::class)->getTable();
    $this->paymentGatewayLogTable = resolve(PaymentGatewayLog::class)->getTable();
    $this->walletTransactionTable = resolve(WalletTransaction::class)->getTable();

    $this->currency = Currency::factory()->create([
        'code' => 'MYR',
        'name' => 'Malaysian Ringgit',
        'symbol' => 'RM'
    ]);

    $this->wallet = Wallet::factory()->create([
        'user_id' => $this->user->id,
        'balance' => 10
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>'
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_SECRET,
        'value' => '123456'
    ]);

    $this->payex_base_url = config('services.payment_gateway.payex.base_url');
    $this->payex_auth_url = $this->payex_base_url . '/' . config('services.payment_gateway.payex.auth_url');
});

test('callback for wallet transaction', function () {
    $payex_transaction_url = $this->payex_base_url . '/' . config('services.payment_gateway.payex.transaction_url');

    $failed_callback_data = [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_8_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "PX1381401a4ef5411392",
        "external_txn_id" => "****************",
        "response" => "Invalid+Transaction",
        "auth_code" => "12", // This indicates that the transaction is failed
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ];

    $success_callback_data = [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_9_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "SUCCESS1381401a4ef5411392",
        "external_txn_id" => "****************",
        "response" => "",
        "auth_code" => "00", // This indicates that the transaction is successful
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ];

    Http::fake([
        $this->payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_transaction_url . $failed_callback_data['txn_id'] => Http::response(
            [
                [
                    "txn_date" => "**************",
                    "mid" => "1381401",
                    "name" => "Skribble Lab Sdn Bhd",
                    "title" => "Default",
                    "collection_id" => "dJyEd14i",
                    "invoice_id" => "-",
                    "txn_id" => "PX1381401a4ef5411392",
                    "status" => "Failed",
                    "customer_name" => "Superadmin",
                    "currency" => "MYR",
                    "base_amount" => 1.05,
                    "amount" => 1.05,
                    "amount_refunded" => 0.0,
                    "txn_type" => "FPX",
                    "fpx_mode" => "01",
                    "fpx_buyer_name" => "N@me()/ .-_,&Buyer'`~*;:",
                    "fpx_buyer_bank_id" => "ABB0234",
                    "fpx_buyer_bank_name" => "Affin B2C - Test ID",
                    "card_holder_name" => null,
                    "card_number" => null,
                    "card_expiry" => null,
                    "card_brand" => "FPX",
                    "external_txn_id" => "****************",
                    "response" => "Invalid Transaction",
                    "auth_code" => "12",
                    "auth_number" => "********",
                    "settlement_id" => null,
                    "settlement_date" => null,
                    "email" => "<EMAIL>",
                    "contact_number" => null,
                    "address" => null,
                    "postcode" => null,
                    "city" => null,
                    "state" => null,
                    "country" => null,
                    "shipping_name" => "Superadmin",
                    "shipping_email" => "<EMAIL>",
                    "shipping_contact_number" => null,
                    "shipping_address" => null,
                    "shipping_postcode" => null,
                    "shipping_city" => null,
                    "shipping_state" => null,
                    "shipping_country" => null,
                    "description" => null,
                    "reference_number" => "1716982240_8_test",
                    "mandate_reference_number" => null,
                    "collection_number" => null,
                    "collection_reference_number" => null,
                    "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
                    "split_amount" => null,
                    "split_rule" => null,
                    "split_type" => null,
                    "split_description" => null,
                    "items" => [],
                    "metadata" => [],
                    "voidable" => "-",
                ]
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_transaction_url . $success_callback_data['txn_id'] => Http::response(
            [
                [
                    "txn_date" => "**************",
                    "mid" => "1381401",
                    "name" => "Skribble Lab Sdn Bhd",
                    "title" => "Default",
                    "collection_id" => "dJyEd14i",
                    "invoice_id" => "-",
                    "txn_id" => "SUCCESS1381401a4ef5411392",
                    "status" => "Success",
                    "customer_name" => "Superadmin",
                    "currency" => "MYR",
                    "base_amount" => 1.05,
                    "amount" => 1.05,
                    "amount_refunded" => 0.0,
                    "txn_type" => "FPX",
                    "fpx_mode" => "01",
                    "fpx_buyer_name" => "N@me()/ .-_,&Buyer'`~*;:",
                    "fpx_buyer_bank_id" => "ABB0234",
                    "fpx_buyer_bank_name" => "Affin B2C - Test ID",
                    "card_holder_name" => null,
                    "card_number" => null,
                    "card_expiry" => null,
                    "card_brand" => "FPX",
                    "external_txn_id" => "****************",
                    "response" => "",
                    "auth_code" => "00",
                    "auth_number" => "********",
                    "settlement_id" => null,
                    "settlement_date" => null,
                    "email" => "<EMAIL>",
                    "contact_number" => null,
                    "address" => null,
                    "postcode" => null,
                    "city" => null,
                    "state" => null,
                    "country" => null,
                    "shipping_name" => "Superadmin",
                    "shipping_email" => "<EMAIL>",
                    "shipping_contact_number" => null,
                    "shipping_address" => null,
                    "shipping_postcode" => null,
                    "shipping_city" => null,
                    "shipping_state" => null,
                    "shipping_country" => null,
                    "description" => null,
                    "reference_number" => "1716982240_9_test",
                    "mandate_reference_number" => null,
                    "collection_number" => null,
                    "collection_reference_number" => null,
                    "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
                    "split_amount" => null,
                    "split_rule" => null,
                    "split_type" => null,
                    "split_description" => null,
                    "items" => [],
                    "metadata" => [],
                    "voidable" => "-",
                ]
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);

    //Test with missing payment gateway log
    $response = $this->postJson(route($this->callbackRoute, [
        'provider' => 'payex',
        'payment_gateway_log' => '123',
        'token' => '123'
    ]), $failed_callback_data)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toEqual('Resource not found');


    $invoice = BillingDocument::factory()->create([
        'reference_no' => 'INV-00000000001',
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    //Test with missing wallet_transaction
    $failed_payment_gateway_log = PaymentGatewayLog::factory()->create([
        'transaction_loggable_id' => null,
        'transaction_loggable_type' => null,
        'amount' => $failed_callback_data['amount'],
        'order_id' => $failed_callback_data['reference_number'],
        'billing_document_id' => $invoice->id,
    ]);

    // Test with processed payment
    $failed_payment_gateway_log->fill([
        'status' => PaymentStatus::SUCCESS
    ])->save();


    $new_balance = (float) $this->wallet->balance + (float) $failed_callback_data['amount'];

    //Test with failed response
    $failed_wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $this->wallet->id,
        'status' => WalletTransactionStatus::PENDING,
        'total_amount' => $failed_callback_data['amount'],
        'balance_before' => $this->wallet->balance,
        'balance_after' => $new_balance
    ]);

    $failed_payment_gateway_log->fill([
        'status' => PaymentStatus::PENDING,
        'transaction_loggable_type' => get_class($failed_wallet_transaction),
        'transaction_loggable_id' => $failed_wallet_transaction->id
    ])->save();

    $line_item = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_type' => get_class($failed_wallet_transaction),
        'billable_item_id' => $failed_wallet_transaction->id,
    ]);

    Queue::fake();

    $response = $this->postJson(route($this->callbackRoute, [
        'provider' => 'payex',
        'payment_gateway_log' => $failed_payment_gateway_log->id,
        'token' => $failed_payment_gateway_log->token
    ]), $failed_callback_data);

    expect($response->getContent())->toEqual('OK');

    Queue::assertPushedOn(
        'event-listeners',
        CallQueuedListener::class,
        function ($job) {
            return $job->class === \App\Listeners\InvoiceVoidedCallback::class;
        }
    );

    Queue::shouldReceive();

    // trigger event listener
    $event = new \App\Events\InvoiceVoidedEvent($invoice);
    $listener = new \App\Listeners\InvoiceVoidedCallback();

    $listener->handle($event);

    $this->assertDatabaseHas($this->walletTransactionTable, [
        'id' => $failed_wallet_transaction->id,
        'status' => WalletTransactionStatus::FAILED->value,
    ]);

    $this->assertDatabaseHas($this->paymentGatewayLogTable, [
        'id' => $failed_payment_gateway_log->id,
        'status' => PaymentStatus::FAILED->value,
    ]);

    // wallet balance no change
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $this->wallet->id,
        'balance' => 10,
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'status' => BillingDocument::STATUS_VOIDED,
    ]);

    $this->assertDatabaseEmpty(Payment::class);

    //Test with success response
    BillingDocumentLineItem::truncate();
    Payment::truncate();
    $invoice->payment_status = BillingDocument::PAYMENT_STATUS_UNPAID;
    $invoice->save();

    $success_wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $this->wallet->id,
        'status' => WalletTransactionStatus::PENDING,
        'total_amount' => $success_callback_data['amount'],
        'balance_before' => $this->wallet->balance,
        'balance_after' => $new_balance
    ]);

    $success_payment_gateway_log = PaymentGatewayLog::factory()->create([
        'status' => PaymentStatus::PENDING,
        'transaction_loggable_type' => get_class($success_wallet_transaction),
        'transaction_loggable_id' => $success_wallet_transaction->id,
        'amount' => $success_callback_data['amount'],
        'order_id' => $success_callback_data['reference_number'],
        'payment_method_id' => PaymentMethod::where('code', \App\Models\PaymentMethod::CODE_FPX)->firstOrFail()->id,
        'billing_document_id' => $invoice->id,
    ]);

    $line_item = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_type' => get_class($success_wallet_transaction),
        'billable_item_id' => $success_wallet_transaction->id,
    ]);

    //Test with invalid provider
    $response = $this->postJson(route($this->callbackRoute, [
        'provider' => 'INVALID_PROVIDER',
        'payment_gateway_log' => $success_payment_gateway_log->id,
        'token' => $success_payment_gateway_log->token
    ]), $failed_callback_data)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toEqual('Payment provider not found.');

    Queue::fake();

    // Test with valid provider and success callback
    $this->postJson(route($this->callbackRoute, [
        'provider' => \App\Enums\PaymentProvider::PAYEX->value,
        'payment_gateway_log' => $success_payment_gateway_log->id,
        'token' => $success_payment_gateway_log->token
    ]), $success_callback_data);

    Queue::assertPushedOn(
        'event-listeners',
        CallQueuedListener::class,
        function ($job) {
            return $job->class === \App\Listeners\MarkBillableItemAsPaid::class;
        }
    );

    Queue::shouldReceive();

    // trigger event listener
    $event = new \App\Events\InvoicePaidEvent($invoice);
    $listener = new \App\Listeners\MarkBillableItemAsPaid();

    $listener->handle($event);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
    ]);
    $this->assertDatabaseHas($this->walletTransactionTable, [
        'id' => $success_wallet_transaction->id,
        'status' => WalletTransactionStatus::SUCCESS->value,
    ]);

    $this->assertDatabaseHas($this->paymentGatewayLogTable, [
        'id' => $success_payment_gateway_log->id,
        'status' => PaymentStatus::SUCCESS->value,
    ]);

    $this->assertDatabaseHas($this->walletTable, [
        'id' => $this->wallet->id,
        'balance' => $new_balance,
    ]);

    $this->assertDatabaseHas(Payment::class, [
        'billing_document_id' => $invoice->id,
        'amount_received' => $success_payment_gateway_log->amount,
        'payment_method_id' => PaymentMethod::where('code', \App\Models\PaymentMethod::CODE_FPX)->firstOrFail()->id,
        'payment_source_id' => $success_payment_gateway_log->id,
        'payment_source_type' => get_class($success_payment_gateway_log),
    ]);

});


test('callback for wallet transaction - success then failed', function () {
    $payex_transaction_url = $this->payex_base_url . '/' . config('services.payment_gateway.payex.transaction_url');

    // callback data pointing to same payment intent and order id
    $failed_callback_data = [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_9_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "PX1381401a4ef5411391",
        "external_txn_id" => "****************",
        "response" => "Invalid+Transaction",
        "auth_code" => "12", // This indicates that the transaction is failed
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ];

    $success_callback_data = [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_9_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "PX1381401a4ef5411392",
        "external_txn_id" => "****************",
        "response" => "",
        "auth_code" => "00", // This indicates that the transaction is successful
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ];

    Http::fake([
        $this->payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_transaction_url . 'PX1381401a4ef5411392' => Http::response(
            [
                [
                    "txn_date" => "**************",
                    "mid" => "1381401",
                    "name" => "Skribble Lab Sdn Bhd",
                    "title" => "Default",
                    "collection_id" => "dJyEd14i",
                    "invoice_id" => "-",
                    "txn_id" => "PX1381401a4ef5411392",
                    "status" => "Failed",
                    "customer_name" => "Superadmin",
                    "currency" => "MYR",
                    "base_amount" => 1.05,
                    "amount" => 1.05,
                    "amount_refunded" => 0.0,
                    "txn_type" => "FPX",
                    "fpx_mode" => "01",
                    "fpx_buyer_name" => "N@me()/ .-_,&Buyer'`~*;:",
                    "fpx_buyer_bank_id" => "ABB0234",
                    "fpx_buyer_bank_name" => "Affin B2C - Test ID",
                    "card_holder_name" => null,
                    "card_number" => null,
                    "card_expiry" => null,
                    "card_brand" => "FPX",
                    "external_txn_id" => "****************",
                    "response" => "Invalid Transaction",
                    "auth_code" => "12",
                    "auth_number" => "********",
                    "settlement_id" => null,
                    "settlement_date" => null,
                    "email" => "<EMAIL>",
                    "contact_number" => null,
                    "address" => null,
                    "postcode" => null,
                    "city" => null,
                    "state" => null,
                    "country" => null,
                    "shipping_name" => "Superadmin",
                    "shipping_email" => "<EMAIL>",
                    "shipping_contact_number" => null,
                    "shipping_address" => null,
                    "shipping_postcode" => null,
                    "shipping_city" => null,
                    "shipping_state" => null,
                    "shipping_country" => null,
                    "description" => null,
                    "reference_number" => "1716982240_9_test",
                    "mandate_reference_number" => null,
                    "collection_number" => null,
                    "collection_reference_number" => null,
                    "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
                    "split_amount" => null,
                    "split_rule" => null,
                    "split_type" => null,
                    "split_description" => null,
                    "items" => [],
                    "metadata" => [],
                    "voidable" => "-",
                ]
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);


    $wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $this->wallet->id,
        'status' => WalletTransactionStatus::PENDING,
        'total_amount' => 1.05,
        'balance_before' => 10,
        'balance_after' => 11.05
    ]);

    $invoice = BillingDocument::factory()->create([
        'reference_no' => 'INV-00000000001',
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    $line_item = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_type' => get_class($wallet_transaction),
        'billable_item_id' => $wallet_transaction->id,
    ]);

    $payment_gateway_log = PaymentGatewayLog::factory()->create([
        'amount' => 1.05,
        'order_id' => '1716982240_9_test',
        'billing_document_id' => $invoice->id,
        'status' => PaymentStatus::PENDING,
        'transaction_loggable_type' => get_class($wallet_transaction),
        'transaction_loggable_id' => $wallet_transaction->id,
        'payment_method_id' => PaymentMethod::where('code', \App\Models\PaymentMethod::CODE_FPX)->firstOrFail()->id,
    ]);


    //Test with success response first
    Queue::fake();

    // Test with valid provider and success callback
    $this->postJson(route($this->callbackRoute, [
        'provider' => \App\Enums\PaymentProvider::PAYEX->value,
        'payment_gateway_log' => $payment_gateway_log->id,
        'token' => $payment_gateway_log->token
    ]), $success_callback_data);

    Queue::assertPushedOn(
        'event-listeners',
        CallQueuedListener::class,
        function ($job) {
            return $job->class === \App\Listeners\MarkBillableItemAsPaid::class;
        }
    );

    Queue::shouldReceive();

    // trigger event listener
    $event = new \App\Events\InvoicePaidEvent($invoice);
    $listener = new \App\Listeners\MarkBillableItemAsPaid();

    $listener->handle($event);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
    ]);
    $this->assertDatabaseHas($this->walletTransactionTable, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::SUCCESS->value,
    ]);

    $this->assertDatabaseHas($this->paymentGatewayLogTable, [
        'id' => $payment_gateway_log->id,
        'status' => PaymentStatus::SUCCESS->value,
        'reference_id' => 'PX1381401a4ef5411392',
    ]);

    $this->assertDatabaseHas($this->walletTable, [
        'id' => $this->wallet->id,
        'balance' => 11.05,
    ]);

    $this->assertDatabaseHas(Payment::class, [
        'billing_document_id' => $invoice->id,
        'amount_received' => 1.05,
        'payment_method_id' => PaymentMethod::where('code', \App\Models\PaymentMethod::CODE_FPX)->firstOrFail()->id,
        'payment_source_id' => $payment_gateway_log->id,
        'payment_source_type' => get_class($payment_gateway_log),
        'payment_reference_no' => 'PX1381401a4ef5411392',
    ]);

    // then receive failed response
    Queue::fake();

    // handle callback should not be called
    $this->mock(\App\Services\PaymentGatewayService::class, function ($mock) {
        $mock->shouldReceive('handleCallback')->never();
    });

    $response = $this->postJson(route($this->callbackRoute, [
        'provider' => 'payex',
        'payment_gateway_log' => $payment_gateway_log->id,
        'token' => $payment_gateway_log->token
    ]), $failed_callback_data);

    expect($response->getContent())->toEqual('OK');

    Queue::assertNothingPushed();

    Queue::shouldReceive();

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
    ]);
    $this->assertDatabaseHas($this->walletTransactionTable, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::SUCCESS->value,
    ]);

    $this->assertDatabaseHas($this->paymentGatewayLogTable, [
        'id' => $payment_gateway_log->id,
        'status' => PaymentStatus::SUCCESS->value,
        'reference_id' => 'PX1381401a4ef5411392',
    ]);

    $this->assertDatabaseHas($this->walletTable, [
        'id' => $this->wallet->id,
        'balance' => 11.05,
    ]);

    $this->assertDatabaseCount(Payment::class, 1);


});



test('callback for wallet transaction - success then success again', function () {
    $payex_transaction_url = $this->payex_base_url . '/' . config('services.payment_gateway.payex.transaction_url');

    // callback data pointing to same payment intent and order id
    $success_callback_data = [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_9_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "PX1381401a4ef5411392",
        "external_txn_id" => "****************",
        "response" => "",
        "auth_code" => "00", // This indicates that the transaction is successful
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ];

    Http::fake([
        $this->payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_transaction_url . 'PX1381401a4ef5411392' => Http::response(
            [
                [
                    "txn_date" => "**************",
                    "mid" => "1381401",
                    "name" => "Skribble Lab Sdn Bhd",
                    "title" => "Default",
                    "collection_id" => "dJyEd14i",
                    "invoice_id" => "-",
                    "txn_id" => "PX1381401a4ef5411392",
                    "status" => "Failed",
                    "customer_name" => "Superadmin",
                    "currency" => "MYR",
                    "base_amount" => 1.05,
                    "amount" => 1.05,
                    "amount_refunded" => 0.0,
                    "txn_type" => "FPX",
                    "fpx_mode" => "01",
                    "fpx_buyer_name" => "N@me()/ .-_,&Buyer'`~*;:",
                    "fpx_buyer_bank_id" => "ABB0234",
                    "fpx_buyer_bank_name" => "Affin B2C - Test ID",
                    "card_holder_name" => null,
                    "card_number" => null,
                    "card_expiry" => null,
                    "card_brand" => "FPX",
                    "external_txn_id" => "****************",
                    "response" => "Invalid Transaction",
                    "auth_code" => "12",
                    "auth_number" => "********",
                    "settlement_id" => null,
                    "settlement_date" => null,
                    "email" => "<EMAIL>",
                    "contact_number" => null,
                    "address" => null,
                    "postcode" => null,
                    "city" => null,
                    "state" => null,
                    "country" => null,
                    "shipping_name" => "Superadmin",
                    "shipping_email" => "<EMAIL>",
                    "shipping_contact_number" => null,
                    "shipping_address" => null,
                    "shipping_postcode" => null,
                    "shipping_city" => null,
                    "shipping_state" => null,
                    "shipping_country" => null,
                    "description" => null,
                    "reference_number" => "1716982240_9_test",
                    "mandate_reference_number" => null,
                    "collection_number" => null,
                    "collection_reference_number" => null,
                    "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
                    "split_amount" => null,
                    "split_rule" => null,
                    "split_type" => null,
                    "split_description" => null,
                    "items" => [],
                    "metadata" => [],
                    "voidable" => "-",
                ]
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);


    $wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $this->wallet->id,
        'status' => WalletTransactionStatus::PENDING,
        'total_amount' => 1.05,
        'balance_before' => 10,
        'balance_after' => 11.05
    ]);

    $invoice = BillingDocument::factory()->create([
        'reference_no' => 'INV-00000000001',
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    $line_item = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_type' => get_class($wallet_transaction),
        'billable_item_id' => $wallet_transaction->id,
    ]);

    $payment_gateway_log = PaymentGatewayLog::factory()->create([
        'amount' => 1.05,
        'order_id' => '1716982240_9_test',
        'billing_document_id' => $invoice->id,
        'status' => PaymentStatus::PENDING,
        'transaction_loggable_type' => get_class($wallet_transaction),
        'transaction_loggable_id' => $wallet_transaction->id,
        'payment_method_id' => PaymentMethod::where('code', \App\Models\PaymentMethod::CODE_FPX)->firstOrFail()->id,
    ]);


    //Test with success response first
    Queue::fake();

    // Test with valid provider and success callback
    $this->postJson(route($this->callbackRoute, [
        'provider' => \App\Enums\PaymentProvider::PAYEX->value,
        'payment_gateway_log' => $payment_gateway_log->id,
        'token' => $payment_gateway_log->token
    ]), $success_callback_data);

    Queue::assertPushedOn(
        'event-listeners',
        CallQueuedListener::class,
        function ($job) {
            return $job->class === \App\Listeners\MarkBillableItemAsPaid::class;
        }
    );

    Queue::shouldReceive();

    // trigger event listener
    $event = new \App\Events\InvoicePaidEvent($invoice);
    $listener = new \App\Listeners\MarkBillableItemAsPaid();

    $listener->handle($event);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
    ]);
    $this->assertDatabaseHas($this->walletTransactionTable, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::SUCCESS->value,
    ]);

    $this->assertDatabaseHas($this->paymentGatewayLogTable, [
        'id' => $payment_gateway_log->id,
        'status' => PaymentStatus::SUCCESS->value,
        'reference_id' => 'PX1381401a4ef5411392',
    ]);

    $this->assertDatabaseHas($this->walletTable, [
        'id' => $this->wallet->id,
        'balance' => 11.05,
    ]);

    $this->assertDatabaseHas(Payment::class, [
        'billing_document_id' => $invoice->id,
        'amount_received' => 1.05,
        'payment_method_id' => PaymentMethod::where('code', \App\Models\PaymentMethod::CODE_FPX)->firstOrFail()->id,
        'payment_source_id' => $payment_gateway_log->id,
        'payment_source_type' => get_class($payment_gateway_log),
        'payment_reference_no' => 'PX1381401a4ef5411392',
    ]);

    // then receive success response again
    Queue::fake();

    // handle callback should not be called
    $this->mock(\App\Services\PaymentGatewayService::class, function ($mock) {
        $mock->shouldReceive('handleCallback')->never();
    });

    $response = $this->postJson(route($this->callbackRoute, [
        'provider' => 'payex',
        'payment_gateway_log' => $payment_gateway_log->id,
        'token' => $payment_gateway_log->token
    ]), $success_callback_data);

    expect($response->getContent())->toEqual('OK');

    Queue::assertNothingPushed();

    Queue::shouldReceive();

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
    ]);
    $this->assertDatabaseHas($this->walletTransactionTable, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::SUCCESS->value,
    ]);

    $this->assertDatabaseHas($this->paymentGatewayLogTable, [
        'id' => $payment_gateway_log->id,
        'status' => PaymentStatus::SUCCESS->value,
        'reference_id' => 'PX1381401a4ef5411392',
    ]);

    $this->assertDatabaseHas($this->walletTable, [
        'id' => $this->wallet->id,
        'balance' => 11.05,
    ]);

    $this->assertDatabaseCount(Payment::class, 1);

});

test('callback for wallet transaction - failed then success', function () {
    $payex_transaction_url = $this->payex_base_url . '/' . config('services.payment_gateway.payex.transaction_url');

    // callback data pointing to same payment intent and order id
    $failed_callback_data = [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_9_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "PX1381401a4ef5411391",
        "external_txn_id" => "****************",
        "response" => "Invalid+Transaction",
        "auth_code" => "12", // This indicates that the transaction is failed
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ];

    $success_callback_data = [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_9_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "PX1381401a4ef5411392",
        "external_txn_id" => "****************",
        "response" => "",
        "auth_code" => "00", // This indicates that the transaction is successful
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ];

    Http::fake([
        $this->payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_transaction_url . 'PX1381401a4ef5411392' => Http::response(
            [
                [
                    "txn_date" => "**************",
                    "mid" => "1381401",
                    "name" => "Skribble Lab Sdn Bhd",
                    "title" => "Default",
                    "collection_id" => "dJyEd14i",
                    "invoice_id" => "-",
                    "txn_id" => "PX1381401a4ef5411392",
                    "status" => "Failed",
                    "customer_name" => "Superadmin",
                    "currency" => "MYR",
                    "base_amount" => 1.05,
                    "amount" => 1.05,
                    "amount_refunded" => 0.0,
                    "txn_type" => "FPX",
                    "fpx_mode" => "01",
                    "fpx_buyer_name" => "N@me()/ .-_,&Buyer'`~*;:",
                    "fpx_buyer_bank_id" => "ABB0234",
                    "fpx_buyer_bank_name" => "Affin B2C - Test ID",
                    "card_holder_name" => null,
                    "card_number" => null,
                    "card_expiry" => null,
                    "card_brand" => "FPX",
                    "external_txn_id" => "****************",
                    "response" => "Invalid Transaction",
                    "auth_code" => "12",
                    "auth_number" => "********",
                    "settlement_id" => null,
                    "settlement_date" => null,
                    "email" => "<EMAIL>",
                    "contact_number" => null,
                    "address" => null,
                    "postcode" => null,
                    "city" => null,
                    "state" => null,
                    "country" => null,
                    "shipping_name" => "Superadmin",
                    "shipping_email" => "<EMAIL>",
                    "shipping_contact_number" => null,
                    "shipping_address" => null,
                    "shipping_postcode" => null,
                    "shipping_city" => null,
                    "shipping_state" => null,
                    "shipping_country" => null,
                    "description" => null,
                    "reference_number" => "1716982240_9_test",
                    "mandate_reference_number" => null,
                    "collection_number" => null,
                    "collection_reference_number" => null,
                    "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
                    "split_amount" => null,
                    "split_rule" => null,
                    "split_type" => null,
                    "split_description" => null,
                    "items" => [],
                    "metadata" => [],
                    "voidable" => "-",
                ]
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);


    $wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $this->wallet->id,
        'status' => WalletTransactionStatus::PENDING,
        'total_amount' => 1.05,
        'balance_before' => 10,
        'balance_after' => 11.05
    ]);

    $invoice = BillingDocument::factory()->create([
        'reference_no' => 'INV-00000000001',
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    $line_item = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_type' => get_class($wallet_transaction),
        'billable_item_id' => $wallet_transaction->id,
    ]);

    $payment_gateway_log = PaymentGatewayLog::factory()->create([
        'amount' => 1.05,
        'order_id' => '1716982240_9_test',
        'billing_document_id' => $invoice->id,
        'status' => PaymentStatus::PENDING,
        'transaction_loggable_type' => get_class($wallet_transaction),
        'transaction_loggable_id' => $wallet_transaction->id,
        'payment_method_id' => PaymentMethod::where('code', \App\Models\PaymentMethod::CODE_FPX)->firstOrFail()->id,
    ]);

    Queue::fake();

    $response = $this->postJson(route($this->callbackRoute, [
        'provider' => 'payex',
        'payment_gateway_log' => $payment_gateway_log->id,
        'token' => $payment_gateway_log->token
    ]), $failed_callback_data);

    expect($response->getContent())->toEqual('OK');

    Queue::assertPushedOn(
        'event-listeners',
        CallQueuedListener::class,
        function ($job) {
            return $job->class === \App\Listeners\InvoiceVoidedCallback::class;
        }
    );

    Queue::shouldReceive();

    // trigger event listener
    $event = new \App\Events\InvoiceVoidedEvent($invoice);
    $listener = new \App\Listeners\InvoiceVoidedCallback();

    $listener->handle($event);


    $this->assertDatabaseHas($this->walletTransactionTable, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::FAILED->value,
    ]);

    $this->assertDatabaseHas($this->paymentGatewayLogTable, [
        'id' => $payment_gateway_log->id,
        'status' => PaymentStatus::FAILED->value,
    ]);

    // wallet balance no change
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $this->wallet->id,
        'balance' => 10,
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'status' => BillingDocument::STATUS_VOIDED,
    ]);

    $this->assertDatabaseEmpty(Payment::class);

    //Test with success response after failed response
    Queue::fake();

    // Test with valid provider and success callback
    $this->postJson(route($this->callbackRoute, [
        'provider' => \App\Enums\PaymentProvider::PAYEX->value,
        'payment_gateway_log' => $payment_gateway_log->id,
        'token' => $payment_gateway_log->token
    ]), $success_callback_data);

    Queue::assertNothingPushed();

    Queue::shouldReceive();

    $this->assertDatabaseHas($this->walletTransactionTable, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::FAILED->value,
    ]);

    $this->assertDatabaseHas($this->paymentGatewayLogTable, [
        'id' => $payment_gateway_log->id,
        'status' => PaymentStatus::FAILED->value,
    ]);

    // wallet balance no change
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $this->wallet->id,
        'balance' => 10,
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'status' => BillingDocument::STATUS_VOIDED,
    ]);

    $this->assertDatabaseEmpty(Payment::class);

});


test('callback for wallet transaction - failed then failed again', function () {
    $payex_transaction_url = $this->payex_base_url . '/' . config('services.payment_gateway.payex.transaction_url');

    // callback data pointing to same payment intent and order id
    $failed_callback_data = [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_9_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "PX1381401a4ef5411391",
        "external_txn_id" => "****************",
        "response" => "Invalid+Transaction",
        "auth_code" => "12", // This indicates that the transaction is failed
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ];

    Http::fake([
        $this->payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_transaction_url . 'PX1381401a4ef5411392' => Http::response(
            [
                [
                    "txn_date" => "**************",
                    "mid" => "1381401",
                    "name" => "Skribble Lab Sdn Bhd",
                    "title" => "Default",
                    "collection_id" => "dJyEd14i",
                    "invoice_id" => "-",
                    "txn_id" => "PX1381401a4ef5411392",
                    "status" => "Failed",
                    "customer_name" => "Superadmin",
                    "currency" => "MYR",
                    "base_amount" => 1.05,
                    "amount" => 1.05,
                    "amount_refunded" => 0.0,
                    "txn_type" => "FPX",
                    "fpx_mode" => "01",
                    "fpx_buyer_name" => "N@me()/ .-_,&Buyer'`~*;:",
                    "fpx_buyer_bank_id" => "ABB0234",
                    "fpx_buyer_bank_name" => "Affin B2C - Test ID",
                    "card_holder_name" => null,
                    "card_number" => null,
                    "card_expiry" => null,
                    "card_brand" => "FPX",
                    "external_txn_id" => "****************",
                    "response" => "Invalid Transaction",
                    "auth_code" => "12",
                    "auth_number" => "********",
                    "settlement_id" => null,
                    "settlement_date" => null,
                    "email" => "<EMAIL>",
                    "contact_number" => null,
                    "address" => null,
                    "postcode" => null,
                    "city" => null,
                    "state" => null,
                    "country" => null,
                    "shipping_name" => "Superadmin",
                    "shipping_email" => "<EMAIL>",
                    "shipping_contact_number" => null,
                    "shipping_address" => null,
                    "shipping_postcode" => null,
                    "shipping_city" => null,
                    "shipping_state" => null,
                    "shipping_country" => null,
                    "description" => null,
                    "reference_number" => "1716982240_9_test",
                    "mandate_reference_number" => null,
                    "collection_number" => null,
                    "collection_reference_number" => null,
                    "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
                    "split_amount" => null,
                    "split_rule" => null,
                    "split_type" => null,
                    "split_description" => null,
                    "items" => [],
                    "metadata" => [],
                    "voidable" => "-",
                ]
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);


    $wallet_transaction = WalletTransaction::factory()->create([
        'wallet_id' => $this->wallet->id,
        'status' => WalletTransactionStatus::PENDING,
        'total_amount' => 1.05,
        'balance_before' => 10,
        'balance_after' => 11.05
    ]);

    $invoice = BillingDocument::factory()->create([
        'reference_no' => 'INV-00000000001',
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    $line_item = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_type' => get_class($wallet_transaction),
        'billable_item_id' => $wallet_transaction->id,
    ]);

    $payment_gateway_log = PaymentGatewayLog::factory()->create([
        'amount' => 1.05,
        'order_id' => '1716982240_9_test',
        'billing_document_id' => $invoice->id,
        'status' => PaymentStatus::PENDING,
        'transaction_loggable_type' => get_class($wallet_transaction),
        'transaction_loggable_id' => $wallet_transaction->id,
        'payment_method_id' => PaymentMethod::where('code', \App\Models\PaymentMethod::CODE_FPX)->firstOrFail()->id,
    ]);

    Queue::fake();

    $response = $this->postJson(route($this->callbackRoute, [
        'provider' => 'payex',
        'payment_gateway_log' => $payment_gateway_log->id,
        'token' => $payment_gateway_log->token
    ]), $failed_callback_data);

    expect($response->getContent())->toEqual('OK');

    Queue::assertPushedOn(
        'event-listeners',
        CallQueuedListener::class,
        function ($job) {
            return $job->class === \App\Listeners\InvoiceVoidedCallback::class;
        }
    );

    Queue::shouldReceive();

    // trigger event listener
    $event = new \App\Events\InvoiceVoidedEvent($invoice);
    $listener = new \App\Listeners\InvoiceVoidedCallback();

    $listener->handle($event);

    $this->assertDatabaseHas($this->walletTransactionTable, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::FAILED->value,
    ]);

    $this->assertDatabaseHas($this->paymentGatewayLogTable, [
        'id' => $payment_gateway_log->id,
        'status' => PaymentStatus::FAILED->value,
    ]);

    // wallet balance no change
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $this->wallet->id,
        'balance' => 10,
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'status' => BillingDocument::STATUS_VOIDED,
    ]);

    $this->assertDatabaseEmpty(Payment::class);

    //Test with success response after failed response
    Queue::fake();

    // Test with valid provider and success callback
    $this->postJson(route($this->callbackRoute, [
        'provider' => \App\Enums\PaymentProvider::PAYEX->value,
        'payment_gateway_log' => $payment_gateway_log->id,
        'token' => $payment_gateway_log->token
    ]), $failed_callback_data);

    Queue::assertNothingPushed();

    Queue::shouldReceive();

    $this->assertDatabaseHas($this->walletTransactionTable, [
        'id' => $wallet_transaction->id,
        'status' => WalletTransactionStatus::FAILED->value,
    ]);

    $this->assertDatabaseHas($this->paymentGatewayLogTable, [
        'id' => $payment_gateway_log->id,
        'status' => PaymentStatus::FAILED->value,
    ]);

    // wallet balance no change
    $this->assertDatabaseHas($this->walletTable, [
        'id' => $this->wallet->id,
        'balance' => 10,
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
        'status' => BillingDocument::STATUS_VOIDED,
    ]);

    $this->assertDatabaseEmpty(Payment::class);

});



test('callback with invalid token', function () {
    $response = $this->postJson(route($this->callbackRoute, [
        'provider' => 'payex',
        'payment_gateway_log' => PaymentGatewayLog::factory()->create(),
        'token' => 'invalid_token'
    ]))->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response)->toMatchArray([
            'code' => 2001,
            'error' => 'Transaction record not found.'
        ]);
});

test('handleCallback for enrollment', function () {
    Config::create([
        'key' => Config::ENROLLMENT_FEES,
        'category' => Config::CATEGORY_GENERAL,
        'value' => 500
    ]);

    $guardian = Guardian::factory()->hasUser()->create();

    $payex_transaction_url = $this->payex_base_url . '/' . config('services.payment_gateway.payex.transaction_url');

    $failed_callback_data = [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_8_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "PX1381401a4ef5411392",
        "external_txn_id" => "****************",
        "response" => "Invalid+Transaction",
        "auth_code" => "12", // This indicates that the transaction is failed
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ];

    $success_callback_data = [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_9_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "SUCCESS1381401a4ef5411392",
        "external_txn_id" => "****************",
        "response" => "",
        "auth_code" => "00", // This indicates that the transaction is successful
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ];

    $pending_callback_data = [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_10_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "PENDING1381401a4ef5411392",
        "external_txn_id" => "****************",
        "response" => "",
        "auth_code" => "09", // Status 09 will be treated as pending
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ];

    Http::fake([
        $this->payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_transaction_url . $failed_callback_data['txn_id'] => Http::response(
            [
                [
                    "txn_date" => "**************",
                    "mid" => "1381401",
                    "name" => "Skribble Lab Sdn Bhd",
                    "title" => "Default",
                    "collection_id" => "dJyEd14i",
                    "invoice_id" => "-",
                    "txn_id" => "PX1381401a4ef5411392",
                    "status" => "Failed",
                    "customer_name" => "Superadmin",
                    "currency" => "MYR",
                    "base_amount" => 500,
                    "amount" => 500,
                    "amount_refunded" => 0.0,
                    "txn_type" => "FPX",
                    "fpx_mode" => "01",
                    "fpx_buyer_name" => "N@me()/ .-_,&Buyer'`~*;:",
                    "fpx_buyer_bank_id" => "ABB0234",
                    "fpx_buyer_bank_name" => "Affin B2C - Test ID",
                    "card_holder_name" => null,
                    "card_number" => null,
                    "card_expiry" => null,
                    "card_brand" => "FPX",
                    "external_txn_id" => "****************",
                    "response" => "Invalid Transaction",
                    "auth_code" => "12",
                    "auth_number" => "********",
                    "settlement_id" => null,
                    "settlement_date" => null,
                    "email" => "<EMAIL>",
                    "contact_number" => null,
                    "address" => null,
                    "postcode" => null,
                    "city" => null,
                    "state" => null,
                    "country" => null,
                    "shipping_name" => "Superadmin",
                    "shipping_email" => "<EMAIL>",
                    "shipping_contact_number" => null,
                    "shipping_address" => null,
                    "shipping_postcode" => null,
                    "shipping_city" => null,
                    "shipping_state" => null,
                    "shipping_country" => null,
                    "description" => null,
                    "reference_number" => "1716982240_8_test",
                    "mandate_reference_number" => null,
                    "collection_number" => null,
                    "collection_reference_number" => null,
                    "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
                    "split_amount" => null,
                    "split_rule" => null,
                    "split_type" => null,
                    "split_description" => null,
                    "items" => [],
                    "metadata" => [],
                    "voidable" => "-",
                ]
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_transaction_url . $success_callback_data['txn_id'] => Http::response(
            [
                [
                    "txn_date" => "**************",
                    "mid" => "1381401",
                    "name" => "Skribble Lab Sdn Bhd",
                    "title" => "Default",
                    "collection_id" => "dJyEd14i",
                    "invoice_id" => "-",
                    "txn_id" => "SUCCESS1381401a4ef5411392",
                    "status" => "Pending",
                    "customer_name" => "Superadmin",
                    "currency" => "MYR",
                    "base_amount" => 500,
                    "amount" => 500,
                    "amount_refunded" => 0.0,
                    "txn_type" => "FPX",
                    "fpx_mode" => "01",
                    "fpx_buyer_name" => "N@me()/ .-_,&Buyer'`~*;:",
                    "fpx_buyer_bank_id" => "ABB0234",
                    "fpx_buyer_bank_name" => "Affin B2C - Test ID",
                    "card_holder_name" => null,
                    "card_number" => null,
                    "card_expiry" => null,
                    "card_brand" => "FPX",
                    "external_txn_id" => "****************",
                    "response" => "",
                    "auth_code" => "00",
                    "auth_number" => "********",
                    "settlement_id" => null,
                    "settlement_date" => null,
                    "email" => "<EMAIL>",
                    "contact_number" => null,
                    "address" => null,
                    "postcode" => null,
                    "city" => null,
                    "state" => null,
                    "country" => null,
                    "shipping_name" => "Superadmin",
                    "shipping_email" => "<EMAIL>",
                    "shipping_contact_number" => null,
                    "shipping_address" => null,
                    "shipping_postcode" => null,
                    "shipping_city" => null,
                    "shipping_state" => null,
                    "shipping_country" => null,
                    "description" => null,
                    "reference_number" => "1716982240_9_test",
                    "mandate_reference_number" => null,
                    "collection_number" => null,
                    "collection_reference_number" => null,
                    "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
                    "split_amount" => null,
                    "split_rule" => null,
                    "split_type" => null,
                    "split_description" => null,
                    "items" => [],
                    "metadata" => [],
                    "voidable" => "-",
                ]
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_transaction_url . $pending_callback_data['txn_id'] => Http::response(
            [
                [
                    "txn_date" => "**************",
                    "mid" => "1381401",
                    "name" => "Skribble Lab Sdn Bhd",
                    "title" => "Default",
                    "collection_id" => "dJyEd14i",
                    "invoice_id" => "-",
                    "txn_id" => "PENDING1381401a4ef5411392",
                    "status" => "Pending",
                    "customer_name" => "Superadmin",
                    "currency" => "MYR",
                    "base_amount" => 500,
                    "amount" => 500,
                    "amount_refunded" => 0.0,
                    "txn_type" => "FPX",
                    "fpx_mode" => "01",
                    "fpx_buyer_name" => "N@me()/ .-_,&Buyer'`~*;:",
                    "fpx_buyer_bank_id" => "ABB0234",
                    "fpx_buyer_bank_name" => "Affin B2C - Test ID",
                    "card_holder_name" => null,
                    "card_number" => null,
                    "card_expiry" => null,
                    "card_brand" => "FPX",
                    "external_txn_id" => "****************",
                    "response" => "",
                    "auth_code" => "09",
                    "auth_number" => "********",
                    "settlement_id" => null,
                    "settlement_date" => null,
                    "email" => "<EMAIL>",
                    "contact_number" => null,
                    "address" => null,
                    "postcode" => null,
                    "city" => null,
                    "state" => null,
                    "country" => null,
                    "shipping_name" => "Superadmin",
                    "shipping_email" => "<EMAIL>",
                    "shipping_contact_number" => null,
                    "shipping_address" => null,
                    "shipping_postcode" => null,
                    "shipping_city" => null,
                    "shipping_state" => null,
                    "shipping_country" => null,
                    "description" => null,
                    "reference_number" => "1716982240_10_test",
                    "mandate_reference_number" => null,
                    "collection_number" => null,
                    "collection_reference_number" => null,
                    "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
                    "split_amount" => null,
                    "split_rule" => null,
                    "split_type" => null,
                    "split_description" => null,
                    "items" => [],
                    "metadata" => [],
                    "voidable" => "-",
                ]
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);

    $invoice = BillingDocument::factory()->create([
        'reference_no' => 'INV-00000000001',
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    // Success Scenario
    $success_enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user->id,
        'status' => EnrollmentStatus::PENDING_PAYMENT
    ]);

    $success_payment_gateway_log = PaymentGatewayLog::factory()->create([
        'status' => PaymentStatus::PENDING,
        'billing_document_id' => $invoice->id,
        'transaction_loggable_type' => get_class($success_enrollment),
        'transaction_loggable_id' => $success_enrollment->id,
        'amount' => 500,
        'order_id' => $success_callback_data['reference_number'],
        'payment_method_id' => PaymentMethod::where('code', \App\Models\PaymentMethod::CODE_FPX)->firstOrFail()->id,
    ]);

    // Test with valid provider and success callback
    $line_item = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_type' => get_class($success_enrollment),
        'billable_item_id' => $success_enrollment->id,
    ]);

    Queue::fake();

    $this->postJson(route($this->callbackRoute, [
        'provider' => 'payex',
        'payment_gateway_log' => $success_payment_gateway_log->id,
        'token' => $success_payment_gateway_log->token
    ]), $success_callback_data);

    Queue::assertPushedOn(
        'event-listeners',
        CallQueuedListener::class,
        function ($job) {
            return $job->class === \App\Listeners\MarkBillableItemAsPaid::class;
        }
    );

    Queue::shouldReceive();

    // trigger event listener
    $event = new \App\Events\InvoicePaidEvent($invoice);
    $listener = new \App\Listeners\MarkBillableItemAsPaid();

    $listener->handle($event);

    $this->assertDatabaseHas('enrollments', [
        'id' => $success_enrollment->id,
        'status' => EnrollmentStatus::SUBMITTED->value
    ]);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'id' => $success_payment_gateway_log->id,
        'status' => PaymentStatus::SUCCESS->value
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
    ]);

    $this->assertDatabaseHas(Payment::class, [
        'billing_document_id' => $invoice->id,
        'amount_received' => $success_payment_gateway_log->amount,
        'payment_method_id' => PaymentMethod::where('code', \App\Models\PaymentMethod::CODE_FPX)->firstOrFail()->id,
        'payment_source_id' => $success_payment_gateway_log->id,
        'payment_source_type' => get_class($success_payment_gateway_log),
    ]);


    // Fail Scenario
    BillingDocumentLineItem::truncate();
    BillingDocument::truncate();
    Payment::truncate();

    $invoice = BillingDocument::factory()->create([
        'reference_no' => 'INV-00000000002',
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    $fail_enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user->id,
        'status' => EnrollmentStatus::PENDING_PAYMENT
    ]);

    $line_item = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_type' => get_class($fail_enrollment),
        'billable_item_id' => $fail_enrollment->id,
    ]);

    $fail_payment_gateway_log = PaymentGatewayLog::factory()->create([
        'status' => PaymentStatus::PENDING,
        'billing_document_id' => $invoice->id,
        'transaction_loggable_type' => get_class($fail_enrollment),
        'transaction_loggable_id' => $fail_enrollment->id,
        'amount' => 500,
        'order_id' => $failed_callback_data['reference_number'],
    ]);

    $this->postJson(route($this->callbackRoute, [
        'provider' => 'payex',
        'payment_gateway_log' => $fail_payment_gateway_log->id,
        'token' => $fail_payment_gateway_log->token
    ]), $failed_callback_data);

    $this->assertDatabaseHas('enrollments', [
        'id' => $fail_enrollment->id,
        'status' => EnrollmentStatus::PAYMENT_FAILED->value
    ]);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'id' => $fail_payment_gateway_log->id,
        'status' => PaymentStatus::FAILED->value
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    $this->assertDatabaseEmpty(Payment::class);

    // Pending Scenario
    BillingDocumentLineItem::truncate();
    BillingDocument::truncate();
    Payment::truncate();

    $invoice = BillingDocument::factory()->create([
        'reference_no' => 'INV-00000000003',
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    $pending_enrollment = Enrollment::factory()->create([
        'created_by' => $guardian->user->id,
        'status' => EnrollmentStatus::PENDING_PAYMENT
    ]);

    $pending_payment_gateway_log = PaymentGatewayLog::factory()->create([
        'status' => PaymentStatus::PENDING,
        'billing_document_id' => $invoice->id,
        'transaction_loggable_type' => get_class($pending_enrollment),
        'transaction_loggable_id' => $pending_enrollment->id,
        'amount' => 500,
        'order_id' => $pending_callback_data['reference_number'],
    ]);

    $line_item = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_type' => get_class($pending_enrollment),
        'billable_item_id' => $pending_enrollment->id,
    ]);

    $this->postJson(route($this->callbackRoute, [
        'provider' => 'payex',
        'payment_gateway_log' => $pending_payment_gateway_log->id,
        'token' => $pending_payment_gateway_log->token
    ]), $pending_callback_data);

    $this->assertDatabaseHas(BillingDocument::class, [
        'id' => $invoice->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    $this->assertDatabaseHas('enrollments', [
        'id' => $pending_enrollment->id,
        'status' => EnrollmentStatus::PENDING_PAYMENT->value
    ]);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'id' => $pending_payment_gateway_log->id,
        'status' => PaymentStatus::PENDING->value
    ]);

    $this->assertDatabaseEmpty(Payment::class);
});

test('router without redirect_url', function() {

    $response = $this->postJson(route($this->returnRoute), [
        "amount" => "1.05",
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_9_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "SUCCESS1381401a4ef5411392",
        "external_txn_id" => "****************",
        "response" => "",
        "auth_code" => "00", // This indicates that the transaction is successful
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ]);

    expect($response->getStatusCode())->toBe(200)
        ->and($response->getContent())->toBe('OK');

});


test('router with redirect_url', function() {

    $response = $this->postJson(route($this->returnRoute), [
        "amount" => "1.05",
        "redirect_url" => 'https://google.com',
        "currency" => "MYR",
        "customer_name" => "Superadmin",
        "description" => "",
        "reference_number" => "1716982240_9_test",
        "mandate_reference_number" => "",
        "payment_intent" => "1df90b1dba2247e4af602144b8bf7e34",
        "collection_id" => "dJyEd14i",
        "invoice_id" => "",
        "txn_id" => "SUCCESS1381401a4ef5411392",
        "external_txn_id" => "****************",
        "response" => "",
        "auth_code" => "00", // This indicates that the transaction is successful
        "auth_number" => "********",
        "txn_date" => "**************",
        "fpx_mode" => "01",
        "fpx_buyer_name" => "N%40me%28%29%2F+.-_%2C%26Buyer%27%60%7E*%3B%3A",
        "fpx_buyer_bank_id" => "ABB0234",
        "fpx_buyer_bank_name" => "Affin+B2C+-+Test+ID",
        "card_holder_name" => "",
        "card_number" => "",
        "card_expiry" => "",
        "card_brand" => "FPX",
        "card_issuer" => "",
        "card_on_file" => "",
        "signature" => "63f3442d39145b6cc283fddca17d7083a2327cb51371d3a34a0bec0b4c1cce344b2e3af692d673f46fc75d6dd67071b1cf2d979c236c0baa4b6863f63c22da1f",
        "txn_type" => "FPX",
        "nonce" => "Tfu2SheCHGiprSj1j5iDoyVPxJAePSbUe3DPRLygc35lWhj4qiZGnmcN3Ny1Qtgh",
        "metadata" => "%7B%7D",
    ]);

    expect($response->getStatusCode())->toBe(302);

    $response->assertRedirect('https://google.com');

});
