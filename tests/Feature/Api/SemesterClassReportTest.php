<?php

use App\Enums\ExportType;
use App\Enums\GuardianType;
use App\Models\ClassModel;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\School;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Database\Seeders\CurrencySeeder;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seedAccountingData();
    $this->seed([
        InternationalizationSeeder::class,
        CurrencySeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->routeNamePrefix = 'reports.semester-classes';

    //prepare data
    $this->school = School::factory()->create();
    $this->students = Student::factory(5)->state(new Sequence(
        [
            'name->en' => 'Student 1',
            'primary_school_id' => $this->school->id,
        ],
        [
            'name->en' => 'Student 2',
            'primary_school_id' => null,
        ],
        [
            'name->en' => 'Student 3',
        ],
        [
            'name->en' => 'Student 4',
        ],
        [
            'name->en' => 'Student 5',
        ],
    ))->create();

    $this->guardians = Guardian::factory(8)->state(new Sequence(
        [
            'name->en' => 'Father',
        ],
        [
            'name->en' => 'Mother',
        ],
        [
            'name->en' => 'Father',
        ],
        [
            'name->en' => 'Father',
        ],
        [
            'name->en' => 'Father',
        ],
        [
            'name->en' => 'Father',
        ],
        [
            'name->en' => 'Guardian 1',
        ],
        [
            'name->en' => 'Guardian 2',
        ]
    ))->create();

    GuardianStudent::factory(8)->state(new Sequence(
        [
            'type' => GuardianType::FATHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[0]->id,
            'guardian_id' => $this->guardians[0]->id,
        ],
        [
            'type' => GuardianType::MOTHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[0]->id,
            'guardian_id' => $this->guardians[1]->id,
        ],
        [
            'type' => GuardianType::GUARDIAN,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[0]->id,
            'guardian_id' => $this->guardians[6]->id,
        ],
        [
            'type' => GuardianType::GUARDIAN,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[0]->id,
            'guardian_id' => $this->guardians[7]->id,
        ],
        [
            'type' => GuardianType::FATHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[1]->id,
            'guardian_id' => $this->guardians[2]->id,
        ],
        [
            'type' => GuardianType::FATHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[2]->id,
            'guardian_id' => $this->guardians[3]->id,
        ],
        [
            'type' => GuardianType::FATHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[3]->id,
            'guardian_id' => $this->guardians[4]->id,
        ],
        [
            'type' => GuardianType::FATHER,
            'studenable_type' => Student::class,
            'studenable_id' => $this->students[4]->id,
            'guardian_id' => $this->guardians[5]->id,
        ],
    ))->create();

    $this->grades = Grade::factory(3)->create();
    $this->classes = ClassModel::factory(3)->state(new Sequence(
        [
            'name' => 'J111',
            'grade_id' => $this->grades[0]->id,
        ],
        [
            'name' => 'J112',
            'grade_id' => $this->grades[1]->id,
        ],
        [
            'name' => 'J222',
            'grade_id' => $this->grades[2]->id,
        ],
    ))->create();

    $this->semester_year_setting = SemesterYearSetting::factory()->create(['year' => 2024]);

    $this->semester_settings = SemesterSetting::factory(2)
        ->state(new Sequence(
            [
                'name' => 'Semester 1',
                'semester_year_setting_id' => $this->semester_year_setting->id,
                'is_current_semester' => true
            ],
            [
                'name' => 'Semester 2',
                'semester_year_setting_id' => $this->semester_year_setting->id,
                'is_current_semester' => true
            ],
        ))
        ->create();
    $this->employees = Employee::factory(3)->state(new Sequence(
        [
            'name->en' => 'Teacher 1',
        ],
        [
            'name->en' => 'Teacher 2',
        ],
        [
            'name->en' => 'Teacher 3',
        ]

    ))->create();
    $this->semester_classes = SemesterClass::factory(3)
        ->state(new Sequence(
            [
                'class_id' => $this->classes[0]->id,
                'semester_setting_id' => $this->semester_settings[0]->id,
                'homeroom_teacher_id' => $this->employees[0]->id,
            ],
            [
                'class_id' => $this->classes[1]->id,
                'semester_setting_id' => $this->semester_settings[0]->id,
                'homeroom_teacher_id' => $this->employees[1]->id,
            ],
            [
                'class_id' => $this->classes[2]->id,
                'semester_setting_id' => $this->semester_settings[1]->id,
                'homeroom_teacher_id' => $this->employees[2]->id,
            ],
        ))
        ->create();

    $this->student_classes = StudentClass::factory(5)->state(new Sequence(
        [
            'student_id' => $this->students[0]->id,
            'seat_no' => 1,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $this->semester_classes[0]->id,
        ],
        [
            'student_id' => $this->students[1]->id,
            'seat_no' => 2,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $this->semester_classes[0]->id,
        ],
        [
            'student_id' => $this->students[2]->id,
            'seat_no' => 3,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $this->semester_classes[1]->id,
        ],
        [
            'student_id' => $this->students[3]->id,
            'seat_no' => 4,
            'semester_setting_id' => $this->semester_settings[0]->id,
            'semester_class_id' => $this->semester_classes[1]->id,
        ],
        [
            'student_id' => $this->students[4]->id,
            'seat_no' => 5,
            'semester_setting_id' => $this->semester_settings[1]->id,
            'semester_class_id' => $this->semester_classes[2]->id,
        ],
    ))->create();

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');
    SnappyPdf::fake();
    Excel::fake();
});

test('reportStudentContacts return data', function () {
    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_settings[0]->id,
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.student-contacts', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['semester_name'])->toBe($this->semester_settings[0]->name)
        ->and($response['data']['classes'])->sequence(
            function ($data) {
                $data->toMatchArray([
                    'class_name' => $this->classes[0]->name,
                    'homeroom_teacher_name' => $this->employees[0]->getFormattedTranslations('name'),
                    'students' => [
                        [
                            'photo' => null,
                            'student_number' => $this->students[0]->student_number,
                            'seat_number' => $this->student_classes[0]->seat_no,
                            'name' => $this->students[0]->getFormattedTranslations('name'),
                            'gender' => $this->students[0]->gender->value,
                            'nric' => $this->students[0]->nric,
                            'address' => $this->students[0]->address,
                            'guardians' => [
                                [
                                    'name' => $this->guardians[0]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[0]->phone_number,
                                    'type' => GuardianType::FATHER->value,
                                ],
                                [
                                    'name' => $this->guardians[1]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[1]->phone_number,
                                    'type' => GuardianType::MOTHER->value,
                                ],
                                [
                                    'name' => $this->guardians[6]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[6]->phone_number,
                                    'type' => GuardianType::GUARDIAN->value,
                                ],
                                [
                                    'name' => $this->guardians[7]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[7]->phone_number,
                                    'type' => GuardianType::GUARDIAN->value,
                                ]
                            ]
                        ],
                        [
                            'photo' => null,
                            'student_number' => $this->students[1]->student_number,
                            'seat_number' => $this->student_classes[1]->seat_no,
                            'name' => $this->students[1]->getFormattedTranslations('name'),
                            'gender' => $this->students[1]->gender->value,
                            'nric' => $this->students[1]->nric,
                            'address' => $this->students[1]->address,
                            'guardians' => [
                                [
                                    'name' => $this->guardians[2]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[2]->phone_number,
                                    'type' => GuardianType::FATHER->value,
                                ]
                            ]
                        ]
                    ]
                ]);
            },
            function ($data) {
                $data->toMatchArray([
                    'class_name' => $this->classes[1]->name,
                    'homeroom_teacher_name' => $this->employees[1]->getFormattedTranslations('name'),
                    'students' => [
                        [
                            'photo' => null,
                            'student_number' => $this->students[2]->student_number,
                            'seat_number' => $this->student_classes[2]->seat_no,
                            'name' => $this->students[2]->getFormattedTranslations('name'),
                            'gender' => $this->students[2]->gender->value,
                            'nric' => $this->students[2]->nric,
                            'address' => $this->students[2]->address,
                            'guardians' => [
                                [
                                    'name' => $this->guardians[3]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[3]->phone_number,
                                    'type' => GuardianType::FATHER->value,
                                ]
                            ]
                        ],
                        [
                            'photo' => null,
                            'student_number' => $this->students[3]->student_number,
                            'seat_number' => $this->student_classes[3]->seat_no,
                            'name' => $this->students[3]->getFormattedTranslations('name'),
                            'gender' => $this->students[3]->gender->value,
                            'nric' => $this->students[3]->nric,
                            'address' => $this->students[3]->address,
                            'guardians' => [
                                [
                                    'name' => $this->guardians[4]->getFormattedTranslations('name'),
                                    'phone_number' => $this->guardians[4]->phone_number,
                                    'type' => GuardianType::FATHER->value,
                                ]
                            ]
                        ]
                    ]
                ]);
            }
        );
});

test('reportStudentContacts return pdf', function () {
    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_settings[0]->id,
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'semester-classes-report-student-contacts';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.student-contacts', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportStudentContacts return excel', function () {
    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_settings[0]->id,
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'semester-classes-report-student-contacts';
    $extension = '.xlsx';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.student-contacts', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportStudentDetails return data', function () {
    $filters = [
        'report_language' => 'en',
        'semester_class_id' => $this->semester_classes[0]->id,
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.student-details', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->sequence(
            function ($data) {
                $data->toMatchArray([
                    'photo' => $this->students[0]->photo,
                    'name' => $this->students[0]->getFormattedTranslations('name'),
                    'student_number' => $this->students[0]->student_number,
                    'address' => $this->students[0]->address,
                    'class_name' => $this->classes[0]->name,
                    'semester_name' => $this->semester_settings[0]->name,
                    'primary_school_name' => $this->school->name,
                    'date_of_birth' => $this->students[0]->date_of_birth,
                    'birthplace' => $this->students[0]->birthplace,
                    'religion' => $this->students[0]->religion->name,
                    'guardians' => [
                        GuardianType::FATHER->value => [
                            'name' => $this->guardians[0]->getFormattedTranslations('name'),
                            'nationality' => $this->guardians[0]->country?->name,
                            'email' => $this->guardians[0]->email,
                            'education' => $this->guardians[0]->education?->name,
                            'religion' => $this->guardians[0]->religion->name,
                            'phone_number' => $this->guardians[0]->phone_number,
                            'occupation' => $this->guardians[0]->occupation,
                        ],
                        GuardianType::MOTHER->value => [
                            'name' => $this->guardians[1]->getFormattedTranslations('name'),
                            'nationality' => $this->guardians[1]->country?->name,
                            'email' => $this->guardians[1]->email,
                            'education' => $this->guardians[1]->education?->name,
                            'religion' => $this->guardians[1]->religion->name,
                            'phone_number' => $this->guardians[1]->phone_number,
                            'occupation' => $this->guardians[1]->occupation,
                        ],
                        GuardianType::GUARDIAN->value => [
                            [
                                'name' => $this->guardians[6]->getFormattedTranslations('name'),
                                'nationality' => $this->guardians[6]->country?->name,
                                'email' => $this->guardians[6]->email,
                                'education' => $this->guardians[6]->education?->name,
                                'religion' => $this->guardians[6]->religion->name,
                                'phone_number' => $this->guardians[6]->phone_number,
                                'occupation' => $this->guardians[6]->occupation,
                            ],
                            [
                                'name' => $this->guardians[7]->getFormattedTranslations('name'),
                                'nationality' => $this->guardians[7]->country?->name,
                                'email' => $this->guardians[7]->email,
                                'education' => $this->guardians[7]->education?->name,
                                'religion' => $this->guardians[7]->religion->name,
                                'phone_number' => $this->guardians[7]->phone_number,
                                'occupation' => $this->guardians[7]->occupation,
                            ]
                        ],
                    ]
                ]);
            },
            function ($data) {
                $data->toMatchArray([
                    'photo' => $this->students[1]->photo,
                    'name' => $this->students[1]->getFormattedTranslations('name'),
                    'student_number' => $this->students[1]->student_number,
                    'address' => $this->students[1]->address,
                    'class_name' => $this->classes[0]->name,
                    'semester_name' => $this->semester_settings[0]->name,
                    'primary_school_name' => null,
                    'date_of_birth' => $this->students[1]->date_of_birth,
                    'birthplace' => $this->students[1]->birthplace,
                    'religion' => $this->students[1]->religion->name,
                    'guardians' => [
                        GuardianType::FATHER->value => [
                            'name' => $this->guardians[2]->getFormattedTranslations('name'),
                            'nationality' => $this->guardians[2]->country?->name,
                            'email' => $this->guardians[2]->email,
                            'education' => $this->guardians[2]->education?->name,
                            'religion' => $this->guardians[2]->religion->name,
                            'phone_number' => $this->guardians[2]->phone_number,
                            'occupation' => $this->guardians[2]->occupation,
                        ],
                        GuardianType::MOTHER->value => [
                            'name' => null,
                            'nationality' => null,
                            'email' => null,
                            'education' => null,
                            'religion' => null,
                            'phone_number' => null,
                            'occupation' => null,
                        ],
                        GuardianType::GUARDIAN->value => [],
                    ]
                ]);
            }
        );
});

test('reportStudentDetails return pdf', function () {
    $filters = [
        'report_language' => 'en',
        'semester_class_id' => $this->semester_classes[0]->id,
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'semester-classes-report-student-details';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.student-details', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportHomeroomTeachers return data', function () {
    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_settings[0]->id,
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.homeroom-teachers', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->sequence(
            function ($data) {
                $data->toMatchArray([
                    'class_name' => $this->classes[0]->name,
                    'class_code' => $this->classes[0]->code,
                    'semester_name' => $this->semester_settings[0]->name,
                    'grade' => $this->grades[0]->name,
                    'homeroom_teacher_name' => $this->employees[0]->getFormattedTranslations('name'),
                ]);
            },
            function ($data) {
                $data->toMatchArray([
                    'class_name' => $this->classes[1]->name,
                    'class_code' => $this->classes[1]->code,
                    'semester_name' => $this->semester_settings[0]->name,
                    'grade' => $this->grades[1]->name,
                    'homeroom_teacher_name' => $this->employees[1]->getFormattedTranslations('name'),
                ]);
            }
        );
});

test('reportHomeroomTeachers return pdf', function () {
    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_settings[0]->id,
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'semester-classes-report-homeroom-teachers';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.homeroom-teachers', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportHomeroomTeachers return excel', function () {
    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_settings[0]->id,
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'semester-classes-report-homeroom-teachers';
    $extension = '.xlsx';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.homeroom-teachers', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportByStudentsInSemesterClass return pdf', function () {
    $filters = [
        'report_language' => 'en',
        'semester_class_id' => $this->semester_classes[0]->id,
        'export_type' => ExportType::PDF->value,
    ];

    $filename = 'students-list';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.by-students-in-semester-class', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('reportByStudentsInSemesterClass return excel', function () {
    $filters = [
        'report_language' => 'en',
        'semester_class_id' => $this->semester_classes[0]->id,
        'export_type' => ExportType::EXCEL->value,
    ];

    $filename = 'students-list';
    $extension = '.xlsx';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.by-students-in-semester-class', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});
