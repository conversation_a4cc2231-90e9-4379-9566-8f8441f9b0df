<?php

use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Enums\StudentAdmissionType;
use App\Enums\Gender;
use App\Models\ClassModel;
use App\Models\Grade;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');
    Sanctum::actingAs($user);

    $this->routeNamePrefix = 'reports.academy.';

    SnappyPdf::fake();
});

test('transferredStudentListByAdmissionYear return data', function () {
    // create transferred student with multiple classes
    $semester_setting = SemesterSetting::factory()->create();
    $primary_class_junior = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
        'name->zh' => '一年1班',
    ]);
    $semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior->id
    ]);
    $semester_setting2 = SemesterSetting::factory()->create();
    $primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J21',
        'name->zh' => '二年1班',
    ]);
    $semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'class_id' => $primary_class_junior_2->id
    ]);
    $student = Student::factory()->create([
        'admission_year' => '2024',
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
        'name->en' => 'student A',
        'name->zh' => '学生 A',
        'student_number' => '0001',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class_junior_1->id,
        'student_id' => $student->id,
        'class_enter_date' => '2025-01-05',
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_class_junior_2->id,
        'student_id' => $student->id,
        'class_enter_date' => '2025-08-05',
    ]);

    // create transferred student without class
    $student3 = Student::factory()->create([
        'admission_year' => '2024',
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
        'name->en' => 'student D',
        'name->zh' => '学生 D',
        'student_number' => '0004',
    ]);

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $filters = [
        'report_language' => 'zh',
        'admission_year' => "2024",
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'transferred-student-list', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            [
                "student_name" => [
                    "en" => "student A",
                    "zh" => "学生 A",
                ],
                "student_number" => "0001",
                "class_name" => "J11 - 一年1班",
            ],
            [
                "student_name" => [
                    "en" => "student D",
                    "zh" => "学生 D",
                ],
                "student_number" => "0004",
                "class_name" => "-",
            ],
        ]);
});

test('transferredStudentListByAdmissionYear return pdf', function () {
    // create transferred student with multiple classes
    $semester_setting = SemesterSetting::factory()->create();
    $primary_class_junior = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
        'name->zh' => '一年1班',
    ]);
    $semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior->id
    ]);
    $semester_setting2 = SemesterSetting::factory()->create();
    $primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J21',
        'name->zh' => '二年1班',
    ]);
    $semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'class_id' => $primary_class_junior_2->id
    ]);
    $student = Student::factory()->create([
        'admission_year' => '2024',
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
        'name->en' => 'student A',
        'name->zh' => '学生 A',
        'student_number' => '0001',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class_junior_1->id,
        'student_id' => $student->id,
        'class_enter_date' => '2025-01-05',
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_class_junior_2->id,
        'student_id' => $student->id,
        'class_enter_date' => '2025-08-05',
    ]);

    // create transferred student without class
    $student3 = Student::factory()->create([
        'admission_year' => '2024',
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
        'name->en' => 'student D',
        'name->zh' => '学生 D',
        'student_number' => '0004',
    ]);

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $filters = [
        'report_language' => 'en',
        'admission_year' => "2024",
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'academy-transferred-student-list';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . 'transferred-student-list', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('studentAnalysisReportBySemesterGroupByGrade return data', function () {
    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $grades = Grade::factory(4)->create(new Sequence(
        [
            'name->en' => 'Junior 1',
            'name->zh' => '初一',
            'sequence' => 3,
        ],
        [
            'name->en' => 'Junior 2',
            'name->zh' => '初二',
            'sequence' => 2,
        ],
        [
            'name->en' => 'Junior 3',
            'name->zh' => '初三',
            'sequence' => 1,
        ],
        // Grade without student
        [
            'name->en' => 'Senior 1',
            'name->zh' => '高一',
            'sequence' => 0,
        ],
    ));
    $primary_class_junior_1 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[0]->id,
        'name->en' => 'J12',
        'name->zh' => '一年2班',
    ]);
    $second_primary_class_junior_1 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[0]->id,
        'name->en' => 'J11',
        'name->zh' => '一年1班',
    ]);
    $primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
        'name->en' => 'J21',
        'name->zh' => '二年1班',
    ]);
    // class without student
    $second_primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
        'name->en' => 'J22',
        'name->zh' => '二年2班',
    ]);
    $primary_class_junior_3 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[2]->id,
        'name->en' => 'J31',
        'name->zh' => '三年1班',
    ]);
    $semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_1->id
    ]);
    $second_semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_primary_class_junior_1->id
    ]);
    $semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_2->id
    ]);
    // class without student
    $second_semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_primary_class_junior_2->id
    ]);
    $semester_class_junior_3 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_3->id
    ]);

    $data_to_be_created = [
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_1,
            'male_students_hostel' => 5,
            'male_students_non_hostel' => 10,
            'female_students_hostel' => 4,
            'female_students_non_hostel' => 12,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $second_semester_class_junior_1,
            'male_students_hostel' => 6,
            'male_students_non_hostel' => 9,
            'female_students_hostel' => 7,
            'female_students_non_hostel' => 10,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_2,
            'male_students_hostel' => 0,
            'male_students_non_hostel' => 1,
            'female_students_hostel' => 2,
            'female_students_non_hostel' => 0,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_3,
            'male_students_hostel' => 5,
            'male_students_non_hostel' => 0,
            'female_students_hostel' => 0,
            'female_students_non_hostel' => 10,
        ],
    ];

    foreach ($data_to_be_created as $data) {
        for ($i = 1; $i <= $data['male_students_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::MALE->value,
                    'is_hostel' => true,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['male_students_non_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::MALE->value,
                    'is_hostel' => false,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['female_students_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::FEMALE->value,
                    'is_hostel' => true,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['female_students_non_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::FEMALE->value,
                    'is_hostel' => false,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
    }

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $semester_setting->id,
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'student-analysis', $filters)
    )->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            "summary" => [
                "total_students" => 81,
                "total_male_students" => 36,
                "total_female_students" => 45,
                "total_hostel_students" => 29,
                "total_non_hostel_students" => 52,
            ],
            "summary_by_grade" => [
                $grades[0]->id => [
                    "grade_name" => "Junior 1",
                    "total_students" => 63,
                    "total_male_students" => 30,
                    "total_female_students" => 33,
                    "total_hostel_students" => 22,
                    "total_non_hostel_students" => 41,
                    "classes_data" => [
                        [
                            "class_name" => "J11",
                            "total_students" => 32,
                            "total_male_students" => 15,
                            "total_female_students" => 17,
                            "total_hostel_students" => 13,
                            "total_non_hostel_students" => 19,
                        ],
                        [
                            "class_name" => "J12",
                            "total_students" => 31,
                            "total_male_students" => 15,
                            "total_female_students" => 16,
                            "total_hostel_students" => 9,
                            "total_non_hostel_students" => 22,
                        ]
                    ],
                ],
                $grades[1]->id => [
                    "grade_name" => "Junior 2",
                    "total_students" => 3,
                    "total_male_students" => 1,
                    "total_female_students" => 2,
                    "total_hostel_students" => 2,
                    "total_non_hostel_students" => 1,
                    "classes_data" => [
                        [
                            "class_name" => "J21",
                            "total_students" => 3,
                            "total_male_students" => 1,
                            "total_female_students" => 2,
                            "total_hostel_students" => 2,
                            "total_non_hostel_students" => 1,
                        ],
                        [
                            "class_name" => "J22",
                            "total_students" => 0,
                            "total_male_students" => 0,
                            "total_female_students" => 0,
                            "total_hostel_students" => 0,
                            "total_non_hostel_students" => 0,
                        ]
                    ],
                ],
                $grades[2]->id => [
                    "grade_name" => "Junior 3",
                    "total_students" => 15,
                    "total_male_students" => 5,
                    "total_female_students" => 10,
                    "total_hostel_students" => 5,
                    "total_non_hostel_students" => 10,
                    "classes_data" => [
                        [
                            "class_name" => "J31",
                            "total_students" => 15,
                            "total_male_students" => 5,
                            "total_female_students" => 10,
                            "total_hostel_students" => 5,
                            "total_non_hostel_students" => 10,
                        ],
                    ],
                ],
                $grades[3]->id => [
                    "grade_name" => "Senior 1",
                    "total_students" => 0,
                    "total_male_students" => 0,
                    "total_female_students" => 0,
                    "total_hostel_students" => 0,
                    "total_non_hostel_students" => 0,
                    "classes_data" => [],
                ],
            ],
        ]);
});

test('studentAnalysisReportBySemesterGroupByGrade return pdf', function () {
    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $grades = Grade::factory(4)->create(new Sequence(
        [
            'name->en' => 'Junior 1',
            'name->zh' => '初一',
            'sequence' => 0,
        ],
        [
            'name->en' => 'Junior 2',
            'name->zh' => '初二',
            'sequence' => 1,
        ],
        [
            'name->en' => 'Junior 3',
            'name->zh' => '初三',
            'sequence' => 2,
        ],
        // Grade without student
        [
            'name->en' => 'Senior 1',
            'name->zh' => '高一',
            'sequence' => 3,
        ],
    ));
    $primary_class_junior_1 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[0]->id,
        'name->en' => 'J12',
        'name->zh' => '一年2班',
    ]);
    $second_primary_class_junior_1 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[0]->id,
        'name->en' => 'J11',
        'name->zh' => '一年1班',
    ]);
    $primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
        'name->en' => 'J21',
        'name->zh' => '二年1班',
    ]);
    // class without student
    $second_primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
        'name->en' => 'J22',
        'name->zh' => '二年2班',
    ]);
    $primary_class_junior_3 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[2]->id,
        'name->en' => 'J31',
        'name->zh' => '三年1班',
    ]);
    $semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_1->id
    ]);
    $second_semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_primary_class_junior_1->id
    ]);
    $semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_2->id
    ]);
    // class without student
    $second_semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_primary_class_junior_2->id
    ]);
    $semester_class_junior_3 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_3->id
    ]);

    $data_to_be_created = [
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_1,
            'male_students_hostel' => 1,
            'male_students_non_hostel' => 2,
            'female_students_hostel' => 2,
            'female_students_non_hostel' => 1,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $second_semester_class_junior_1,
            'male_students_hostel' => 2,
            'male_students_non_hostel' => 1,
            'female_students_hostel' => 1,
            'female_students_non_hostel' => 1,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_2,
            'male_students_hostel' => 0,
            'male_students_non_hostel' => 1,
            'female_students_hostel' => 1,
            'female_students_non_hostel' => 0,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_3,
            'male_students_hostel' => 2,
            'male_students_non_hostel' => 1,
            'female_students_hostel' => 0,
            'female_students_non_hostel' => 2,
        ],
    ];

    foreach ($data_to_be_created as $data) {
        for ($i = 1; $i <= $data['male_students_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::MALE->value,
                    'is_hostel' => true,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['male_students_non_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::MALE->value,
                    'is_hostel' => false,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['female_students_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::FEMALE->value,
                    'is_hostel' => true,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['female_students_non_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::FEMALE->value,
                    'is_hostel' => false,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
    }

    LatestPrimaryClassBySemesterSettingView::refreshViewTable();

    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $semester_setting->id,
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'academy-student-analysis-report-by-semester';
    $extension = '.pdf';
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . 'student-analysis', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});
