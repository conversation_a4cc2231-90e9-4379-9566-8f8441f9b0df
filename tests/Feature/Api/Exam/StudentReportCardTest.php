<?php

use App\Http\Resources\EmployeeResource;
use App\Http\Resources\GradeResource;
use App\Http\Resources\SemesterSettingResource;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\ResultsPostingHeader;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentReportCard;
use App\Models\User;
use App\Repositories\ResultsPostingHeaderRepository;
use App\Services\Exam\ReportCard\StudentReportCardService;
use App\Services\Exam\ResultsPostingHeaderService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;


beforeEach(function () {

    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->routeNamePrefix = 'student-report-card';
});


test('index', function () {
    $grades = Grade::factory(3)->create();
    $semester_setting = SemesterSetting::factory()->create();
    $semester_class = SemesterClass::factory(3)->create([
        'semester_setting_id' => $semester_setting->id,
    ]);
    $students = Student::factory(2)->create();

    $headers = ResultsPostingHeader::factory(3)->state(new Sequence(
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_setting->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ],
        [
            'report_card_output_code' => 'SEM1RESULT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_setting->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ],
        [
            'report_card_output_code' => 'SEM1RESULT ALT',
            'grade_id' => $grades[0]->id,
            'semester_setting_id' => $semester_setting->id,
            'status' => ResultsPostingHeader::STATUS_COMPLETED,
        ]
    ))->create();


    $report_cards = StudentReportCard::factory(4)->state(new Sequence(
        [
            'results_posting_header_id' => $headers[0]->id,
            'semester_class_id' => $semester_class[0]->id,
            'semester_setting_id' => $semester_setting->id,
            'student_id' => $students[0]->id,
            'is_active' => true,
            'file_url' => 'https://url_one.com'
        ],
        [
            'results_posting_header_id' => $headers[0]->id,
            'semester_class_id' => $semester_class[0]->id,
            'semester_setting_id' => $semester_setting->id,
            'student_id' => $students[0]->id,
            'is_active' => false,
            'file_url' => 'https://url_two.com'
        ],
        [
            'results_posting_header_id' => $headers[0]->id,
            'semester_class_id' => $semester_class[0]->id,
            'semester_setting_id' => $semester_setting->id,
            'student_id' => $students[1]->id,
            'is_active' => true,
            'file_url' => 'https://url_three.com'
        ],
        [
            'results_posting_header_id' => $headers[1]->id,
            'semester_class_id' => $semester_class[1]->id,
            'semester_setting_id' => $semester_setting->id,
            'student_id' => $students[0]->id,
            'is_active' => true,
            'file_url' => 'https://url_four.com'
        ]
    ))->create();

    $payload = [
        'results_posting_header_id' => $headers[0]->id,
        'semester_class_id' => $semester_class[0]->id,
        'is_active' => true
    ];
    $response = $this->getJson(route($this->routeNamePrefix . '.index', $payload))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['code'])->toBe(200)
        ->and($response['data'])->toHavecount(2)
        ->and($response['data'][0])->toMatchArray([
            'id' => $report_cards[0]->id,
            'file_url' => $report_cards[0]->file_url
        ])
        ->and($response['data'][0]['semester_setting'])->toMatchArray([
            'id' => $semester_setting->id,
            'name' => $semester_setting->name
        ])
        ->and($response['data'][1])->toMatchArray([
            'id' => $report_cards[2]->id,
            'file_url' => $report_cards[2]->file_url
        ])
        ->and($response['data'][1]['semester_setting'])->toMatchArray([
            'id' => $semester_setting->id,
            'name' => $semester_setting->name
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(StudentReportCardService::class, function (MockInterface $mock) {
        $report_card = StudentReportCard::factory()->create();

        $mock->shouldReceive('getAllPaginatedReportCards')
            ->once()
            ->andReturn(new LengthAwarePaginator([$report_card], 1, 1));
    });

    $response = $this->getJson(route("$this->routeNamePrefix" . ".index", [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(StudentReportCardService::class, function (MockInterface $mock) {
        $report_cards = StudentReportCard::factory(2)->create();

        $mock->shouldReceive('getAllReportCard')->once()->andReturn($report_cards);
    });

    $response = $this->getJson(route("$this->routeNamePrefix" . ".index", [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});
