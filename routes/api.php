<?php

use App\Http\Controllers\Api\AccountingController;
use App\Http\Controllers\Api\AnnouncementController;
use App\Http\Controllers\Api\AnnouncementGroupController;
use App\Http\Controllers\Api\Attendance\AttendanceController;
use App\Http\Controllers\Api\Attendance\AttendanceInputController;
use App\Http\Controllers\Api\Attendance\AttendancePeriodOverrideController;
use App\Http\Controllers\Api\Attendance\SchoolAttendancePeriodOverrideController;
use App\Http\Controllers\Api\Auth\LoginController;
use App\Http\Controllers\Api\Auth\ResetPasswordController;
use App\Http\Controllers\Api\BillingDocument\BillingDocumentController;
use App\Http\Controllers\Api\Calendar\CalendarController;
use App\Http\Controllers\Api\Calendar\CalendarSettingController;
use App\Http\Controllers\Api\Calendar\CalendarTargetController;
use App\Http\Controllers\Api\CardController;
use App\Http\Controllers\Api\ClassController;
use App\Http\Controllers\Api\ClassSubjectController;
use App\Http\Controllers\Api\Club\ClubCategoryController;
use App\Http\Controllers\Api\Club\ClubController;
use App\Http\Controllers\Api\CompetitionController;
use App\Http\Controllers\Api\ComprehensiveAssessment\ComprehensiveAssessmentCategoryController;
use App\Http\Controllers\Api\ComprehensiveAssessment\ComprehensiveAssessmentQuestionController;
use App\Http\Controllers\Api\ComprehensiveAssessment\ComprehensiveAssessmentRecordController;
use App\Http\Controllers\Api\ConductRecordController;
use App\Http\Controllers\Api\ConductSettingController;
use App\Http\Controllers\Api\ContractorController;
use App\Http\Controllers\Api\CounsellingCaseRecordController;
use App\Http\Controllers\Api\DeadlineController;
use App\Http\Controllers\Api\DiscountController;
use App\Http\Controllers\Api\Ecommerce\EcommerceOrderController;
use App\Http\Controllers\Api\Ecommerce\EcommerceProductCategoryController;
use App\Http\Controllers\Api\Ecommerce\EcommerceProductController;
use App\Http\Controllers\Api\Ecommerce\EcommerceProductGroupController;
use App\Http\Controllers\Api\Ecommerce\EcommerceProductSubCategoryController;
use App\Http\Controllers\Api\Ecommerce\EcommerceProductTagController;
use App\Http\Controllers\Api\Ecommerce\MerchantController;
use App\Http\Controllers\Api\EmployeeController;
use App\Http\Controllers\Api\Enrollment\EnrollmentLoginController;
use App\Http\Controllers\Api\Enrollment\EnrollmentUserController;
use App\Http\Controllers\Api\EnrollmentController;
use App\Http\Controllers\Api\EnrollmentSessionController;
use App\Http\Controllers\Api\Exam\ExamController;
use App\Http\Controllers\Api\Exam\ExamResultsDataEntryController;
use App\Http\Controllers\Api\Exam\ExamResultsPostingController;
use App\Http\Controllers\Api\Exam\ExamSubjectExemptionController;
use App\Http\Controllers\Api\Exam\GradingFrameworkController;
use App\Http\Controllers\Api\Exam\PromotionMarkController;
use App\Http\Controllers\Api\Exam\ResultsPostingHeaderController;
use App\Http\Controllers\Api\Exam\StudentReportCardController;
use App\Http\Controllers\Api\ExamSemesterSettingController;
use App\Http\Controllers\Api\GradingSchemeController;
use App\Http\Controllers\Api\GuardianController;
use App\Http\Controllers\Api\GuestController;
use App\Http\Controllers\Api\Hostel\HostelBedAssignmentController;
use App\Http\Controllers\Api\Hostel\HostelBlockController;
use App\Http\Controllers\Api\Hostel\HostelInOutRecordController;
use App\Http\Controllers\Api\Hostel\HostelMeritDemeritSettingController;
use App\Http\Controllers\Api\Hostel\HostelPersonInChargeController;
use App\Http\Controllers\Api\Hostel\HostelRewardPunishmentRecordController;
use App\Http\Controllers\Api\Hostel\HostelRewardPunishmentSettingController;
use App\Http\Controllers\Api\Hostel\HostelRoomBedController;
use App\Http\Controllers\Api\Hostel\HostelRoomController;
use App\Http\Controllers\Api\Hostel\HostelSavingsAccountController;
use App\Http\Controllers\Api\LeaveApplication\LeaveApplicationController;
use App\Http\Controllers\Api\LeaveApplication\LeaveApplicationTypeController;
use App\Http\Controllers\Api\Library\BookController;
use App\Http\Controllers\Api\Library\IsbnController;
use App\Http\Controllers\Api\Library\LibraryMemberController;
use App\Http\Controllers\Api\LibraryBookLoanController;
use App\Http\Controllers\Api\MasterData\AuthorController;
use App\Http\Controllers\Api\MasterData\AwardController;
use App\Http\Controllers\Api\MasterData\BankController;
use App\Http\Controllers\Api\MasterData\BookCategoryController;
use App\Http\Controllers\Api\MasterData\BookClassificationController;
use App\Http\Controllers\Api\MasterData\BookLanguageController;
use App\Http\Controllers\Api\MasterData\BookSourceController;
use App\Http\Controllers\Api\MasterData\BookSubClassificationController;
use App\Http\Controllers\Api\MasterData\ConfigController;
use App\Http\Controllers\Api\MasterData\CountryController;
use App\Http\Controllers\Api\MasterData\CourseController;
use App\Http\Controllers\Api\MasterData\CurrencyController;
use App\Http\Controllers\Api\MasterData\DepartmentController;
use App\Http\Controllers\Api\MasterData\EducationController;
use App\Http\Controllers\Api\MasterData\EmployeeCategoryController;
use App\Http\Controllers\Api\MasterData\EmployeeJobTitleController;
use App\Http\Controllers\Api\MasterData\EmployeeSessionController;
use App\Http\Controllers\Api\MasterData\GlAccountController;
use App\Http\Controllers\Api\MasterData\GradeController;
use App\Http\Controllers\Api\MasterData\HealthConcernController;
use App\Http\Controllers\Api\MasterData\InternationalizationController;
use App\Http\Controllers\Api\MasterData\LeadershipPositionController;
use App\Http\Controllers\Api\MasterData\LeadershipPositionRecordController;
use App\Http\Controllers\Api\MasterData\LeaveReasonController;
use App\Http\Controllers\Api\MasterData\PaymentMethodController;
use App\Http\Controllers\Api\MasterData\ProductController;
use App\Http\Controllers\Api\MasterData\RaceController;
use App\Http\Controllers\Api\MasterData\ReligionController;
use App\Http\Controllers\Api\MasterData\RewardPunishmentCategoryController;
use App\Http\Controllers\Api\MasterData\RewardPunishmentSubCategoryController;
use App\Http\Controllers\Api\MasterData\SchoolController;
use App\Http\Controllers\Api\MasterData\SchoolProfileController;
use App\Http\Controllers\Api\MasterData\SemesterSettingController;
use App\Http\Controllers\Api\MasterData\SemesterYearSettingController;
use App\Http\Controllers\Api\MasterData\SocietyPositionController;
use App\Http\Controllers\Api\MasterData\StateController;
use App\Http\Controllers\Api\MasterData\UomController;
use App\Http\Controllers\Api\MasterData\WithdrawalReasonController;
use App\Http\Controllers\Api\MediaController;
use App\Http\Controllers\Api\MeritDemeritSettingController;
use App\Http\Controllers\Api\PaymentGatewayController;
use App\Http\Controllers\Api\PermissionController;
use App\Http\Controllers\Api\PosTerminalKeyController;
use App\Http\Controllers\Api\Reports\AcademyReportController;
use App\Http\Controllers\Api\Reports\AccountingReportController;
use App\Http\Controllers\Api\Reports\AttendanceReportController;
use App\Http\Controllers\Api\Reports\BillingDocumentReportController;
use App\Http\Controllers\Api\Reports\CardReportController;
use App\Http\Controllers\Api\Reports\CocurriculumReportController;
use App\Http\Controllers\Api\Reports\ContractorAttendanceReportController;
use App\Http\Controllers\Api\Reports\EcommerceBookshopReportController;
use App\Http\Controllers\Api\Reports\EcommerceCanteenReportController;
use App\Http\Controllers\Api\Reports\EnrollmentReportController;
use App\Http\Controllers\Api\Reports\HostelReportController;
use App\Http\Controllers\Api\Reports\LibraryBookLoanReportController;
use App\Http\Controllers\Api\Reports\SemesterClassReportController;
use App\Http\Controllers\Api\Reports\StudentConductReportController;
use App\Http\Controllers\Api\Reports\WalletReportController;
use App\Http\Controllers\Api\Reports\WalletTransactionReportController;
use App\Http\Controllers\Api\RewardPunishmentController;
use App\Http\Controllers\Api\RewardPunishmentRecordController;
use App\Http\Controllers\Api\RoleController;
use App\Http\Controllers\Api\Scholarship\ScholarshipController;
use App\Http\Controllers\Api\SemesterClassController;
use App\Http\Controllers\Api\Student\StudentController;
use App\Http\Controllers\Api\StudentSocietyPositionController;
use App\Http\Controllers\Api\SubjectController;
use App\Http\Controllers\Api\TerminalController;
use App\Http\Controllers\Api\Timetable\EmployeeTimetableController;
use App\Http\Controllers\Api\Timetable\PeriodController;
use App\Http\Controllers\Api\Timetable\PeriodGroupController;
use App\Http\Controllers\Api\Timetable\StudentTimetableController;
use App\Http\Controllers\Api\Timetable\SubstituteRecordController;
use App\Http\Controllers\Api\Timetable\TimeslotOverrideController;
use App\Http\Controllers\Api\Timetable\TimetableController;
use App\Http\Controllers\Api\UnpaidItemAssignmentController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\UserInboxController;
use App\Http\Controllers\Api\UserSpecialSettingController;
use App\Http\Controllers\Api\WalletController;
use App\Http\Controllers\Api\WalletTransactionController;
use App\Http\Controllers\Debug\DocumentPrintController;
use App\Http\Controllers\Debug\PushNotificationController;
use App\Http\Controllers\Debug\TestController;
use App\Http\Controllers\StatusController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('throttle:api')->group(function () {
    Route::prefix('debug')->group(function () {
        Route::post('/push-notification-send', [PushNotificationController::class, 'testSend']);
        Route::get('/_ip', [TestController::class, 'ip'])->middleware(['inbound.log'])->name('debug.ip');
    });

    Route::get('status', [StatusController::class, 'index'])->name('status');
});

Route::middleware('throttle:3rd-party')->group(function () {
    Route::any('payment-gateway/router',
        [PaymentGatewayController::class, 'router'])->middleware(['inbound.log'])->name('payment-gateway.router');

    Route::any('payment-gateway/{provider}/callback/{payment_gateway_log}/{token}',
        [PaymentGatewayController::class, 'callback'])->middleware(['inbound.log'])->name('payment-gateway.callback');
});

Route::middleware('check_platform_access')->group(function () {
    Route::middleware(['auth.terminal', 'inbound.log', 'throttle:3rd-party'])->group(function () {
        // PosTerminalKey routes
        Route::prefix('pos-terminal-keys')->name('pos-terminal-keys.')->group(function () {
            Route::get('/ping', [PosTerminalKeyController::class, 'ping'])->name('ping');
        });

        Route::prefix('operator-app')->name('operator-app.')->group(function () {
            Route::get('wallets/balance', [WalletController::class, 'getBalance'])->name('wallets.get-balance');
            Route::get('book-sub-classifications', [BookSubClassificationController::class, 'index'])->name('book-sub-classifications.index');
            Route::get('books', [BookController::class, 'index'])->name('books.index');
            Route::get('authors', [AuthorController::class, 'index'])->name('authors.index');
            Route::get('student-attendance-periods', [StudentTimetableController::class, 'attendancePeriods'])->name('student-timetable.attendance-periods');
            Route::post('attendance-offline-bulk-submit', [AttendanceInputController::class, 'bulkCreateAttendanceInputWithCard'])->name('bulk-create-with-card');
            Route::post('attendance-online-submit', [AttendanceInputController::class, 'createAttendanceInputWithCard'])->name('create-with-card');
        });
    });

    // Parent enrollment view routes
    Route::middleware('throttle:api')->prefix('enrollments')->name('enrollments.')->group(function () {
        // Route::post('/create-enrollment-user', [EnrollmentUserController::class, 'create'])->name('enrollment-user.create');
        Route::post('/login/request-otp', [EnrollmentLoginController::class, 'requestOtp'])->name('request-otp');
        Route::post('/login', [EnrollmentLoginController::class, 'login'])->name('login');

        Route::middleware('auth:enrollment')->group(function () {
            Route::post('/logout', [EnrollmentLoginController::class, 'logout'])->name('logout');
            
            // Master Data routes
            Route::prefix('master-data')->name('master-data.')->group(function () {
                Route::get('/internationalization', [InternationalizationController::class, 'index'])->name('internationalization.index');
                Route::get('/countries', [CountryController::class, 'index'])->name('country.index');
                Route::get('/states', [StateController::class, 'index'])->name('state.index');
                Route::get('/races', [RaceController::class, 'index'])->name('race.index');
                Route::get('/religions', [ReligionController::class, 'index'])->name('religion.index');
                Route::get('/grades', [GradeController::class, 'index'])->name('grade.index');
                Route::get('/educations', [EducationController::class, 'index'])->name('education.index');
                Route::get('/schools', [SchoolController::class, 'index'])->name('school.index');
                Route::get('/health-concerns', [HealthConcernController::class, 'index'])->name('health-concern.index');
            });

            // Enrollment routes
            Route::get('/', [EnrollmentController::class, 'index'])->name('index');
            Route::get('/{enrollment}', [EnrollmentController::class, 'show'])->name('show');
            Route::post('/{enrollment}/make-payment', [EnrollmentController::class, 'makePayment'])->name('make-payment');
            Route::put('/{enrollment}', [EnrollmentController::class, 'update'])->name('update');
            Route::post('/{billing_document}/retry-payment', [EnrollmentController::class, 'retryPayment'])->name('retry-payment');
        });
    });

    Route::middleware('throttle:api')->group(function () {
        Route::post('/login/request-otp', [LoginController::class, 'requestOtp'])->name('auth.request-otp');
        Route::post('/login', [LoginController::class, 'login'])->name('auth.login');
        Route::post('/login/reset-password/request-otp', [ResetPasswordController::class, 'requestOtpForResetPassword'])->name('auth.request-otp.reset-password');
        Route::post('/login/reset-password', [ResetPasswordController::class, 'resetPassword'])->name('auth.reset-password');

        // Group all authenticated routes under this middleware
        Route::middleware(['auth:sanctum', 'inbound.log'])->group(function () {
            Route::post('/logout', [LoginController::class, 'logout'])->name('auth.logout');
            //Admin BO Routes (SHOULD NOT USE THESE FOR mobile app FRONTEND)
            Route::prefix('admin')->name('admin.')->group(function () {
                //wallet
                Route::prefix('wallets')->name('wallets.')->group(function () {
                    Route::post('{wallet}/withdraw', [WalletController::class, 'withdraw'])->name('withdraw')->permission('wallet-admin-withdraw');
                    Route::post('{wallet}/adjustment', [WalletController::class, 'adjustment'])->name('adjustment')->permission('wallet-admin-adjustment');
                    Route::get('/', [WalletController::class, 'indexAdmin'])->name('index')->permission('wallet-admin-view');
                    Route::get('/transactions', [WalletTransactionController::class, 'index'])->name('transaction-index')->permission('transaction-admin-view');
                    Route::post('/transactions/{wallet_transaction}/refund', [WalletTransactionController::class, 'refund'])->name('transaction-refund')->permission('transaction-admin-refund');
                });

                //Master data
                Route::prefix('master-data')->name('master-data.')->group(function () {
                    // Config routes
                    Route::prefix('configs')->name('configs.')->group(function () {
                        Route::get('/', [ConfigController::class, 'indexAdmin'])->name('index')->permission('master-config-admin-view');
                        Route::post('/', [ConfigController::class, 'store'])->name('store')->permission('master-config-update');
                    });
                });

                // Scholarship routes
                Route::prefix('scholarships')->name('scholarships.')->group(function () {
                    Route::get('/', [ScholarshipController::class, 'index'])->name('index')->permission('scholarship-admin-view');
                    Route::post('/', [ScholarshipController::class, 'create'])->name('create')->permission('scholarship-admin-create');
                    Route::get('{scholarship}', [ScholarshipController::class, 'show'])->name('show')->permission('scholarship-admin-view');
                    Route::put('{scholarship}', [ScholarshipController::class, 'update'])->name('update')->permission('scholarship-admin-update');
                    Route::delete('{scholarship}', [ScholarshipController::class, 'destroy'])->name('destroy')->permission('scholarship-admin-delete');
                });

                // Discount
                Route::prefix('discounts')->name('discounts.')->group(function () {
                    Route::post('/confirm', [DiscountController::class, 'confirm'])->name('confirm')->permission('discount-confirm');
                    Route::post('/bulk-create', [DiscountController::class, 'createBulk'])->name('create')->permission('discount-create');
                    Route::put('/bulk-update', [DiscountController::class, 'updateBulk'])->name('update')->permission('discount-update');
                    Route::get('/', [DiscountController::class, 'index'])->name('index')->permission('discount-view');
                    Route::get('/{discount_setting}', [DiscountController::class, 'show'])->name('show')->permission('discount-view');
                });

                //ecommerce order
                Route::prefix('ecommerce/orders')->name('orders.')->group(function () {
                    Route::get('/', [EcommerceOrderController::class, 'indexAdmin'])->name('index')->permission('order-admin-view');
                });

                Route::prefix('unpaid-item-assignments')->name('unpaid-item-assignments.')->group(function () {
                    Route::get('/', [UnpaidItemAssignmentController::class, 'indexAdmin'])->name('index')->permission('unpaid-item-assignment-admin-view');
                    Route::get('/{unpaid_item_assignment}', [UnpaidItemAssignmentController::class, 'show'])->name('show')->permission('unpaid-item-assignment-admin-view');
                    Route::delete('{unpaid_item_assignment}', [UnpaidItemAssignmentController::class, 'destroy'])->name('destroy')->permission('unpaid-item-assignment-admin-delete');
                });

                // enrollment_session routes
                Route::prefix('enrollment-sessions')->name('enrollment-sessions.')->group(function () {
                    Route::get('/get-fee-setting-conditions', [EnrollmentSessionController::class, 'getFeeSettingConditions'])->name('get-fee-setting-conditions')->permission(['enrollment-session-create', 'enrollment-session-update']);
                    Route::get('/', [EnrollmentSessionController::class, 'index'])->name('index')->permission('enrollment-session-view');
                    Route::post('/', [EnrollmentSessionController::class, 'create'])->name('create')->permission('enrollment-session-create');
                    Route::get('{enrollment_session}', [EnrollmentSessionController::class, 'show'])->name('show')->permission('enrollment-session-view');
                    Route::put('{enrollment_session}', [EnrollmentSessionController::class, 'update'])->name('update')->permission('enrollment-session-update');
                    Route::delete('{enrollment_session}', [EnrollmentSessionController::class, 'destroy'])->name('destroy')->permission('enrollment-session-delete');
                });

                // Enrollment routes (Admin use only)
                Route::prefix('enrollments')->name('enrollments.')->group(function () {
                    // copied for Enrollments
                    Route::get('/billing-documents', [BillingDocumentController::class, 'indexAdmin'])->name('billing-document-index')->permission('enrollment-view');
                    Route::post('/{billing_document}/manual-payment', [BillingDocumentController::class, 'manualPayment'])->name('manual-payment')->permission('enrollment-manual-payment');

                    Route::get('/', [EnrollmentController::class, 'adminIndex'])->name('admin-index')->permission('enrollment-view');
                    Route::get('/pre-payment-template', [EnrollmentController::class, 'downloadPrePaymentTemplate'])->name('download-pre-payment-template')->permission('enrollment-download-pre-payment-template');
                    Route::get('/post-payment-template', [EnrollmentController::class, 'downloadPostPaymentTemplate'])->name('download-post-payment-template')->permission('enrollment-download-post-payment-template');
                    // show route placed after other routes to avoid conflict with the {enrollment} wildcard
                    Route::get('/{enrollment}', [EnrollmentController::class, 'adminShow'])->name('show')->permission('enrollment-view');
                    Route::put('/{enrollment}', [EnrollmentController::class, 'adminUpdate'])->name('update')->permission('enrollment-update');
                    Route::post('/{enrollment}/extend-expiry', [EnrollmentController::class, 'extendExpiry'])->name('extend-expiry')->permission('enrollment-extend-expiry');
                    Route::post('/import', [EnrollmentController::class, 'importTemplateValidation'])->name('import-template-validation')->permission('enrollment-import-template-validation');
                    Route::post('/import-insert', [EnrollmentController::class, 'bulkSaveImportedData'])->name('bulk-save-imported-data')->permission('enrollment-bulk-save-imported-data');
                    Route::delete('/{enrollment}', [EnrollmentController::class, 'destroy'])->name('destroy')->permission('enrollment-delete');
                    // post-payment import routes
                    Route::post('/post-payment-import', [EnrollmentController::class, 'importPostPaymentTemplateValidation'])->name('import-post-payment-template-validation')->permission('enrollment-import-post-payment-template-validation');
                    Route::post('/post-payment-import-insert', [EnrollmentController::class, 'bulkSavePostPaymentImportedData'])->name('bulk-save-post-payment-imported-data')->permission('enrollment-bulk-save-post-payment-imported-data');
                });

                // accounting/fees
                Route::prefix('accounting')->name('accounting.')->group(function () {
                    Route::prefix('fees')->name('fees.')->group(function () {
                        Route::post('/unpaid-items/create-billing-document', [AccountingController::class, 'createBillingDocumentAdmin'])->name('create-billing-document-admin')->permission('fees-unpaid-item-admin-pay');
                        Route::post('/print-unpaid-items', [AccountingController::class, 'printUnpaidItems'])->name('print-unpaid-items')->permission('fees-unpaid-item-admin-view');
                        Route::post('/create-and-assign', [AccountingController::class, 'createAndAssignFeesToStudents'])->name('create-and-assign')->permission('fees-create-and-assign');
                        Route::get('/unpaid-items', [AccountingController::class, 'indexUnpaidItemAdmin'])->name('index-unpaid-item')->permission('fees-unpaid-item-admin-view');
                    });

                    Route::post('/posting', [AccountingController::class, 'postBillingDocumentsToAutoCount'])->name('post-to-autocount')->permission('post-billing-documents-to-autocount');
                });

                // student timetable routes (BO only)
                Route::prefix('student-timetable')->name('student-timetable.')->group(function () {
                    Route::get('attendance-periods', [StudentTimetableController::class, 'attendancePeriods'])->name('attendance-periods')->permission('student-attendance-period-view');
                });

                // billing-document
                Route::prefix('billing-document')->name('billing-document.')->group(function () {
                    Route::get('/', [BillingDocumentController::class, 'indexAdmin'])->name('index-admin')->permission('billing-document-admin-view');
                    Route::get('/{billing_document}', [BillingDocumentController::class, 'showAdmin'])->name('show')->permission('billing-document-admin-view');
                    Route::put('/status', [BillingDocumentController::class, 'changeStatus'])->name('change-status')->permission('billing-document-admin-status-update');
                    Route::post('/{billing_document}/void', [BillingDocumentController::class, 'adminVoid'])->name('admin-void')->permission('billing-document-admin-void');
                    Route::post('/{billing_document}/manual-payment', [BillingDocumentController::class, 'manualPayment'])->name('manual-payment')->permission('billing-document-manual-payment');
                });

                // HostelPIC routes
                Route::prefix('hostels')->name('hostels.')->group(function () {
                    Route::put('/students/{student}', [StudentController::class, 'updateHostelStudent'])->name('student-update')->permission('hostel-student-update');

                    Route::prefix('person-in-charge')->name('person-in-charge.')->group(function () {
                        Route::get('/', [HostelPersonInChargeController::class, 'index'])->name('index')->permission('hostel-person-in-charge-admin-view');
                        Route::post('/add', [HostelPersonInChargeController::class, 'add'])->name('add')->permission('hostel-person-in-charge-admin-add');
                        Route::post('/remove', [HostelPersonInChargeController::class, 'remove'])->name('remove')->permission('hostel-person-in-charge-admin-remove');
                    });
                });

                // Enrollment User Routes
                Route::prefix('enrollment-users')->name('enrollment-users.')->group(function () {
                    Route::get('/', [EnrollmentUserController::class, 'index'])->name('index')->permission('enrollment-user-view');
                    Route::get('{enrollment_user}', [EnrollmentUserController::class, 'show'])->name('show')->permission('enrollment-user-view');
                    Route::put('{enrollment_user}', [EnrollmentUserController::class, 'update'])->name('update')->permission('enrollment-user-update');
                });
            });

            Route::post('media/upload', [MediaController::class, 'upload'])->name('media.upload')->block(10, 10);

            // merchant routes
            Route::prefix('merchants')->name('merchants.')->group(function () {
                Route::get('/', [MerchantController::class, 'index'])->name('index')->permission('merchant-view');
                Route::post('/', [MerchantController::class, 'create'])->name('create')->permission('merchant-create');
                Route::get('{merchant}', [MerchantController::class, 'show'])->name('show')->permission('merchant-view');
                Route::put('{merchant}', [MerchantController::class, 'update'])->name('update')->permission('merchant-update');
                Route::delete('{merchant}', [MerchantController::class, 'destroy'])->name('destroy')->permission('merchant-delete');
            });

            //ecommerce order
            Route::prefix('orders')->name('orders.')->group(function () {
                Route::get('/', [EcommerceOrderController::class, 'index'])->name('index')->permission('order-view');
                Route::get('{order}', [EcommerceOrderController::class, 'show'])->name('show')->permission('order-view');
                Route::post('checkout', [EcommerceOrderController::class, 'checkout'])->name('checkout')->permission('order-checkout');
                Route::put('{order}', [EcommerceOrderController::class, 'update'])->name('update')->permission('order-update');
                Route::post('{order}/cancel', [EcommerceOrderController::class, 'cancel'])->name('cancel')->permission('order-cancel');
            });

            // ecommerce product routes`
            Route::prefix('products')->name('products.')->group(function () {
                //Delivery date
                Route::get('delivery-date', [EcommerceProductController::class, 'deliveryDates'])->name('delivery-dates');
                Route::get('get-by-delivery-date', [EcommerceProductController::class, 'getByDeliveryDates'])->name('get-by-delivery-date')->permission('product-update');
                Route::post('delivery-date/bulk-update', [EcommerceProductController::class, 'bulkUpdateDeliveryDate'])->name('product-delivery-date-bulk-update')->permission('product-update');

                //Available date
                Route::post('available-date/bulk-update', [EcommerceProductController::class, 'bulkUpdateAvailableDate'])->name('product-available-date-bulk-update')->permission('product-update');
                Route::get('get-by-available-date', [EcommerceProductController::class, 'getByAvailableDates'])->name('get-by-available-date')->permission('product-update');

                Route::get('/', [EcommerceProductController::class, 'index'])->name('index')->permission('product-view');
                Route::get('merchant-type/{merchant_type}', [EcommerceProductController::class, 'indexByMerchantType'])->name('index-by-merchant-type');
                Route::post('/', [EcommerceProductController::class, 'create'])->name('create')->permission('product-create');
                Route::get('{product}', [EcommerceProductController::class, 'show'])->name('show')->permission('product-view');
                Route::put('{product}', [EcommerceProductController::class, 'update'])->name('update')->permission('product-update');
                Route::delete('{product}', [EcommerceProductController::class, 'destroy'])->name('destroy')->permission('product-delete');
            });

            // ecommerce product tag routes
            Route::prefix('product-tags')->name('product-tags.')->group(function () {
                Route::get('/', [EcommerceProductTagController::class, 'index'])->name('index')->permission('product-tag-view');
                Route::post('/', [EcommerceProductTagController::class, 'create'])->name('create')->permission('product-tag-create');
                Route::get('{product_tag}', [EcommerceProductTagController::class, 'show'])->name('show')->permission('product-tag-view');
                Route::put('{product_tag}', [EcommerceProductTagController::class, 'update'])->name('update')->permission('product-tag-update');
                Route::delete('{product_tag}', [EcommerceProductTagController::class, 'destroy'])->name('destroy')->permission('product-tag-delete');
                Route::get('{product_tag}/get-targets', [EcommerceProductTagController::class, 'getTargets'])->name('get-targets')->permission('product-tag-view');
            });

            // ecommerce product group routes
            Route::prefix('product-groups')->name('product-groups.')->group(function () {
                Route::get('/', [EcommerceProductGroupController::class, 'index'])->name('index')->permission('product-group-view');
                Route::post('/', [EcommerceProductGroupController::class, 'create'])->name('create')->permission('product-group-create');
                Route::get('{product_group}', [EcommerceProductGroupController::class, 'show'])->name('show')->permission('product-group-view');
                Route::put('{product_group}', [EcommerceProductGroupController::class, 'update'])->name('update')->permission('product-group-update');
                Route::delete('{product_group}', [EcommerceProductGroupController::class, 'destroy'])->name('destroy')->permission('product-group-delete');
            });

            // ecommerce product category routes
            Route::prefix('product-categories')->name('product-categories.')->group(function () {
                Route::get('/', [EcommerceProductCategoryController::class, 'index'])->name('index')->permission('product-category-view');
                Route::post('/', [EcommerceProductCategoryController::class, 'create'])->name('create')->permission('product-category-create');
                Route::get('{product_category}', [EcommerceProductCategoryController::class, 'show'])->name('show')->permission('product-category-view');
                Route::put('{product_category}', [EcommerceProductCategoryController::class, 'update'])->name('update')->permission('product-category-update');
                Route::delete('{product_category}', [EcommerceProductCategoryController::class, 'destroy'])->name('destroy')->permission('product-category-delete');
            });

            // ecommerce product sub category routes
            Route::prefix('product-sub-categories')->name('product-sub-categories.')->group(function () {
                Route::get('/', [EcommerceProductSubCategoryController::class, 'index'])->name('index')->permission('product-category-view|product-category-admin-view');
                Route::post('/', [EcommerceProductSubCategoryController::class, 'create'])->name('create')->permission('product-category-create');
                Route::get('{product_sub_category}', [EcommerceProductSubCategoryController::class, 'show'])->name('show')->permission('product-category-view');
                Route::put('{product_sub_category}', [EcommerceProductSubCategoryController::class, 'update'])->name('update')->permission('product-category-update');
                Route::delete('{product_sub_category}', [EcommerceProductSubCategoryController::class, 'destroy'])->name('destroy')->permission('product-category-delete');
            });

            // Master data routes
            Route::prefix('master-data')->name('master-data.')->group(function () {
                // Leave Reason routes
                Route::prefix('leave-reasons')->name('leave-reasons.')->group(function () {
                    Route::get('/', [LeaveReasonController::class, 'index'])->name('index')->permission('master-leave-reason-view');
                    Route::post('/', [LeaveReasonController::class, 'create'])->name('create')->permission('master-leave-reason-create');
                    Route::get('{leave_reason}', [LeaveReasonController::class, 'show'])->name('show')->permission('master-leave-reason-view');
                    Route::put('{leave_reason}', [LeaveReasonController::class, 'update'])->name('update')->permission('master-leave-reason-update');
                    Route::delete('{leave_reason}', [LeaveReasonController::class, 'destroy'])->name('destroy')->permission('master-leave-reason-delete');
                });

                // employee category routes
                Route::prefix('employee-categories')->name('employee-categories.')->group(function () {
                    Route::get('/', [EmployeeCategoryController::class, 'index'])->name('index')->permission('master-employee-category-view');
                });

                // Health concern routes
                Route::prefix('health-concerns')->name('health-concerns.')->group(function () {
                    Route::get('/', [HealthConcernController::class, 'index'])->name('index')->permission('master-health-concern-view');
                    Route::post('/', [HealthConcernController::class, 'create'])->name('create')->permission('master-health-concern-create');
                    Route::get('{health_concern}', [HealthConcernController::class, 'show'])->name('show')->permission('master-health-concern-view');
                    Route::put('{health_concern}', [HealthConcernController::class, 'update'])->name('update')->permission('master-health-concern-update');
                    Route::delete('{health_concern}', [HealthConcernController::class, 'destroy'])->name('destroy')->permission('master-health-concern-delete');
                });

                // Bank routes
                Route::prefix('banks')->name('banks.')->group(function () {
                    Route::get('/', [BankController::class, 'index'])->name('index')->permission('master-bank-view');
                    Route::post('/', [BankController::class, 'create'])->name('create')->permission('master-bank-create');
                    Route::get('{bank}', [BankController::class, 'show'])->name('show')->permission('master-bank-view');
                    Route::put('{bank}', [BankController::class, 'update'])->name('update')->permission('master-bank-update');
                    Route::delete('{bank}', [BankController::class, 'destroy'])->name('destroy')->permission('master-bank-delete');
                });

                // Education routes
                Route::prefix('educations')->name('educations.')->group(function () {
                    Route::get('/', [EducationController::class, 'index'])->name('index')->permission('master-education-view');
                    Route::post('/', [EducationController::class, 'create'])->name('create')->permission('master-education-create');
                    Route::get('{master_education}', [EducationController::class, 'show'])->name('show')->permission('master-education-view');
                    Route::put('{master_education}', [EducationController::class, 'update'])->name('update')->permission('master-education-update');
                    Route::delete('{master_education}', [EducationController::class, 'destroy'])->name('destroy')->permission('master-education-delete');
                });

                // Courses routes
                Route::prefix('courses')->name('courses.')->group(function () {
                    Route::get('/', [CourseController::class, 'index'])->name('index')->permission('master-course-view');
                    Route::post('/', [CourseController::class, 'create'])->name('create')->permission('master-course-create');
                    Route::get('{master_course}', [CourseController::class, 'show'])->name('show')->permission('master-course-view');
                    Route::put('{master_course}', [CourseController::class, 'update'])->name('update')->permission('master-course-update');
                });

                // Internationalization routes
                Route::prefix('internationalization')->name('internationalization.')->group(function () {
                    Route::get('/', [InternationalizationController::class, 'index'])->name('index')->permission('master-internationalization-view');
                    Route::post('/', [InternationalizationController::class, 'create'])->name('create')->permission('master-internationalization-create');
                    Route::get('/{internationalization}', [InternationalizationController::class, 'show'])->name('show')->permission('master-internationalization-view');
                    Route::put('/{internationalization}', [InternationalizationController::class, 'update'])->name('update')->permission('master-internationalization-update');
                });

                // Author routes
                Route::prefix('authors')->name('authors.')->group(function () {
                    Route::get('/', [AuthorController::class, 'index'])->name('index')->permission('master-author-view');
                    Route::post('/', [AuthorController::class, 'create'])->name('create')->permission('master-author-create');
                    Route::get('{author}', [AuthorController::class, 'show'])->name('show')->permission('master-author-view');
                    Route::put('{author}', [AuthorController::class, 'update'])->name('update')->permission('master-author-update');
                    Route::delete('{author}', [AuthorController::class, 'destroy'])->name('destroy')->permission('master-author-delete');
                });

                // award routes
                Route::prefix('awards')->name('awards.')->group(function () {
                    Route::get('/', [AwardController::class, 'index'])->name('index')->permission('master-award-view');
                    Route::post('/', [AwardController::class, 'create'])->name('create')->permission('master-award-create');
                    Route::get('{master_award}', [AwardController::class, 'show'])->name('show')->permission('master-award-view');
                    Route::put('{master_award}', [AwardController::class, 'update'])->name('update')->permission('master-award-update');
                    Route::delete('{master_award}', [AwardController::class, 'destroy'])->name('destroy')->permission('master-award-delete');
                });

                // Book binding routes
                Route::prefix('book-sub-classifications')->name('book-sub-classifications.')->group(function () {
                    Route::get('/', [BookSubClassificationController::class, 'index'])->name('index')->permission('master-book-sub-classification-view');
                    Route::post('/', [BookSubClassificationController::class, 'create'])->name('create')->permission('master-book-sub-classification-create');
                    Route::get('{master_book_sub_classification}', [BookSubClassificationController::class, 'show'])->name('show')->permission('master-book-sub-classification-view');
                    Route::put('{master_book_sub_classification}', [BookSubClassificationController::class, 'update'])->name('update')->permission('master-book-sub-classification-update');
                    Route::delete('{master_book_sub_classification}', [BookSubClassificationController::class, 'destroy'])->name('destroy')->permission('master-book-sub-classification-delete');
                });

                // Book category routes
                Route::prefix('book-categories')->name('book-categories.')->group(function () {
                    Route::get('/', [BookCategoryController::class, 'index'])->name('index')->permission('master-book-category-view');
                    Route::post('/', [BookCategoryController::class, 'create'])->name('create')->permission('master-book-category-create');
                    Route::get('{master_book_category}', [BookCategoryController::class, 'show'])->name('show')->permission('master-book-category-view');
                    Route::put('{master_book_category}', [BookCategoryController::class, 'update'])->name('update')->permission('master-book-category-update');
                    Route::delete('{master_book_category}', [BookCategoryController::class, 'destroy'])->name('destroy')->permission('master-book-category-delete');
                });

                // Book classification routes
                Route::prefix('book-classifications')->name('book-classifications.')->group(function () {
                    Route::get('/', [BookClassificationController::class, 'index'])->name('index')->permission('master-book-classification-view');
                    Route::post('/', [BookClassificationController::class, 'create'])->name('create')->permission('master-book-classification-create');
                    Route::get('{master_book_classification}', [BookClassificationController::class, 'show'])->name('show')->permission('master-book-classification-view');
                    Route::put('{master_book_classification}', [BookClassificationController::class, 'update'])->name('update')->permission('master-book-classification-update');
                    Route::delete('{master_book_classification}', [BookClassificationController::class, 'destroy'])->name('destroy')->permission('master-book-classification-delete');
                });

                // Book source routes
                Route::prefix('book-sources')->name('book-sources.')->group(function () {
                    Route::get('/', [BookSourceController::class, 'index'])->name('index')->permission('master-book-source-view');
                    Route::post('/', [BookSourceController::class, 'create'])->name('create')->permission('master-book-source-create');
                    Route::get('{master_book_source}', [BookSourceController::class, 'show'])->name('show')->permission('master-book-source-view');
                    Route::put('{master_book_source}', [BookSourceController::class, 'update'])->name('update')->permission('master-book-source-update');
                    Route::delete('{master_book_source}', [BookSourceController::class, 'destroy'])->name('destroy')->permission('master-book-source-delete');
                });

                // Race routes
                Route::prefix('races')->name('races.')->group(function () {
                    Route::get('/', [RaceController::class, 'index'])->name('index')->permission('master-race-view');
                    Route::post('/', [RaceController::class, 'create'])->name('create')->permission('master-race-create');
                    Route::get('{master_race}', [RaceController::class, 'show'])->name('show')->permission('master-race-view');
                    Route::put('{master_race}', [RaceController::class, 'update'])->name('update')->permission('master-race-update');
                    Route::delete('{master_race}', [RaceController::class, 'destroy'])->name('destroy')->permission('master-race-delete');
                });

                // Religion routes
                Route::prefix('religions')->name('religions.')->group(function () {
                    Route::get('/', [ReligionController::class, 'index'])->name('index')->permission('master-religion-view');
                    Route::post('/', [ReligionController::class, 'create'])->name('create')->permission('master-religion-create');
                    Route::get('{master_religion}', [ReligionController::class, 'show'])->name('show')->permission('master-religion-view');
                    Route::put('{master_religion}', [ReligionController::class, 'update'])->name('update')->permission('master-religion-update');
                    Route::delete('{master_religion}', [ReligionController::class, 'destroy'])->name('destroy')->permission('master-religion-delete');
                });

                // School routes
                Route::prefix('schools')->name('schools.')->group(function () {
                    Route::get('/', [SchoolController::class, 'index'])->name('index')->permission('master-school-view');
                    Route::post('/', [SchoolController::class, 'create'])->name('create')->permission('master-school-create');
                    Route::get('{school}', [SchoolController::class, 'show'])->name('show')->permission('master-school-view');
                    Route::put('{school}', [SchoolController::class, 'update'])->name('update')->permission('master-school-update');
                    Route::delete('{school}', [SchoolController::class, 'destroy'])->name('destroy')->permission('master-school-delete');
                });

                // Country routes
                Route::prefix('countries')->name('countries.')->group(function () {
                    Route::get('/', [CountryController::class, 'index'])->name('index')->permission('master-country-view');
                    Route::post('/', [CountryController::class, 'create'])->name('create')->permission('master-country-create');
                    Route::get('{master_country}', [CountryController::class, 'show'])->name('show')->permission('master-country-view');
                    Route::put('{master_country}', [CountryController::class, 'update'])->name('update')->permission('master-country-update');
                    Route::delete('{master_country}', [CountryController::class, 'destroy'])->name('destroy')->permission('master-country-delete');
                });

                // State routes
                Route::prefix('states')->name('states.')->group(function () {
                    Route::get('/', [StateController::class, 'index'])->name('index')->permission('master-state-view');
                    Route::post('/', [StateController::class, 'create'])->name('create')->permission('master-state-create');
                    Route::get('{master_state}', [StateController::class, 'show'])->name('show')->permission('master-state-view');
                    Route::put('{master_state}', [StateController::class, 'update'])->name('update')->permission('master-state-update');
                    Route::delete('{master_state}', [StateController::class, 'destroy'])->name('destroy')->permission('master-state-delete');
                });

                // School profile routes
                Route::prefix('school-profile')->name('school-profile.')->group(function () {
                    Route::get('/', [SchoolProfileController::class, 'index'])->name('index')->permission('master-school-profile-view');
                    Route::post('/', [SchoolProfileController::class, 'update'])->name('update')->permission('master-school-profile-update');
                });

                // Grade routes
                Route::prefix('grades')->name('grades.')->group(function () {
                    Route::get('/', [GradeController::class, 'index'])->name('index')->permission('master-grade-view');
                    Route::post('/', [GradeController::class, 'create'])->name('create')->permission('master-grade-create');
                    Route::get('{master_grade}', [GradeController::class, 'show'])->name('show')->permission('master-grade-view');
                    Route::put('{master_grade}', [GradeController::class, 'update'])->name('update')->permission('master-grade-update');
                    Route::delete('{master_grade}', [GradeController::class, 'destroy'])->name('destroy')->permission('master-grade-delete');
                });

                // Config routes
                Route::prefix('configs')->name('configs.')->group(function () {
                    Route::get('/', [ConfigController::class, 'index'])->name('index')->permission('master-config-view');
                    Route::get('{key}', [ConfigController::class, 'show'])->name('show')->permission('master-config-view');
                });

                // Uom routes
                Route::prefix('uoms')->name('uoms.')->group(function () {
                    Route::get('/', [UomController::class, 'index'])->name('index')->permission('master-uom-view');
                });

                // GlAccount routes
                Route::prefix('gl-accounts')->name('gl-accounts.')->group(function () {
                    Route::get('/', [GlAccountController::class, 'index'])->name('index')->permission('master-gl-account-view');
                });

                // Currency routes
                Route::prefix('currencies')->name('currencies.')->group(function () {
                    Route::get('/', [CurrencyController::class, 'index'])->name('index')->permission('master-currency-view');
                });

                // Product routes
                Route::prefix('products')->name('products.')->group(function () {
                    Route::get('/', [ProductController::class, 'index'])->name('index')->permission('master-product-view');
                    Route::post('/', [ProductController::class, 'create'])->name('create')->permission('master-product-create');
                    Route::get('{product}', [ProductController::class, 'show'])->name('show')->permission('master-product-view');
                    Route::put('{product}', [ProductController::class, 'update'])->name('update')->permission('master-product-update');
                    Route::delete('{product}', [ProductController::class, 'destroy'])->name('destroy')->permission('master-product-delete');
                });

                // SemesterSettings routes
                Route::prefix('semester-year-settings')->name('semester-year-settings.')->group(function () {
                    Route::get('/', [SemesterYearSettingController::class, 'index'])->name('index')->permission('master-semester-year-setting-view');
                    Route::post('/', [SemesterYearSettingController::class, 'create'])->name('create')->permission('master-semester-year-setting-create');
                    Route::get('{master_semester_year_setting}', [SemesterYearSettingController::class, 'show'])->name('show')->permission('master-semester-year-setting-view');
                    Route::put('{master_semester_year_setting}', [SemesterYearSettingController::class, 'update'])->name('update')->permission('master-semester-year-setting-update');
                    Route::delete('{master_semester_year_setting}', [SemesterYearSettingController::class, 'destroy'])->name('destroy')->permission('master-semester-year-setting-delete');
                });

                // SemesterSettings routes
                Route::prefix('semester-settings')->name('semester-settings.')->group(function () {
                    Route::get('/', [SemesterSettingController::class, 'index'])->name('index')->permission('master-semester-setting-view');
                    Route::post('/', [SemesterSettingController::class, 'create'])->name('create')->permission('master-semester-setting-create');
                    Route::get('{master_semester_setting}', [SemesterSettingController::class, 'show'])->name('show')->permission('master-semester-setting-view');
                    Route::put('{master_semester_setting}', [SemesterSettingController::class, 'update'])->name('update')->permission('master-semester-setting-update');
                    Route::delete('{master_semester_setting}', [SemesterSettingController::class, 'destroy'])->name('destroy')->permission('master-semester-setting-delete');
                });

                // Employee Job Title routes
                Route::prefix('employee-job-titles')->name('employee-job-titles.')->group(function () {
                    Route::get('/', [EmployeeJobTitleController::class, 'index'])->name('index')->permission('master-employee-job-title-view');
                    Route::post('/', [EmployeeJobTitleController::class, 'create'])->name('create')->permission('master-employee-job-title-create');
                    Route::get('{master_employee_job_title}', [EmployeeJobTitleController::class, 'show'])->name('show')->permission('master-employee-job-title-view');
                    Route::put('{master_employee_job_title}', [EmployeeJobTitleController::class, 'update'])->name('update')->permission('master-employee-job-title-update');
                    Route::delete('{master_employee_job_title}', [EmployeeJobTitleController::class, 'destroy'])->name('destroy')->permission('master-employee-job-title-delete');
                });

                // Reward Punishment Category routes
                Route::prefix('reward-punishment-categories')->name('reward-punishment-categories.')->group(function () {
                    Route::get('/', [RewardPunishmentCategoryController::class, 'index'])->name('index')->permission('master-reward-punishment-category-view');
                    Route::post('/', [RewardPunishmentCategoryController::class, 'create'])->name('create')->permission('master-reward-punishment-category-create');
                    Route::get('{reward_punishment_category}', [RewardPunishmentCategoryController::class, 'show'])->name('show')->permission('master-reward-punishment-category-view');
                    Route::put('{reward_punishment_category}', [RewardPunishmentCategoryController::class, 'update'])->name('update')->permission('master-reward-punishment-category-update');
                    Route::delete('{reward_punishment_category}', [RewardPunishmentCategoryController::class, 'destroy'])->name('destroy')->permission('master-reward-punishment-category-delete');
                });

                // SocietyPosition routes
                Route::prefix('society-positions')->name('society-positions.')->group(function () {
                    Route::get('/', [SocietyPositionController::class, 'index'])->name('index')->permission('master-society-position-view');
                    Route::get('/{society_position}', [SocietyPositionController::class, 'show'])->name('show')->permission('master-society-position-view');
                    Route::post('/', [SocietyPositionController::class, 'create'])->name('create')->permission('master-society-position-create');
                    Route::put('/{society_position}', [SocietyPositionController::class, 'update'])->name('update')->permission('master-society-position-update');
                    Route::delete('/{society_position}', [SocietyPositionController::class, 'destroy'])->name('destroy')->permission('master-society-position-delete');
                });

                // Reward Sub Punishment Category routes
                Route::prefix('reward-punishment-sub-categories')->name('reward-punishment-sub-categories.')->group(function () {
                    Route::get('/', [RewardPunishmentSubCategoryController::class, 'index'])->name('index')->permission('master-reward-punishment-sub-category-view');
                    Route::post('/', [RewardPunishmentSubCategoryController::class, 'create'])->name('create')->permission('master-reward-punishment-sub-category-create');
                    Route::get('{reward_punishment_sub_category}', [RewardPunishmentSubCategoryController::class, 'show'])->name('show')->permission('master-reward-punishment-sub-category-view');
                    Route::put('{reward_punishment_sub_category}', [RewardPunishmentSubCategoryController::class, 'update'])->name('update')->permission('master-reward-punishment-sub-category-update');
                    Route::delete('{reward_punishment_sub_category}', [RewardPunishmentSubCategoryController::class, 'destroy'])->name('destroy')->permission('master-reward-punishment-sub-category-delete');
                });

                // Leadership position routes
                Route::prefix('leadership-positions')->name('leadership-positions.')->group(function () {
                    Route::get('/', [LeadershipPositionController::class, 'index'])->name('index')->permission('master-leadership-position-view');
                    Route::post('/', [LeadershipPositionController::class, 'create'])->name('create')->permission('master-leadership-position-create');
                    Route::get('{master_leadership_position}', [LeadershipPositionController::class, 'show'])->name('show')->permission('master-leadership-position-view');
                    Route::put('{master_leadership_position}', [LeadershipPositionController::class, 'update'])->name('update')->permission('master-leadership-position-update');
                    Route::delete('{master_leadership_position}', [LeadershipPositionController::class, 'destroy'])->name('destroy')->permission('master-leadership-position-delete');
                });

                // Language routes
                Route::prefix('book-languages')->name('book-languages.')->group(function () {
                    Route::get('/', [BookLanguageController::class, 'index'])->name('index')->permission('book-language-view');
                });

                // Withdrawal Reason routes
                Route::prefix('withdrawal-reasons')->name('withdrawal-reasons.')->group(function () {
                    Route::get('/', [WithdrawalReasonController::class, 'index'])->name('index')->permission('master-withdrawal-reason-view');
                    Route::post('/', [WithdrawalReasonController::class, 'create'])->name('create')->permission('master-withdrawal-reason-create');
                    Route::get('{withdrawal_reason}', [WithdrawalReasonController::class, 'show'])->name('show')->permission('master-withdrawal-reason-view');
                    Route::put('{withdrawal_reason}', [WithdrawalReasonController::class, 'update'])->name('update')->permission('master-withdrawal-reason-update');
                    Route::delete('{withdrawal_reason}', [WithdrawalReasonController::class, 'destroy'])->name('destroy')->permission('master-withdrawal-reason-delete');
                });

                // Payment Method routes
                Route::prefix('payment-methods')->name('payment-methods.')->group(function () {
                    Route::get('/', [PaymentMethodController::class, 'index'])->name('index')->permission('master-payment-method-view');
                    Route::post('/', [PaymentMethodController::class, 'create'])->name('create')->permission('master-payment-method-create');
                    Route::get('{payment_method}', [PaymentMethodController::class, 'show'])->name('show')->permission('master-payment-method-view');
                    Route::put('{payment_method}', [PaymentMethodController::class, 'update'])->name('update')->permission('master-payment-method-update');
                    Route::delete('{payment_method}', [PaymentMethodController::class, 'destroy'])->name('destroy')->permission('master-payment-method-delete');
                });

                // Employee Session routes
                Route::prefix('employee-session')->name('employee-session.')->group(function () {
                    Route::get('/', [EmployeeSessionController::class, 'index'])->name('index')->permission('master-employee-session-view');
                    Route::post('/', [EmployeeSessionController::class, 'create'])->name('create')->permission('master-employee-session-create');
                    Route::get('{employee_session}', [EmployeeSessionController::class, 'show'])->name('show')->permission('master-employee-session-view');
                    Route::put('{employee_session}', [EmployeeSessionController::class, 'update'])->name('update')->permission('master-employee-session-update');
                    Route::delete('{employee_session}', [EmployeeSessionController::class, 'destroy'])->name('destroy')->permission('master-employee-session-delete');
                });
                // Department routes
                Route::prefix('departments')->name('departments.')->group(function () {
                    Route::get('/', [DepartmentController::class, 'index'])->name('index')->permission('department-view');
                    Route::post('/', [DepartmentController::class, 'create'])->name('create')->permission('department-create');
                    Route::get('{department}', [DepartmentController::class, 'show'])->name('show')->permission('department-view');
                    Route::put('{department}', [DepartmentController::class, 'update'])->name('update')->permission('department-update');
                    Route::delete('{department}', [DepartmentController::class, 'destroy'])->name('destroy')->permission('department-delete');
                });
            });

            //Timetable
            Route::prefix('timetables')->name('timetables.')->group(function () {
//        Route::delete('{timetable}', [TimetableController::class, 'destroy'])->name('destroy')->permission('timetable-delete');

                Route::prefix('period-groups')->name('period-groups.')->group(function () {
                    Route::get('/', [PeriodGroupController::class, 'index'])->name('index')->permission('period-group-view');
                    Route::post('/', [PeriodGroupController::class, 'create'])->name('create')->permission('period-group-create');
                    Route::get('/get-all-grouped-periods', [PeriodGroupController::class, 'getAllPeriodGroupLabelWithGroupedPeriods'])->name('getAllPeriodGroupLabelWithGroupedPeriods')->permission('period-group-view');
                    Route::get('{period_group}', [PeriodGroupController::class, 'show'])->name('show')->permission('period-group-view');
                    Route::put('{period_group}', [PeriodGroupController::class, 'update'])->name('update')->permission('period-group-update');
                    Route::delete('{period_group}', [PeriodGroupController::class, 'destroy'])->name('destroy')->permission('period-group-delete');

                    Route::post('/get-period-group-label-by-student-ids', [PeriodGroupController::class, 'getPeriodGroupLabelByStudentIds'])->name('getPeriodGroupLabelByStudentIds');
                });

                Route::prefix('periods')->name('periods.')->group(function () {
                    Route::get('/', [PeriodController::class, 'index'])->name('index')->permission('period-view');
                    Route::post('bulk-save', [PeriodController::class, 'bulkSave'])->name('bulk-save')->permission('period-save');
                    Route::get('{period}', [PeriodController::class, 'show'])->name('show')->permission('period-view');
                });

                Route::get('/', [TimetableController::class, 'index'])->name('index')->permission('timetable-view');
                Route::post('/', [TimetableController::class, 'create'])->name('create')->permission('timetable-create');
                Route::put('{timetable}/assign-period-group', [TimetableController::class, 'assignPeriodGroup'])->name('assign-period-group')->permission('timetable-assign-period-group');
                Route::get('{timetable}', [TimetableController::class, 'show'])->name('show')->permission('timetable-view');
                Route::put('{timetable}', [TimetableController::class, 'update'])->name('update')->permission('timetable-update');
            });

            // attendance period override
            Route::prefix('attendance-period-override')->name('attendance-period-override.')->group(function () {
                Route::get('/', [AttendancePeriodOverrideController::class, 'index'])->name('index')->permission('attendance-period-override-view');
                Route::get('{attendance_period_override}', [AttendancePeriodOverrideController::class, 'show'])->name('show')->permission('attendance-period-override-view');
                Route::post('/', [AttendancePeriodOverrideController::class, 'create'])->name('create')->permission('attendance-period-override-create');
                Route::put('{attendance_period_override}', [AttendancePeriodOverrideController::class, 'update'])->name('update')->permission('attendance-period-override-update');
                Route::delete('{attendance_period_override}', [AttendancePeriodOverrideController::class, 'destroy'])->name('destroy')->permission('attendance-period-override-delete');
                Route::post('/batch-delete', [AttendancePeriodOverrideController::class, 'batchDelete'])->name('batch-delete')->permission('attendance-period-override-delete');
            });

            //Student routes
            Route::prefix('students')->name('students.')->group(function () {
                Route::get('/', [StudentController::class, 'index'])->name('index')->permission(['student-view', 'hostel-student-view']);
                Route::get('/first-by-student-or-card-number/{number}',
                    [StudentController::class, 'getFirstByStudentNumberOrCardNumber'])->name('get-by-student-number-or-card-number')->permission('student-view');
                Route::post('/', [StudentController::class, 'create'])->name('create')->permission('student-create');
                Route::post('{student}/regenerate-student-number', [StudentController::class, 'regenerateStudentNumber'])->name('regenerate-student-number')->permission('student-update');
                Route::get('{student}', [StudentController::class, 'show'])->name('show')->permission(['student-view', 'hostel-student-view']);
                Route::put('{student}', [StudentController::class, 'update'])->name('update')->permission('student-update');
                Route::delete('{student}', [StudentController::class, 'destroy'])->name('destroy')->permission('student-delete');

                Route::post('/leave/{student}', [StudentController::class, 'leave'])->name('leave')->permission('student-update');
                Route::post('/return/{student}', [StudentController::class, 'return'])->name('return')->permission('student-update');
            });

            // Guardian routes
            Route::prefix('guardians')->name('guardians.')->group(function () {
                Route::get('/', [GuardianController::class, 'index'])->name('index')->permission('guardian-view');
            });

            // Employee routes
            Route::prefix('employees')->name('employees.')->group(function () {
                Route::get('/', [EmployeeController::class, 'index'])->name('index')->permission('employee-view');
                Route::post('/', [EmployeeController::class, 'create'])->name('create')->permission('employee-create');
                Route::get('{employee}', [EmployeeController::class, 'show'])->name('show')->permission('employee-view');
                Route::put('{employee}', [EmployeeController::class, 'update'])->name('update')->permission('employee-update');
                Route::delete('{employee}', [EmployeeController::class, 'destroy'])->name('destroy')->permission('employee-delete');

                Route::post('/resign/{employee}', [EmployeeController::class, 'resign'])->name('resign')->permission('employee-update');
                Route::post('/reinstate/{employee}', [EmployeeController::class, 'reinstate'])->name('reinstate')->permission('employee-update');
                Route::post('/transfer/{employee}', [EmployeeController::class, 'transfer'])->name('transfer')->permission('employee-update');
            });

            Route::prefix('cards')->name('cards.')->group(function () {
                Route::get('/template', [CardController::class, 'downloadImportCardTemplate'])->name('template')->permission('card-download-template');
                Route::post('/import', [CardController::class, 'import'])->name('import')->permission('card-import');
                Route::post('/bulk-assignment', [CardController::class, 'bulkAssignment'])->name('bulk-assignment')->permission('card-bulk-assignment');
            });

            // Guest routes
            Route::prefix('guests')->name('guests.')->group(function () {
                Route::get('/', [GuestController::class, 'index'])->name('index')->permission('guest-view');
                Route::post('/', [GuestController::class, 'create'])->name('create')->permission('guest-create');
                Route::get('{guest}', [GuestController::class, 'show'])->name('show')->permission('guest-view');
                Route::put('{guest}', [GuestController::class, 'update'])->name('update')->permission('guest-update');
                Route::delete('{guest}', [GuestController::class, 'destroy'])->name('destroy')->permission('guest-delete');
            });

            // Accounting routes (public)
            Route::prefix('accounting')->name('accounting.')->group(function () {
                Route::prefix('fees')->name('fees.')->group(function () {
                    Route::get('/unpaid-items', [AccountingController::class, 'indexUnpaidItem'])->name('index-unpaid-item')->permission('fees-unpaid-item-view');
                    Route::post('/unpaid-items/create-billing-document', [AccountingController::class, 'createBillingDocument'])->name('create-billing-document-from-unpaid-item')->permission('fees-unpaid-item-pay');
                });
            });
            Route::prefix('users')->name('users.')->group(function () {
                Route::get('/get-profile', [UserController::class, 'getProfile'])->name('get-profile');
                Route::post('/change-password', [UserController::class, 'changePassword'])->name('change-password');

                Route::prefix('cards')->name('cards.')->group(function () {
                    Route::get('/', [CardController::class, 'index'])->name('index')->permission('card-view');
                    Route::get('/get-userable-list', [CardController::class, 'getUserableList'])->name('get-userable-list')->permission('card-create|card-update');
                    Route::get('{card}', [CardController::class, 'show'])->name('show')->permission('card-view');
                    Route::post('/', [CardController::class, 'create'])->name('create')->permission('card-create');
                    Route::put('{card}', [CardController::class, 'update'])->name('update')->permission('card-update');
                    Route::delete('{card}', [CardController::class, 'destroy'])->name('destroy')->permission('card-delete');
                });

                // Wallet routes
                Route::prefix('wallets')->name('wallets.')->group(function () {
                    Route::get('/', [WalletController::class, 'index'])->name('index')->permission('wallet-view');
                    Route::get('/get-transferable-wallets', [WalletController::class, 'getTransferableWallets'])->name('getTransferableWallets')->permission('wallet-transfer');
                    Route::get('balance', [WalletController::class, 'getBalance'])->name('get-balance')->permission('wallet-get-balance');
                    //            Route::middleware('user_has_permission_to_model:'.Wallet::class.',wallet_id')->group(function () {
                    Route::post('transfer', [WalletController::class, 'transfer'])->name('transfer')->permission('wallet-transfer');
                    Route::post('deposit/request', [WalletController::class, 'depositRequest'])->name('deposit-request')->permission('wallet-deposit');
                    //            });

                    // Wallet Transactions
                    Route::get('{wallet}/transactions', [WalletTransactionController::class, 'show'])->name('transactions.show')->permission('wallet-view');
                });

                // Inbox route
                Route::prefix('inbox')->name('inbox.')->group(function () {
                    Route::get('/', [UserInboxController::class, 'index'])->name('index');//->permission('inbox-view');
                    Route::get('{user_inbox}', [UserInboxController::class, 'show'])->name('show');//->permission('inbox-view');
                    Route::put('{user_inbox}/read', [UserInboxController::class, 'markAsRead'])->name('mark-as-read');//->permission('inbox-update');
                    Route::delete('/{user_inbox}', [UserInboxController::class, 'destroy'])->name('destroy');//->permission('delete');
                    Route::delete('/', [UserInboxController::class, 'destroyAll'])->name('destroy-all');
                });

                // Push notification route
                Route::prefix('push-notifications')->name('push-notifications.')->group(function () {
                    Route::post('/token', [UserController::class, 'updatePushNotificationToken'])->name('update-token');
                    Route::delete('/token', [UserController::class, 'clearPushNotificationToken'])->name('clear-token');
                });


                Route::get('/', [UserController::class, 'index'])->name('index')->permission('user-view');
                Route::get('/{user}', [UserController::class, 'show'])->name('show')->permission('user-view');
                Route::put('/{user}', [UserController::class, 'update'])->name('update')->permission('user-update');
                Route::post('/{user}/unlink-guardian', [UserController::class, 'unlinkGuardian'])->name('unlink-guardian')->permission('user-update');
            });

            Route::prefix('announcements')->name('announcements.')->group(function () {
                Route::prefix('groups')->name('group.')->group(function () {
                    Route::get('/', [AnnouncementGroupController::class, 'index'])->name('index')->permission('announcement-group-view');
                    Route::get('{announcement_group}', [AnnouncementGroupController::class, 'show'])->name('show')->permission('announcement-group-view');
                    Route::post('/', [AnnouncementGroupController::class, 'create'])->name('create')->permission('announcement-group-create');
                    Route::put('{announcement_group}', [AnnouncementGroupController::class, 'update'])->name('update')->permission('announcement-group-update');
                    Route::delete('{announcement_group}', [AnnouncementGroupController::class, 'destroy'])->name('destroy')->permission('announcement-group-delete');
                    Route::get('{announcement_group}/get-userables', [AnnouncementGroupController::class, 'getUserables'])->name('announcement-group-get-userables');
                });

                Route::get('/', [AnnouncementController::class, 'index'])->name('index')->permission('announcement-view');
                Route::get('{announcement}', [AnnouncementController::class, 'show'])->name('show')->permission('announcement-view');
                Route::post('/', [AnnouncementController::class, 'create'])->name('create')->permission('announcement-create');
                Route::put('{announcement}/cancel', [AnnouncementController::class, 'cancel'])->name('cancel')->permission('announcement-create');
                Route::delete('{announcement}', [AnnouncementController::class, 'destroy'])->name('destroy')->permission('announcement-delete');
            });

            // Subject routes
            Route::prefix('subjects')->name('subjects.')->group(function () {
                Route::get('/', [SubjectController::class, 'index'])->name('index')->permission('subject-view');
                Route::post('/', [SubjectController::class, 'create'])->name('create')->permission('subject-create');
                Route::get('/{subject}', [SubjectController::class, 'show'])->name('show')->permission('subject-view');
                Route::put('/{subject}', [SubjectController::class, 'update'])->name('update')->permission('subject-update');
                Route::delete('/{subject}', [SubjectController::class, 'destroy'])->name('destroy')->permission('subject-delete');
            });

            // ClassSubject routes
            Route::prefix('class-subjects')->name('class-subjects.')->group(function () {
                Route::get('/', [ClassSubjectController::class, 'index'])->name('index')->permission(['primary-class-subject-view', 'english-class-subject-view', 'society-class-subject-view', 'elective-class-subject-view']);
                Route::post('/', [ClassSubjectController::class, 'create'])->name('create')->permission(['primary-class-subject-create', 'english-class-subject-create', 'society-class-subject-create', 'elective-class-subject-create']);
                Route::put('/assign-subjects-to-semester-class',
                    [ClassSubjectController::class, 'assignSubjectsToSemesterClass'])->name('assign-subjects-to-semester-classes')->permission(['primary-class-subject-update', 'english-class-subject-update', 'society-class-subject-update', 'elective-class-subject-update']);
                Route::put('/assign-semester-classes-to-subject',
                    [ClassSubjectController::class, 'assignSemesterClassesToSubject'])->name('assign-semester-classes-to-subject')->permission(['primary-class-subject-update', 'english-class-subject-update', 'society-class-subject-update', 'elective-class-subject-update']);
                Route::get('/{class_subject}', [ClassSubjectController::class, 'show'])->name('show')->permission(['primary-class-subject-view', 'english-class-subject-view', 'society-class-subject-view', 'elective-class-subject-view']);
                Route::put('/{class_subject}', [ClassSubjectController::class, 'update'])->name('update')->permission(['primary-class-subject-update', 'english-class-subject-update', 'society-class-subject-update', 'elective-class-subject-update']);
                Route::delete('/{class_subject}', [ClassSubjectController::class, 'destroy'])->name('destroy')->permission(['primary-class-subject-delete', 'english-class-subject-delete', 'society-class-subject-delete', 'elective-class-subject-delete']);
            });

            // ClubCategory routes
            Route::prefix('club-categories')->name('club-categories.')->group(function () {
                Route::get('/', [ClubCategoryController::class, 'index'])->name('index')->permission('club-category-view');
                Route::post('/', [ClubCategoryController::class, 'create'])->name('create')->permission('club-category-create');
                Route::get('/{club_category}', [ClubCategoryController::class, 'show'])->name('show')->permission('club-category-view');
                Route::put('/{club_category}', [ClubCategoryController::class, 'update'])->name('update')->permission('club-category-update');
                Route::delete('{club_category}', [ClubCategoryController::class, 'destroy'])->name('destroy')->permission('club-category-delete');
            });

            // Club routes
            Route::prefix('clubs')->name('clubs.')->group(function () {
                Route::get('/', [ClubController::class, 'index'])->name('index')->permission('club-view');
                Route::post('/', [ClubController::class, 'create'])->name('create')->permission('club-create');
                Route::get('/{club}', [ClubController::class, 'show'])->name('show')->permission('club-view');
                Route::put('/{club}', [ClubController::class, 'update'])->name('update')->permission('club-update');
                Route::delete('/{club}', [ClubController::class, 'destroy'])->name('destroy')->permission('club-delete');
            });

            //CounsellingCaseRecord routes
            Route::prefix('counselling-case-records')->name('counselling-case-records.')->group(function () {
                Route::get('/', [CounsellingCaseRecordController::class, 'index'])->name('index')->permission('counselling-case-record-view');
                Route::post('/', [CounsellingCaseRecordController::class, 'create'])->name('create')->permission('counselling-case-record-create');
                Route::get('{counselling_case_record}', [CounsellingCaseRecordController::class, 'show'])->name('show')->permission('counselling-case-record-view');
                Route::put('{counselling_case_record}', [CounsellingCaseRecordController::class, 'update'])->name('update')->permission('counselling-case-record-update');
                Route::delete('{counselling_case_record}', [CounsellingCaseRecordController::class, 'destroy'])->name('destroy')->permission('counselling-case-record-delete');
            });

            //Class routes
            Route::prefix('classes')->name('classes.')->group(function () {
                Route::prefix('seat-assignment')->name('seat-assignment.')->group(function () {
                    Route::get('/{semester_class}', [ClassController::class, 'getStudentsBySemesterClass'])->name('get-students')->permission('class-seat-assignment-view');
                    Route::put('/{semester_class}', [ClassController::class, 'assignSeatsToStudents'])->name('assign-seats')->permission('class-seat-assignment-update');
                    Route::post('/{semester_setting}', [ClassController::class, 'autoAssignSeatsBySemesterSetting'])->name('auto-assign')->permission('class-seat-assignment-update');
                });

                Route::get('/', [ClassController::class, 'index'])->name('index')->permission(['primary-class-view', 'english-class-view', 'society-class-view', 'elective-class-view']);
                Route::post('/', [ClassController::class, 'create'])->name('create')->permission(['primary-class-create', 'english-class-create', 'society-class-create', 'elective-class-create']);
                Route::put('/assign-student', [ClassController::class, 'assignClassToStudent'])->name('assign-class-to-student')->permission(['primary-class-update', 'english-class-update', 'society-class-update', 'elective-class-update']);
                Route::put('/assign-classes-to-semester', [ClassController::class, 'assignClassToSemester'])->name('assign-class-to-semester')->permission(['primary-class-update', 'english-class-update', 'society-class-update', 'elective-class-update']);
                Route::put('/assign-classes-to-semesters', [ClassController::class, 'assignClassesToSemesters'])->name('assign-classes-to-semesters')->permission(['primary-class-update', 'english-class-update', 'society-class-update', 'elective-class-update']);
                Route::put('/assign-semesters-to-class', [ClassController::class, 'assignSemestersToClass'])->name('assign-semester-to-class')->permission(['primary-class-update', 'english-class-update', 'society-class-update', 'elective-class-update']);
                Route::get('{class}', [ClassController::class, 'show'])->name('show')->permission(['primary-class-view', 'english-class-view', 'society-class-view', 'elective-class-view']);
                Route::put('{class}', [ClassController::class, 'update'])->name('update')->permission(['primary-class-update', 'english-class-update', 'society-class-update', 'elective-class-update']);
                Route::delete('{class}', [ClassController::class, 'destroy'])->name('destroy')->permission(['primary-class-delete', 'english-class-delete', 'society-class-delete', 'elective-class-delete']);
            });

            // Semester class routes
            Route::prefix('semester-classes')->name('semester-classes.')->group(function () {
                Route::get('/', [SemesterClassController::class, 'index'])->name('index')->permission(['semester-class-view']);
                Route::get('/{semester_class}', [SemesterClassController::class, 'show'])->name('show')->permission(['primary-semester-class-view', 'english-semester-class-view', 'society-semester-class-view', 'elective-semester-class-view']);
                Route::put('/{semester_class}', [SemesterClassController::class, 'update'])->name('update')->permission(['primary-semester-class-update', 'english-semester-class-update', 'society-semester-class-update', 'elective-semester-class-update']);
                Route::delete('/{semester_class}', [SemesterClassController::class, 'destroy'])->name('destroy')->permission(['primary-semester-class-delete', 'english-semester-class-delete', 'society-semester-class-delete', 'elective-semester-class-delete']);
            });

            //Contractor routes
            Route::prefix('contractors')->name('contractors.')->group(function () {
                Route::get('/', [ContractorController::class, 'index'])->name('index')->permission('contractor-view');
                Route::post('/', [ContractorController::class, 'create'])->name('create')->permission('contractor-create');
                Route::get('{contractor}', [ContractorController::class, 'show'])->name('show')->permission('contractor-view');
                Route::put('{contractor}', [ContractorController::class, 'update'])->name('update')->permission('contractor-update');
                Route::delete('{contractor}', [ContractorController::class, 'destroy'])->name('destroy')->permission('contractor-delete');
            });

            //Deadline routes
            Route::prefix('deadlines')->name('deadlines.')->group(function () {
                Route::get('/conduct', [DeadlineController::class, 'conductIndex'])->name('conduct-index')->permission('conduct-deadline-view');
                Route::put('/conduct', [DeadlineController::class, 'updateOrCreateConductDeadline'])->name('update-or-create-conduct-deadline')->permission('conduct-deadline-update');
            });

            // GradingScheme routes
            Route::prefix('grading-schemes')->name('grading-schemes.')->group(function () {
                Route::get('/', [GradingSchemeController::class, 'index'])->name('index')->permission('grading-scheme-view');
                Route::post('/', [GradingSchemeController::class, 'create'])->name('create')->permission('grading-scheme-create');
                Route::get('/{grading_scheme}', [GradingSchemeController::class, 'show'])->name('show')->permission('grading-scheme-view');
                Route::put('/{grading_scheme}', [GradingSchemeController::class, 'update'])->name('update')->permission('grading-scheme-update');
                Route::delete('/{grading_scheme}', [GradingSchemeController::class, 'destroy'])->name('destroy')->permission('grading-scheme-delete');
            });

            // ConductSetting routes
            Route::prefix('conduct-settings')->name('conduct-settings.')->group(function () {
                Route::get('/first-by-semester-class-id/{semester_class}', [ConductSettingController::class, 'firstBySemesterClassId'])->name('first-by-semester-class-id')->permission('conduct-setting-view');
                Route::get('/get-teachers-by-semester-class/{semester_class}', [ConductSettingController::class, 'getTeachersBySemesterClass'])->name('get-teachers-by-semester-class')->permission('conduct-setting-view');
                Route::post('/', [ConductSettingController::class, 'bulkUpdate'])->name('bulk-update')->permission('conduct-setting-update');
                Route::get('/get-conduct-teacher-list', [ConductSettingController::class, 'getConductTeacherList'])->name('get-conduct-teacher-list')->permission('conduct-setting-view');
                Route::get('/get-semester-classes-by-teacher', [ConductSettingController::class, 'getSemesterClassesByTeacher'])->name('get-semester-classes-by-teacher')->permission('conduct-setting-view');
            });

            // ConductRecord routes
            Route::prefix('conduct-records')->name('conduct-records.')->group(function () {
                Route::get('/', [ConductRecordController::class, 'index'])->name('index')->permission(['conduct-record-view', 'conduct-record-view-all-teacher']);
                Route::post('/{conduct_setting_teacher}', [ConductRecordController::class, 'createOrUpdate'])->name('create-update')->permission('conduct-record-update');
                Route::delete('/{conduct_record}', [ConductRecordController::class, 'destroy'])->name('destroy')->permission('conduct-record-delete');
            });

            Route::prefix('exam-semester-settings')->name('exam-semester-settings.')->group(function () {
                Route::get('/', [ExamSemesterSettingController::class, 'index'])->name('index')->permission('exam-semester-setting-view');
                Route::put('/bulk-create-or-update', [ExamSemesterSettingController::class, 'bulkCreateOrUpdate'])->name('bulk-create-or-update')->permission('exam-semester-setting-create');
            });

            //UserSpecialSetting routes
            Route::prefix('user-special-settings')->name('user-special-settings.')->group(function () {
                Route::get('/', [UserSpecialSettingController::class, 'index'])->name('index')->permission('user-special-setting-view');
                Route::post('/', [UserSpecialSettingController::class, 'create'])->name('create')->permission('user-special-setting-create');
                Route::get('{user_special_setting}', [UserSpecialSettingController::class, 'show'])->name('show')->permission('user-special-setting-view');
                Route::put('{user_special_setting}', [UserSpecialSettingController::class, 'update'])->name('update')->permission('user-special-setting-update');
                Route::delete('{user_special_setting}', [UserSpecialSettingController::class, 'destroy'])->name('destroy')->permission('user-special-setting-delete');
            });

            //MeritDemeritSetting routes
            Route::prefix('merit-demerit-settings')->name('merit-demerit-settings.')->group(function () {
                Route::get('/', [MeritDemeritSettingController::class, 'index'])->name('index')->permission('merit-demerit-setting-view');
                Route::post('/', [MeritDemeritSettingController::class, 'create'])->name('create')->permission('merit-demerit-setting-create');
                Route::get('{merit_demerit_setting}', [MeritDemeritSettingController::class, 'show'])->name('show')->permission('merit-demerit-setting-view');
                Route::put('{merit_demerit_setting}', [MeritDemeritSettingController::class, 'update'])->name('update')->permission('merit-demerit-setting-update');
                Route::delete('{merit_demerit_setting}', [MeritDemeritSettingController::class, 'destroy'])->name('destroy')->permission('merit-demerit-setting-delete');
            });

            //RewardPunishment routes
            Route::prefix('reward-punishments')->name('reward-punishments.')->group(function () {
                Route::get('/', [RewardPunishmentController::class, 'index'])->name('index')->permission('reward-punishment-view');
                Route::post('/', [RewardPunishmentController::class, 'create'])->name('create')->permission('reward-punishment-create');
                Route::get('{reward_punishment}', [RewardPunishmentController::class, 'show'])->name('show')->permission('reward-punishment-view');
                Route::put('{reward_punishment}', [RewardPunishmentController::class, 'update'])->name('update')->permission('reward-punishment-update');
                Route::delete('{reward_punishment}', [RewardPunishmentController::class, 'destroy'])->name('destroy')->permission('reward-punishment-delete');
            });

            //RewardPunishmentRecord routes
            Route::prefix('reward-punishment-records')->name('reward-punishment-records.')->group(function () {
                Route::get('/', [RewardPunishmentRecordController::class, 'index'])->name('index')->permission('reward-punishment-record-view');
                Route::post('/', [RewardPunishmentRecordController::class, 'create'])->name('create')->permission('reward-punishment-record-create');
                Route::get('/{reward_punishment_record}', [RewardPunishmentRecordController::class, 'show'])->name('show')->permission('reward-punishment-record-view');
                Route::put('/bulk-update-status', [RewardPunishmentRecordController::class, 'bulkUpdateStatus'])->name('bulk-update-status')->permission('reward-punishment-record-update');
                Route::put('/{reward_punishment_record}', [RewardPunishmentRecordController::class, 'update'])->name('update')->permission('reward-punishment-record-update');
                Route::delete('/{reward_punishment_record}', [RewardPunishmentRecordController::class, 'destroy'])->name('destroy')->permission('reward-punishment-record-delete');
            });

            //HostelBlock routes
            Route::prefix('hostel-blocks')->name('hostel-blocks.')->group(function () {
                Route::get('/', [HostelBlockController::class, 'index'])->name('index')->permission('hostel-block-view');
                Route::post('/', [HostelBlockController::class, 'create'])->name('create')->permission('hostel-block-create');
                Route::get('{hostel_block}', [HostelBlockController::class, 'show'])->name('show')->permission('hostel-block-view');
                Route::put('{hostel_block}', [HostelBlockController::class, 'update'])->name('update')->permission('hostel-block-update');
                Route::delete('{hostel_block}', [HostelBlockController::class, 'destroy'])->name('destroy')->permission('hostel-block-delete');
            });

            //HostelRoom routes
            Route::prefix('hostel-rooms')->name('hostel-rooms.')->group(function () {
                Route::get('/', [HostelRoomController::class, 'index'])->name('index')->permission('hostel-room-view');
                Route::post('/', [HostelRoomController::class, 'create'])->name('create')->permission('hostel-room-create');
                Route::get('{hostel_room}', [HostelRoomController::class, 'show'])->name('show')->permission('hostel-room-view');
                Route::put('{hostel_room}', [HostelRoomController::class, 'update'])->name('update')->permission('hostel-room-update');
                Route::delete('{hostel_room}', [HostelRoomController::class, 'destroy'])->name('destroy')->permission('hostel-room-delete');
            });

            //HostelRoomBed routes
            Route::prefix('hostel-room-beds')->name('hostel-room-beds.')->group(function () {
                Route::get('/', [HostelRoomBedController::class, 'index'])->name('index')->permission('hostel-room-bed-view');
                Route::post('/', [HostelRoomBedController::class, 'create'])->name('create')->permission('hostel-room-bed-create');
                Route::get('{hostel_room_bed}', [HostelRoomBedController::class, 'show'])->name('show')->permission('hostel-room-bed-view');
                Route::put('{hostel_room_bed}', [HostelRoomBedController::class, 'update'])->name('update')->permission('hostel-room-bed-update');
                Route::delete('{hostel_room_bed}', [HostelRoomBedController::class, 'destroy'])->name('destroy')->permission('hostel-room-bed-delete');
            });

            //HostelBedAssignment routes
            Route::prefix('hostel-bed-assignments')->name('hostel-bed-assignments.')->group(function () {
                Route::post('/assign', [HostelBedAssignmentController::class, 'assign'])->name('assign')->permission('hostel-bed-assignment-assign');
                Route::post('/unassign', [HostelBedAssignmentController::class, 'unassign'])->name('unassign')->permission('hostel-bed-assignment-unassign');
                Route::post('/change', [HostelBedAssignmentController::class, 'change'])->name('change')->permission('hostel-bed-assignment-change');
                Route::get('/template', [HostelBedAssignmentController::class, 'downloadBedAssignmentTemplate'])->name('template')->permission('hostel-bed-assignment-download-template');
                Route::post('/import', [HostelBedAssignmentController::class, 'import'])->name('import')->permission('hostel-bed-assignment-import');
                Route::post('/bulk-assignment', [HostelBedAssignmentController::class, 'bulkAssignment'])->name('bulk-assignment')->permission('hostel-bed-assignment-bulk-assignment');
            });

            //HostelInOutRecord routes
            Route::prefix('hostel-in-out-records')->name('hostel-in-out-records.')->group(function () {
                Route::get('/', [HostelInOutRecordController::class, 'index'])->name('index')->permission('hostel-in-out-record-view');
                Route::post('/', [HostelInOutRecordController::class, 'create'])->name('create')->permission('hostel-in-out-record-create');
                Route::patch('/', [HostelInOutRecordController::class, 'checkIn'])->name('check-in')->permission('hostel-in-out-record-update');
                Route::get('{hostel_in_out_record}', [HostelInOutRecordController::class, 'show'])->name('show')->permission('hostel-in-out-record-view');
                Route::put('{hostel_in_out_record}', [HostelInOutRecordController::class, 'update'])->name('update')->permission('hostel-in-out-record-update');
                Route::delete('{hostel_in_out_record}', [HostelInOutRecordController::class, 'destroy'])->name('destroy')->permission('hostel-in-out-record-delete');
            });

            //HostelMeritDemeritSetting routes
            Route::prefix('hostel-merit-demerit-settings')->name('hostel-merit-demerit-settings.')->group(function () {
                Route::get('/', [HostelMeritDemeritSettingController::class, 'index'])->name('index')->permission('hostel-merit-demerit-setting-view');
                Route::post('/', [HostelMeritDemeritSettingController::class, 'create'])->name('create')->permission('hostel-merit-demerit-setting-create');
                Route::get('{hostel_merit_demerit_setting}', [HostelMeritDemeritSettingController::class, 'show'])->name('show')->permission('hostel-merit-demerit-setting-view');
                Route::put('{hostel_merit_demerit_setting}', [HostelMeritDemeritSettingController::class, 'update'])->name('update')->permission('hostel-merit-demerit-setting-update');
                Route::delete('{hostel_merit_demerit_setting}', [HostelMeritDemeritSettingController::class, 'destroy'])->name('destroy')->permission('hostel-merit-demerit-setting-delete');
            });

            //HostelRewardPunishmentSetting routes
            Route::prefix('hostel-reward-punishment-settings')->name('hostel-reward-punishment-settings.')->group(function () {
                Route::get('/', [HostelRewardPunishmentSettingController::class, 'index'])->name('index')->permission('hostel-reward-punishment-setting-view');
                Route::post('/', [HostelRewardPunishmentSettingController::class, 'create'])->name('create')->permission('hostel-reward-punishment-setting-create');
                Route::get('{hostel_reward_punishment_setting}', [HostelRewardPunishmentSettingController::class, 'show'])->name('show')->permission('hostel-reward-punishment-setting-view');
                Route::put('{hostel_reward_punishment_setting}', [HostelRewardPunishmentSettingController::class, 'update'])->name('update')->permission('hostel-reward-punishment-setting-update');
                Route::delete('{hostel_reward_punishment_setting}', [HostelRewardPunishmentSettingController::class, 'destroy'])->name('destroy')->permission('hostel-reward-punishment-setting-delete');
            });

            //HostelRewardPunishmentRecord routes
            Route::prefix('hostel-reward-punishment-records')->name('hostel-reward-punishment-records.')->group(function () {
                Route::get('/', [HostelRewardPunishmentRecordController::class, 'index'])->name('index')->permission('hostel-reward-punishment-record-view');
                Route::post('/', [HostelRewardPunishmentRecordController::class, 'create'])->name('create')->permission('hostel-reward-punishment-record-create');
                Route::patch('/', [HostelRewardPunishmentRecordController::class, 'update'])->name('update')->permission('hostel-reward-punishment-record-update');
                Route::get('{hostel_reward_punishment_record}', [HostelRewardPunishmentRecordController::class, 'show'])->name('show')->permission('hostel-reward-punishment-record-view');
                Route::delete('{hostel_reward_punishment_record}', [HostelRewardPunishmentRecordController::class, 'destroy'])->name('destroy')->permission('hostel-reward-punishment-record-delete');
            });

            // Hostel savings account routes
            Route::prefix('hostel-savings-account')->name('hostel-savings-account.')->group(function () {
                Route::get('/balance', [HostelSavingsAccountController::class, 'getBalance'])->name('get-balance')->permission('hostel-savings-account-view');
                Route::get('/transactions', [HostelSavingsAccountController::class, 'transactions'])->name('transactions')->permission('hostel-savings-account-view');
                Route::post('/deposit', [HostelSavingsAccountController::class, 'deposit'])->name('deposit')->permission('hostel-savings-account-deposit');
                Route::post('/withdraw', [HostelSavingsAccountController::class, 'withdraw'])->name('withdraw')->permission('hostel-savings-account-withdraw');
                Route::post('/billing-document/{billing_document}/void', [HostelSavingsAccountController::class, 'void'])->name('void')->permission('hostel-savings-account-void');
            });


            // Billing Routes (public)
            Route::prefix('billing-document')->name('billing-document.')->group(function () {
                Route::get('/', [BillingDocumentController::class, 'index'])->name('index')->permission('billing-document-view');
                Route::get('/{billing_document}', [BillingDocumentController::class, 'show'])->name('show')->permission('billing-document-view');
                Route::post('/{billing_document}/void', [BillingDocumentController::class, 'void'])->name('void')->permission('billing-document-void');
                Route::post('/{billing_document}/make-fpx-payment', [BillingDocumentController::class, 'makePayment'])->name('make-payment')->permission('billing-document-make-payment');
            });


            // Book routes
            Route::prefix('libraries')->name('libraries.')->group(function () {
                Route::prefix('books')->name('books.')->group(function () {
                    Route::get('/', [BookController::class, 'index'])->name('index')->permission('book-view');
                    Route::post('/', [BookController::class, 'create'])->name('create')->permission('book-create');
                    Route::get('{book}', [BookController::class, 'show'])->name('show')->permission('book-view');
                    Route::put('{book}', [BookController::class, 'update'])->name('update')->permission('book-update');
                    Route::post('{book}/recover', [BookController::class, 'recoverLostBook'])->name('recover-lost-book')->permission('book-update');
                    Route::delete('{book}', [BookController::class, 'destroy'])->name('destroy')->permission('book-delete');
                });

                Route::prefix('members')->name('members.')->group(function () {
                    Route::get('/', [LibraryMemberController::class, 'index'])->name('index')->permission('library-member-view');
                    Route::post('/', [LibraryMemberController::class, 'create'])->name('create')->permission('library-member-create');
                    Route::get('{library_member}', [LibraryMemberController::class, 'show'])->name('show')->permission('library-member-view');
                    Route::put('{library_member}', [LibraryMemberController::class, 'update'])->name('update')->permission('library-member-update');
                    Route::delete('{library_member}', [LibraryMemberController::class, 'destroy'])->name('destroy')->permission('library-member-delete');
                });

                Route::prefix('book-loans')->name('book-loans.')->group(function () {
                    Route::get('/', [LibraryBookLoanController::class, 'index'])->name('index')->permission('book-loan-view');
                    Route::post('/', [LibraryBookLoanController::class, 'create'])->name('create')->permission('book-loan-create');
                    Route::post('return', [LibraryBookLoanController::class, 'return'])->name('return')->permission('book-loan-return');
                    Route::post('extend', [LibraryBookLoanController::class, 'extend'])->name('extend')->permission('book-loan-extend');
                });
            });

            Route::prefix('comprehensive-assessment')->name('comprehensive-assessment.')->group(function () {
                // Comprehensive Assessment Category routes
                Route::prefix('categories')->name('categories.')->group(function () {
                    Route::get('/', [ComprehensiveAssessmentCategoryController::class, 'index'])->name('index')->permission('comprehensive-assessment-category-view');
                    Route::post('/', [ComprehensiveAssessmentCategoryController::class, 'create'])->name('create')->permission('comprehensive-assessment-category-create');
                    Route::get('{assessment_category}', [ComprehensiveAssessmentCategoryController::class, 'show'])->name('show')->permission('comprehensive-assessment-category-view');
                    Route::put('{assessment_category}', [ComprehensiveAssessmentCategoryController::class, 'update'])->name('update')->permission('comprehensive-assessment-category-update');
                    Route::delete('{assessment_category}', [ComprehensiveAssessmentCategoryController::class, 'destroy'])->name('destroy')->permission('comprehensive-assessment-category-delete');
                });

                // Comprehensive Assessment Question routes
                Route::prefix('questions')->name('questions.')->group(function () {
                    Route::get('/', [ComprehensiveAssessmentQuestionController::class, 'index'])->name('index')->permission('comprehensive-assessment-question-view');
                    Route::post('/', [ComprehensiveAssessmentQuestionController::class, 'create'])->name('create')->permission('comprehensive-assessment-question-create');
                    Route::get('{assessment_question}', [ComprehensiveAssessmentQuestionController::class, 'show'])->name('show')->permission('comprehensive-assessment-question-view');
                    Route::put('{assessment_question}', [ComprehensiveAssessmentQuestionController::class, 'update'])->name('update')->permission('comprehensive-assessment-question-update');
                    Route::delete('{assessment_question}', [ComprehensiveAssessmentQuestionController::class, 'destroy'])->name('destroy')->permission('comprehensive-assessment-question-delete');
                });

                // Comprehensive Assessment Records
                Route::prefix('records')->name('records.')->group(function () {
                    Route::get('/by-student', [ComprehensiveAssessmentRecordController::class, 'indexByStudent'])->name('index-by-student')->permission('comprehensive-assessment-record-view|student-tab-comprehensive-assessment-record-view');
                    Route::get('/', [ComprehensiveAssessmentRecordController::class, 'index'])->name('index')->permission('comprehensive-assessment-record-view');
                    Route::post('/', [ComprehensiveAssessmentRecordController::class, 'update'])->name('update')->permission('comprehensive-assessment-record-update');
                });
            });

            //leadership-position-records
            Route::prefix('leadership-position-records')->name('leadership-position-records.')->group(function () {
                Route::get('/', [LeadershipPositionRecordController::class, 'index'])->name('index')->permission('leadership-position-record-view');
                Route::post('/bulk-create', [LeadershipPositionRecordController::class, 'bulkCreate'])->name('bulk-create')->permission('leadership-position-record-create');
            });

            // PosTerminalKey routes
            Route::prefix('pos-terminal-keys')->name('pos-terminal-keys.')->group(function () {
                Route::get('/', [PosTerminalKeyController::class, 'index'])->name('index')->permission('pos-terminal-key-view');
                Route::get('/{pos_terminal_key}', [PosTerminalKeyController::class, 'show'])->name('show')->permission('pos-terminal-key-view');
                Route::post('/', [PosTerminalKeyController::class, 'create'])->name('create')->permission('pos-terminal-key-create');
                Route::put('/{pos_terminal_key}/regenerate', [PosTerminalKeyController::class, 'regenerate'])->name('regenerate')->permission('pos-terminal-key-update');
                Route::put('/{pos_terminal_key}', [PosTerminalKeyController::class, 'update'])->name('update')->permission('pos-terminal-key-update');
                Route::delete('/{pos_terminal_key}', [PosTerminalKeyController::class, 'destroy'])->name('destroy')->permission('pos-terminal-key-delete');
            });

            // Terminal routes
            Route::prefix('terminals')->name('terminals.')->group(function () {
                Route::get('/', [TerminalController::class, 'index'])->name('index')->permission('terminal-view');
                Route::get('/{terminal}', [TerminalController::class, 'show'])->name('show')->permission('terminal-view');
                Route::post('/', [TerminalController::class, 'create'])->name('create')->permission('terminal-create');
                Route::put('{terminal}', [TerminalController::class, 'update'])->name('update')->permission('terminal-update');
                Route::delete('{terminal}', [TerminalController::class, 'destroy'])->name('destroy')->permission('terminal-delete');
            });

            // Permission routes
            Route::prefix('permissions')->name('permissions.')->group(function () {
                Route::get('/', [PermissionController::class, 'index'])->name('index');
            });

            // Competition routes
            Route::prefix('competitions')->name('competitions.')->group(function () {
                Route::get('/', [CompetitionController::class, 'index'])->name('index')->permission('competition-view');
                Route::get('/{competition}', [CompetitionController::class, 'show'])->name('show')->permission('competition-view');
                Route::post('/', [CompetitionController::class, 'create'])->name('create')->permission('competition-create');
                Route::put('/{competition}', [CompetitionController::class, 'update'])->name('update')->permission('competition-update');
                Route::delete('/{competition}', [CompetitionController::class, 'destroy'])->name('destroy')->permission('competition-delete');
            });

            // Role routes
            Route::prefix('roles')->name('roles.')->group(function () {
                Route::get('/', [RoleController::class, 'index'])->name('index')->permission('role-view');
                Route::get('/{role}', [RoleController::class, 'show'])->name('show')->permission('role-view');
                Route::post('/', [RoleController::class, 'create'])->name('create')->permission('role-create');
                Route::put('/{role}', [RoleController::class, 'update'])->name('update')->permission('role-update');
                Route::delete('/{role}', [RoleController::class, 'destroy'])->name('destroy')->permission('role-delete');
            });

            // StudentSocietyPosition routes
            Route::prefix('student-society-positions')->name('student-society-positions.')->group(function () {
                Route::put('/', [StudentSocietyPositionController::class, 'assignPositionsToStudents'])->name('assign-positions-to-students')->permission('student-society-position-update');
            });

            // ISBN routes
            Route::prefix('isbn')->name('isbn.')->group(function () {
                Route::get('/{isbn}', [IsbnController::class, 'search'])->name('search')->permission('book-view');
            });

            // Report routes
            Route::prefix('reports')->name('reports.')->group(function () {
                Route::prefix('semester-classes')->name('semester-classes.')->group(function () {
                    Route::get('student-contacts', [SemesterClassReportController::class, 'reportStudentContacts'])->name('student-contacts')->permission('semester-class-student-contacts-report');
                    Route::get('student-details', [SemesterClassReportController::class, 'reportStudentDetails'])->name('student-details')->permission('semester-class-student-details-report');
                    Route::get('homeroom-teachers', [SemesterClassReportController::class, 'reportHomeroomTeachers'])->name('homeroom-teachers')->permission('semester-class-homeroom-teachers-report');
                    Route::get('by-students-in-class', [SemesterClassReportController::class, 'reportByStudentsInSemesterClass'])->name('by-students-in-semester-class')->permission('semester-class-by-students-in-class-report');
                });

                Route::prefix('cocurriculum')->name('cocurriculum.')->group(function () {
                    Route::get('trainer-detail', [CocurriculumReportController::class, 'reportByTrainerDetail'])->name('trainer-detail')->permission('cocurriculum-trainer-detail-report');
                    Route::get('student-statistic-report-by-semester', [CocurriculumReportController::class, 'studentStatisticReportBySemester'])->name('student-statistic-report-by-semester')->permission('cocurriculum-student-statistic-by-semester-report');
                });

                Route::prefix('hostels')->name('hostels.')->group(function () {
                    Route::get('by-boarders-name-list', [HostelReportController::class, 'reportByBoardersNameList'])->name('by-boarders-name-list')->permission('hostel-by-boarders-name-list-report');
                    Route::get('by-available-bed', [HostelReportController::class, 'reportByAvailableBed'])->name('by-available-bed')->permission('hostel-by-available-bed-report');
                    Route::get('by-checkout-record', [HostelReportController::class, 'reportByCheckoutRecord'])->name('by-checkout-record')->permission('hostel-by-checkout-record-report');
                    Route::get('by-boarders-list-info', [HostelReportController::class, 'reportByBoardersListInfo'])->name('by-boarders-list-info')->permission('hostel-by-boarders-list-info-report');
                    Route::get('by-boarders-contact-info', [HostelReportController::class, 'reportByBoardersContactInfo'])->name('by-boarders-contact-info')->permission('hostel-by-boarders-contact-info-report');
                    Route::get('by-boarders-date-of-birth', [HostelReportController::class, 'reportByBoardersDateOfBirth'])->name('by-boarders-date-of-birth')->permission('hostel-by-boarders-date-of-birth-report');
                    Route::get('by-boarders-stayback', [HostelReportController::class, 'reportByBoardersStayback'])->name('by-boarders-stayback')->permission('hostel-by-boarders-stayback-report');
                    Route::get('by-employee-lodging', [HostelReportController::class, 'reportByEmployeeLodging'])->name('by-employee-lodging')->permission('hostel-by-employee-lodging-report');
                    Route::get('by-boarders-go-home-or-out', [HostelReportController::class, 'reportByBoardersGoHomeOrOut'])->name('by-boarders-go-home-or-out')->permission('hostel-by-boarders-go-home-or-out-report');
                    Route::get('by-change-room-record', [HostelReportController::class, 'reportByChangeRoomRecord'])->name('by-change-room-record')->permission('hostel-by-change-room-record-report');
                    Route::get('by-reward-punishment-block', [HostelReportController::class, 'reportByRewardPunishmentBlock'])->name('by-reward-punishment-block')->permission('hostel-report-reward-punishment-by-block');
                    Route::get('by-reward-punishment-student', [HostelReportController::class, 'reportByRewardPunishmentStudent'])->name('by-reward-punishment-student')->permission('hostel-report-reward-punishment-by-student');
                    Route::get('by-reward-punishment-room', [HostelReportController::class, 'reportByRewardPunishmentRoom'])->name('by-reward-punishment-room')->permission('hostel-report-reward-punishment-by-room');
                });

                // Library reports
                Route::prefix('libraries')->name('libraries.')->group(function () {
                    Route::get('book-loans', [LibraryBookLoanReportController::class, 'reportByBookLoan'])->name('book-loans')->permission('library-book-loans-report');
                    Route::get('top-borrowers', [LibraryBookLoanReportController::class, 'reportTopBorrower'])->name('top-borrowers')->permission('library-top-borrowers-report');
                    Route::get('top-borrowed-books', [LibraryBookLoanReportController::class, 'reportTopBorrowedBook'])->name('top-borrowed-books')->permission('library-top-borrowed-books-report');
                    Route::get('school-rate-borrow-books', [LibraryBookLoanReportController::class, 'reportSchoolRateBorrowBooks'])->name('school-rate-borrow-books')->permission('library-school-rate-borrow-books-report');
                    Route::get('book-borrow-records', [LibraryBookLoanReportController::class, 'reportBookBorrowRecord'])->name('book-borrow-records')->permission('library-book-borrow-records-report');
                });

                Route::prefix('cards')->name('cards.')->group(function () {
                    Route::get('by-cards-list', [CardReportController::class, 'reportByCard'])->name('by-cards-list')->permission('card-report');
                });

                Route::prefix('attendances')->name('attendances.')->group(function () {
                    Route::get('by-summary-list', [AttendanceReportController::class, 'reportByAttendanceSummary'])->name('by-summary-list')->permission('attendance-by-summary-list-report');
                    Route::get('class-attendance-report', [AttendanceReportController::class, 'classAttendanceReport'])->name('class-attendance-report')->permission('class-attendance-report');
                    Route::get('student-attendance', [AttendanceReportController::class, 'studentAttendanceReport'])->name('student-attendance-report')->permission('student-attendance-report');
                    Route::get('student-absent', [AttendanceReportController::class, 'studentAbsentReport'])->name('student-absent-report')->permission('student-absent-report');
                    Route::get('by-student-attendance-mark-deduction', [AttendanceReportController::class, 'reportByStudentAttendanceMarkDeduction'])->name('by-student-attendance-mark-deduction')->permission('attendance-by-student-attendance-mark-deduction-report');
                    Route::get('class-attendance-taking-status-report', [AttendanceReportController::class, 'classAttendanceTakingStatusReport'])->name('class-attendance-taking-status-report')->permission('class-attendance-taking-status-report');
                });

                Route::prefix('ecommerce')->name('ecommerce.')->group(function () {
                    Route::prefix('bookshops')->name('bookshops.')->group(function () {
                        Route::get('/orders', [EcommerceBookshopReportController::class, 'reportByOrders'])->name('orders')->permission('ecommerce-bookshops-orders-report');
                        Route::get('/classes', [EcommerceBookshopReportController::class, 'reportByClasses'])->name('classes')->permission('ecommerce-bookshops-classes-report');
                        Route::get('/students', [EcommerceBookshopReportController::class, 'reportByStudents'])->name('students')->permission('ecommerce-bookshops-students-report');
                        Route::get('/order-items', [EcommerceBookshopReportController::class, 'reportOrderItems'])->name('order-items')->permission('ecommerce-order-items-bookshops-report');
                    });

                    Route::prefix('canteens')->name('canteens.')->group(function () {
                        Route::get('/classes', [EcommerceCanteenReportController::class, 'reportByClasses'])->name('classes')->permission('ecommerce-canteens-classes-report');
                        Route::get('/classes/weekly', [EcommerceCanteenReportController::class, 'reportByClassesWeekly'])->name('classes-weekly')->permission('ecommerce-canteens-classes-weekly-report');
                        Route::get('/classes/date-range', [EcommerceCanteenReportController::class, 'reportByClassesDateRange'])->name('classes-date-range')->permission('ecommerce-canteens-classes-date-range-report');
                        Route::get('/merchants', [EcommerceCanteenReportController::class, 'reportByMerchants'])->name('merchants')->permission('ecommerce-canteens-merchants-report');
                        Route::get('/by-student', [EcommerceCanteenReportController::class, 'reportByStudent'])->name('by-student')->permission('ecommerce-canteens-by-student-report');
                        Route::get('/by-merchant-daily-sales', [EcommerceCanteenReportController::class, 'reportByMerchantDailySales'])->name('by-merchant-daily-sales')->permission('ecommerce-canteens-by-merchant-daily-sales-report');
                        Route::get('/by-daily-sales-group-by-merchant', [EcommerceCanteenReportController::class, 'reportByDailySalesGroupByMerchant'])->name('by-daily-sales-group-by-merchant')->permission('ecommerce-canteens-by-daily-sales-group-by-merchant-report');
                        Route::get('/by-daily-collection', [EcommerceCanteenReportController::class, 'reportByDailyCollection'])->name('by-daily-collection')->permission('ecommerce-canteens-by-daily-collection-report');
                    });
                });

                // Wallet reports
                Route::prefix('wallets')->name('wallets.')->group(function () {
                    Route::get('by-daily-wallet-transactions', [WalletReportController::class, 'reportByDailyWalletTransactions'])->name('by-daily-wallet-transactions')->permission('wallet-report');
                });

                // Wallet transaction reports
                Route::prefix('wallet-transactions')->name('wallet-transactions.')->group(function () {
                    Route::get('by-all-wallet-transactions', [WalletTransactionReportController::class, 'reportByAllWalletTransactions'])->name('by-all-wallet-transactions')->permission('wallet-report');
                });

                // Academy reports
                Route::prefix('academics')->name('academy.')->group(function () {
                    Route::get('transferred-student-list', [AcademyReportController::class, 'transferredStudentListByAdmissionYear'])->name('transferred-student-list')->permission('academy-transferred-student-list');
                    Route::get('student-analysis', [AcademyReportController::class, 'studentAnalysisReportBySemesterGroupByGrade'])->name('student-analysis')->permission('academy-student-analysis-report');
                });

                // Accounting reports
                Route::prefix('accounting')->name('accounting.')->group(function () {
                    Route::get('student-outstanding-balance-report', [AccountingReportController::class, 'studentOutstandingBalanceReportByClass'])->name('student-outstanding-balance-report')->permission('accounting-student-outstanding-balance-report');
                });

                // Billing Document reports
                Route::prefix('billing-documents')->name('billing-documents.')->group(function () {
                    Route::get('by-daily-collection', [BillingDocumentReportController::class, 'reportByDailyCollection'])->name('by-daily-collection')->permission('billing-document-by-daily-collection-report');
                });

                // Test Invoice Document Generation
                Route::prefix('print-invoice')->name('print-invoice.')->group(function () {
                    Route::get('/test', [DocumentPrintController::class, 'invoice'])->permission('invoice-report');
                    Route::get('/report-card', [DocumentPrintController::class, 'report_card']);
                });


                Route::prefix('contractor-attendances')->name('contractor-attendances.')->group(function () {
                    Route::get('daily-attendance-report', [ContractorAttendanceReportController::class, 'dailyAttendanceReport'])->name('daily-attendance-report')->permission('contractor-daily-attendance-report');
                });

                Route::prefix('student-conduct')->name('student-conduct.')->group(function () {
                    Route::get('student-conduct-report', [StudentConductReportController::class, 'studentConductReport'])->name('student-conduct-report')->permission('student-conduct-report');
                });

                Route::prefix('enrollment')->name('enrollment.')->group(function () {
                    Route::get('by-daily-collection', [EnrollmentReportController::class, 'reportByDailyCollection'])->name('by-daily-collection')->permission('enrollment-by-daily-collection-report');
                    Route::get('student-registration-report', [EnrollmentReportController::class, 'reportByStudentRegistration'])->name('student-registration-report')->permission('student-registration-report');
                });
            });

            // Exam mark entry routes
            Route::prefix('exam-results-data-entry')->name('exam-results-data-entry.')->group(function () {
                Route::get('/eligible-exams', [ExamResultsDataEntryController::class, 'getEligibleExams'])->name('getEligibleExams')->permission('exam-results-data-entry-view|exam-results-data-entry-view-all');
                Route::get('/eligible-subjects', [ExamResultsDataEntryController::class, 'getEligibleSubjects'])->name('getEligibleSubjects')->permission('exam-results-data-entry-view|exam-results-data-entry-view-all');
                Route::get('/eligible-classes', [ExamResultsDataEntryController::class, 'getEligibleClasses'])->name('getEligibleClasses')->permission('exam-results-data-entry-view|exam-results-data-entry-view-all');
                Route::get('/eligible-students-and-data', [ExamResultsDataEntryController::class, 'getEligibleStudentsAndScores'])->name('getEligibleStudentsAndScores')->permission('exam-results-data-entry-view|exam-results-data-entry-view-all');
                Route::post('/save', [ExamResultsDataEntryController::class, 'save'])->name('save')->permission('exam-results-data-entry-save');
                Route::post('/reopen', [ExamResultsDataEntryController::class, 'reopenPostedEntry'])->name('reopenPostedEntry')->permission('exam-results-data-entry-reopen');
            });

            // Exam mark exempted routes
            Route::prefix('exam-subject-exemption')->name('exam-subject-exemption.')->group(function () {
                Route::get('/eligible_subjects', [ExamSubjectExemptionController::class, 'getEligibleSubjectsForExemption'])->name('get-eligible-subjects-for-exemption')->permission('exam-exemption-save');
                Route::get('/eligible_classes', [ExamSubjectExemptionController::class, 'getEligibleClassesForExemption'])->name('get-eligible-classes-for-exemption')->permission('exam-exemption-save');
                Route::get('/student_exemption_status', [ExamSubjectExemptionController::class, 'getStudentExamExemptionStatus'])->name('get-student-exam-exemption-status')->permission('exam-exemption-save');
                Route::post('/set_exemption', [ExamSubjectExemptionController::class, 'setExamSubjectExemptionStatus'])->name('set-exam-subject-exemption-status')->permission('exam-exemption-save');
            });

            // Exam result posting routes
            Route::prefix('exam-results-posting')->name('exam-results-posting.')->group(function () {
                Route::get('/eligible-grading-frameworks', [ExamResultsPostingController::class, 'getEligibleMasterGradingFrameworks'])->name('getEligibleMasterGradingFrameworks')->permission('exam-posting-create-posting-session');
                Route::get('/eligible-output-codes', [ExamResultsPostingController::class, 'getEligibleReportCardOutputCodes'])->name('getEligibleReportCardOutputCodes')->permission('exam-posting-create-posting-session');
                Route::get('/exam-posting-status', [ExamResultsPostingController::class, 'getAllResultsPostingHeader'])->name('getAllResultsPostingHeader')->permission('exam-posting-create-posting-session');
                Route::post('/create-posting-session', [ExamResultsPostingController::class, 'createExamPostingSession'])->name('createExamPostingSession')->permission('exam-posting-create-posting-session');
                Route::post('/publish-report-card-by-header', [ExamResultsPostingController::class, 'publishStudentReportCardByHeader'])->name('publish-report-card-by-header')->permission('exam-posting-create-posting-session');
                Route::get('/pre-checks', [ExamResultsPostingController::class, 'examPostingPreChecks'])->name('examPostingPreChecks')->permission('exam-posting-create-posting-session');
                Route::post('/refresh-pre-checks', [ExamResultsPostingController::class, 'manualRefreshExamPostingPreChecks'])->name('manualRefreshExamPostingPreChecks')->permission('exam-posting-create-posting-session');
            });

            // Exam routes
            Route::prefix('exams')->name('exams.')->group(function () {
                Route::get('/', [ExamController::class, 'index'])->name('index')->permission('exam-view');
                Route::post('/', [ExamController::class, 'create'])->name('create')->permission('exam-create');
                Route::get('{exam}', [ExamController::class, 'show'])->name('show')->permission('exam-view');
                Route::put('{exam}', [ExamController::class, 'update'])->name('update')->permission('exam-update');
                Route::delete('{exam}', [ExamController::class, 'destroy'])->name('destroy')->permission('exam-delete');
            });

            Route::prefix('promotion-marks')->name('promotion-marks.')->group(function () {
                Route::get('/', [PromotionMarkController::class, 'index'])->name('index')->permission('promotion-mark-view');
                Route::put('/bulk-create-or-update', [PromotionMarkController::class, 'bulkCreateOrUpdate'])->name('bulk-create-or-update')->permission('promotion-mark-create');
            });

            // Grading Framework routes
            Route::prefix('grading-frameworks')->name('grading-frameworks.')->group(function () {
                Route::get('/', [GradingFrameworkController::class, 'index'])->name('index')->permission('grading-framework-view');
                Route::post('/', [GradingFrameworkController::class, 'create'])->name('create')->permission('grading-framework-create');
                Route::get('{grading_framework}', [GradingFrameworkController::class, 'show'])->name('show')->permission('grading-framework-view');
                Route::put('{grading_framework}', [GradingFrameworkController::class, 'update'])->name('update')->permission('grading-framework-update');
                Route::post('/apply', [GradingFrameworkController::class, 'applyGradingFramework'])->name('apply')->permission('grading-framework-apply');
                Route::post('/bulkApply', [GradingFrameworkController::class, 'bulkApplyGradingFramework'])->name('bulkApply')->permission('grading-framework-apply');
                Route::get('/{grading_framework}/fixed-formula', [GradingFrameworkController::class, 'getFixedFormulas'])->name('getFixedFormulas')->permission('grading-framework-update');
                Route::get('{grading_framework}/result-source-formula', [GradingFrameworkController::class, 'getResultSourceFormulas'])->name('getResultSourceFormulas')->permission('grading-framework-update');
                Route::get('{grading_framework}/output-formula', [GradingFrameworkController::class, 'getOutputFormulas'])->name('getOutputFormulas')->permission('grading-framework-update');
            });

            // Results Posting Header routes
            Route::prefix('results-posting-header')->name('results-posting-header.')->group(function () {
                Route::get('/', [ResultsPostingHeaderController::class, 'index'])->name('index')->permission('results-posting-header-view');
                Route::get('/results-posting-header-by-grade', [ResultsPostingHeaderController::class, 'getResultPostingHeaderByGrade'])->name('getResultPostingHeaderByGrade')->permission('results-posting-header-view');
                Route::get('/results-posting-header-by-student', [ResultsPostingHeaderController::class, 'getResultPostingHeaderByStudent'])->name('getResultPostingHeaderByStudent')->permission('results-posting-header-view');
            });

            // Student Report Card routes
            Route::prefix('student-report-card')->name('student-report-card.')->group(function () {
                Route::get('/', [StudentReportCardController::class, 'index'])->name('index')->permission('student-report-card-view');
            });

            // Substitute Record routes
            Route::prefix('substitute-record')->name('substitute-record.')->group(function () {
                Route::get('/', [SubstituteRecordController::class, 'index'])->name('index')->permission('substitute-record-view');
                Route::get('/{requestor}', [SubstituteRecordController::class, 'getRequestorSubstituteTimeslotsByDateRange'])->name('getRequestorSubstituteTimeslotsByDateRange')->permission('substitute-record-bulk-create-update-view');
                Route::post('/{requestor}', [SubstituteRecordController::class, 'bulkCreateOrUpdate'])->name('bulkCreateOrUpdate')->permission('substitute-record-bulk-create-update');
                Route::put('{substitute_record}', [SubstituteRecordController::class, 'update'])->name('update')->permission('substitute-record-update');
                Route::delete('{substitute_record}', [SubstituteRecordController::class, 'destroy'])->name('destroy')->permission('substitute-record-delete');
            });

            // Leave Application routes
            Route::prefix('leave-applications')->name('leave-applications.')->group(function () {
                Route::get('/', [LeaveApplicationController::class, 'index'])->name('index')->permission('leave-application-view');
                Route::post('/', [LeaveApplicationController::class, 'create'])->name('create')->permission('leave-application-create');
                Route::post('/status-batch', [LeaveApplicationController::class, 'updateStatusBatch'])->name('updateStatusBatch')->permission('leave-application-update');
                Route::get('{leave_application}', [LeaveApplicationController::class, 'show'])->name('show')->permission('leave-application-view');
                Route::put('{leave_application}', [LeaveApplicationController::class, 'update'])->name('update')->permission('leave-application-update');
                Route::put('{leave_application}/status', [LeaveApplicationController::class, 'updateStatus'])->name('updateStatus')->permission('leave-application-update');
                Route::delete('{leave_application}', [LeaveApplicationController::class, 'destroy'])->name('destroy')->permission('leave-application-delete');
                Route::post('/preview-status-change', [LeaveApplicationController::class, 'previewLeaveApplicationStatusChange'])->name('preview-status-change')->permission('leave-application-update');
                Route::post('/leave-application-history', [LeaveApplicationController::class, 'getLeaveApplicationHistory'])->name('getLeaveApplicationHistory')->permission('leave-application-view');
            });

            // Leave Application Type routes
            Route::prefix('leave-application-types')->name('leave-application-types.')->group(function () {
                Route::get('/', [LeaveApplicationTypeController::class, 'index'])->name('index')->permission('leave-application-type-view');
                Route::post('/', [LeaveApplicationTypeController::class, 'create'])->name('create')->permission('leave-application-type-create');
                Route::get('{leave_application_type}', [LeaveApplicationTypeController::class, 'show'])->name('show')->permission('leave-application-type-view');
                Route::put('{leave_application_type}', [LeaveApplicationTypeController::class, 'update'])->name('update')->permission('leave-application-type-update');
                Route::delete('{leave_application_type}', [LeaveApplicationTypeController::class, 'destroy'])->name('destroy')->permission('leave-application-type-delete');
            });

            // School attendance period override routes
            Route::prefix('school-attendance-period-override')->name('school-attendance-period-override.')->group(function () {
                Route::get('/', [SchoolAttendancePeriodOverrideController::class, 'index'])->name('index')->permission('school-attendance-period-override-view');
                Route::post('/', [SchoolAttendancePeriodOverrideController::class, 'create'])->name('create')->permission('school-attendance-period-override-create');
                Route::get('{period_override}', [SchoolAttendancePeriodOverrideController::class, 'show'])->name('show')->permission('school-attendance-period-override-view');
                Route::put('{period_override}', [SchoolAttendancePeriodOverrideController::class, 'update'])->name('update')->permission('school-attendance-period-override-update');
                Route::delete('{period_override}', [SchoolAttendancePeriodOverrideController::class, 'destroy'])->name('destroy')->permission('school-attendance-period-override-delete');
            });

            //Attendances routes
            Route::prefix('attendances')->name('attendances.')->group(function () {
                Route::get('/', [AttendanceController::class, 'index'])->name('index')->permission('attendance-view');
                Route::get('{attendance}', [AttendanceController::class, 'show'])->name('show')->permission('attendance-view');
                Route::delete('{attendance}', [AttendanceController::class, 'destroy'])->name('destroy')->permission('attendance-delete');
                Route::post('/trigger-attendance-posting', [AttendanceController::class, 'triggerAttendancePosting'])->name('trigger-attendance-posting')->permission('trigger-attendance-posting');
            });

            // Class attendance taking (attendance periods) routes
            Route::prefix('class-attendance-taking')->name('class-attendance-taking.')->group(function () {
                Route::get('/get-period-by-timeslot-teacher', [AttendanceController::class, 'getPeriodsByTimeslotTeacher'])->name('get-period-by-timeslot-teacher')->permission('class-attendance-taking-view');
                Route::get('/get-class-attendance-by-timeslot', [AttendanceController::class, 'getClassAttendanceByTimeslot'])->name('get-class-attendance-by-timeslot')->permission('class-attendance-taking-view');
                Route::post('bulk-update-class-attendance', [AttendanceController::class, 'bulkUpdateClassAttendance'])->name('bulk-update-class-attendance')->permission('class-attendance-taking-bulk-update');
            });

            // student timetable routes (mobile app)
            Route::prefix('student-timetable')->name('student-timetable.')->group(function () {
                Route::get('/', [StudentTimetableController::class, 'studentTimetable'])->name('index')->permission('student-personal-timetable-view');
            });

            Route::prefix('employee-timetables')->name('employee-timetables.')->group(function () {
                Route::get('/{employee}', [EmployeeTimetableController::class, 'getEmployeeTimetable'])->name('get-employee-timetable')->permission('employee-timetable-view');
            });

            //Attendance Input routes
            Route::prefix('attendance-input')->name('attendance-input.')->group(function () {
                Route::get('/students', [AttendanceInputController::class, 'indexByStudent'])->name('student-attendance-input-view')->permission('student-attendance-input-view');
                Route::get('/employees', [AttendanceInputController::class, 'indexByEmployee'])->name('employee-attendance-input-view')->permission('employee-attendance-input-view');
                Route::get('/contractors', [AttendanceInputController::class, 'indexByContractor'])->name('contractor-attendance-input-view')->permission('contractor-attendance-input-view');
                Route::post('/', [AttendanceInputController::class, 'create'])->name('create')->permission('attendance-input-create');
                Route::get('{attendance_input}', [AttendanceInputController::class, 'show'])->name('show');
                Route::put('{attendance_input}', [AttendanceInputController::class, 'update'])->name('update')->permission('attendance-input-update');
                Route::delete('{attendance_input}', [AttendanceInputController::class, 'destroy'])->name('destroy')->permission('attendance-input-delete');
            });

            // Calendar routes
            Route::prefix('calendars')->name('calendars.')->group(function () {
                Route::get('/', [CalendarController::class, 'index'])->name('index')->permission('calendar-view');
                Route::post('/', [CalendarController::class, 'create'])->name('create')->permission('calendar-create');
                Route::get('{calendar}', [CalendarController::class, 'show'])->name('show')->permission('calendar-view');
                Route::put('{calendar}', [CalendarController::class, 'update'])->name('update')->permission('calendar-update');
                Route::delete('{calendar}', [CalendarController::class, 'destroy'])->name('destroy')->permission('calendar-delete');
            });

            // Calendar Setting routes
            Route::prefix('calendar-settings')->name('calendar-settings.')->group(function () {
                Route::get('/', [CalendarSettingController::class, 'index'])->name('index')->permission('calendar-setting-view');
                Route::post('/edit', [CalendarSettingController::class, 'edit'])->name('edit')->permission('calendar-setting-edit');
                Route::get('{calendar_setting}', [CalendarSettingController::class, 'show'])->name('show')->permission('calendar-setting-view');
                Route::delete('{calendar_setting}', [CalendarSettingController::class, 'destroy'])->name('destroy')->permission('calendar-setting-delete');
            });

            // Calendar target routes
            Route::post('calendars/{calendar}/calendar-targets', [CalendarTargetController::class, 'bulkUpdate'])->name('calendar-targets.bulk-update')->permission('calendar-target-update');
            Route::prefix('calendar-targets')->name('calendar-targets.')->group(function () {
                Route::get('/', [CalendarTargetController::class, 'index'])->name('index')->permission('calendar-target-view');
                Route::get('{calendar_target}', [CalendarTargetController::class, 'show'])->name('show')->permission('calendar-target-view');
            });

            // Timeslot override routes
            Route::prefix('timeslot-override')->name('timeslot-override.')->group(function () {
                Route::get('/', [TimeslotOverrideController::class, 'index'])->name('index')->permission('timeslot-override-view');
                Route::post('/', [TimeslotOverrideController::class, 'bulkCreate'])->name('bulk-create')->permission('timeslot-override-create');
                Route::post('/batch-delete', [TimeslotOverrideController::class, 'batchDelete'])->name('batch-delete')->permission('timeslot-override-delete');
                Route::get('{timeslot_override}', [TimeslotOverrideController::class, 'show'])->name('show')->permission('timeslot-override-view');
                Route::delete('{timeslot_override}', [TimeslotOverrideController::class, 'destroy'])->name('destroy')->permission('timeslot-override-delete');
            });
        });
    });

    // POS Terminal routes
    Route::prefix('wallets')->name('wallets.')->middleware(['wallet_third_party', 'inbound.log', 'throttle:3rd-party'])->group(function () {
        Route::get('balance', [WalletController::class, 'getBalance'])->name('get-balance');
        Route::get('charge', [WalletTransactionController::class, 'getChargeStatus'])->name('get-charge-status');
        Route::post('charge', [WalletTransactionController::class, 'charge'])->name('charge');
    });
});
