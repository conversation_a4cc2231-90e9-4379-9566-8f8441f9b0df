@extends('reports.layout')

@section('content')
    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>考生编号 / Exam Slip</th>
                <th>Student Name</th>
                <th>身份证号码 / IC Number</th>
                <th>Passport Number</th>
                <th>宿舍 / Hostel  (TRUE, FALSE)</th>
                <th>总平均 / Total Average</th>
                @foreach ($subject_lists as $code => $s)
                    <th>{{ $s->getTranslation('name', 'zh') }} / {{ $s->getTranslation('name', 'en') }}</th>
                @endforeach
                <th>Status (APPROVED, REJECTED, SHORLISTED)</th>
            </tr>
        </thead>

        <tbody>
            @foreach ($data as $row)
            <tr>
                <td>{{ $row['number'] }}</td>
                <td>{{ $row['exam_slip_number'] }}</td>
                <td>{{ $row['student_name_en'] }}</td>
                <td>{{ $row['nric'] }}</td>
                <td>{{ $row['passport_number'] }}</td>
                <td>{{ $row['hostel'] }}</td>
                <td style="text-align: left;">{{ number_format($row['total_average'], 2) }}</td>
                @foreach ($subject_lists as $code => $subject)
                    <td style="text-align: left;">{{ number_format($row[$code], 2) }}</td>
                @endforeach
                <td>{{ $row['status'] }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
@endsection
